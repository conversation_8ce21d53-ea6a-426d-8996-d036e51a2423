import React from 'react';
import { Box, Typography, Grid, Tooltip, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';

interface HeatMapData {
  x: string | number;
  y: string | number;
  value: number;
  label?: string;
  category?: string;
}

interface HeatMapProps {
  data: HeatMapData[];
  title?: string;
  subtitle?: string;
  width?: number;
  height?: number;
  cellSize?: number;
  showValues?: boolean;
  colorScheme?: 'traffic' | 'severity' | 'custom';
  customColors?: string[];
  minValue?: number;
  maxValue?: number;
  xLabels?: string[];
  yLabels?: string[];
}

const HeatMapCell = styled(Box)<{ 
  intensity: number; 
  colorscheme: string;
  customcolors?: string[];
}>(({ theme, intensity, colorscheme, customcolors }) => {
  const getBackgroundColor = () => {
    if (customcolors && customcolors.length >= 3) {
      // Interpolación entre colores personalizados
      if (intensity <= 0.33) {
        return customcolors[0];
      } else if (intensity <= 0.66) {
        return customcolors[1];
      } else {
        return customcolors[2];
      }
    }

    switch (colorscheme) {
      case 'traffic':
        // Verde -> Amarillo -> Rojo (semáforo)
        if (intensity <= 0.33) {
          return '#63B4A9'; // Verde menta
        } else if (intensity <= 0.66) {
          return '#DDA15E'; // Ocre/Amarillo
        } else {
          return '#F28A7C'; // Coral/Rojo
        }
      case 'severity':
        // Azul claro -> Azul oscuro (intensidad)
        const alpha = 0.2 + (intensity * 0.8);
        return `rgba(90, 146, 200, ${alpha})`;
      default:
        // Esquema por defecto (azul)
        const defaultAlpha = 0.2 + (intensity * 0.8);
        return `rgba(90, 146, 200, ${defaultAlpha})`;
    }
  };

  const getTextColor = () => {
    if (intensity > 0.6) {
      return '#ffffff';
    }
    return theme.palette.text.primary;
  };

  return {
    backgroundColor: getBackgroundColor(),
    color: getTextColor(),
    transition: 'all 0.3s ease-in-out',
    cursor: 'pointer',
    '&:hover': {
      transform: 'scale(1.05)',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      zIndex: 1,
    },
  };
});

export const HeatMap: React.FC<HeatMapProps> = ({
  data,
  title,
  subtitle,
  cellSize = 60,
  showValues = true,
  colorScheme = 'traffic',
  customColors,
  minValue,
  maxValue,
  xLabels,
  yLabels,
}) => {
  // Calcular valores mínimo y máximo si no se proporcionan
  const calculatedMinValue = minValue ?? Math.min(...data.map(d => d.value));
  const calculatedMaxValue = maxValue ?? Math.max(...data.map(d => d.value));
  const range = calculatedMaxValue - calculatedMinValue;

  // Normalizar valores entre 0 y 1
  const normalizeValue = (value: number) => {
    if (range === 0) return 0;
    return (value - calculatedMinValue) / range;
  };

  // Obtener etiquetas únicas para ejes
  const uniqueXLabels = xLabels || [...new Set(data.map(d => d.x))].sort();
  const uniqueYLabels = yLabels || [...new Set(data.map(d => d.y))].sort();

  // Crear matriz de datos
  const dataMatrix = uniqueYLabels.map(y => 
    uniqueXLabels.map(x => {
      const item = data.find(d => d.x === x && d.y === y);
      return item || { x, y, value: 0 };
    })
  );

  // Leyenda de colores
  const ColorLegend = () => {
    const legendItems = [
      { label: 'Bajo', intensity: 0.2 },
      { label: 'Medio', intensity: 0.5 },
      { label: 'Alto', intensity: 0.8 },
    ];

    return (
      <Box display="flex" alignItems="center" gap={2} mt={2} justifyContent="center">
        <Typography variant="caption" color="text.secondary">
          Intensidad:
        </Typography>
        {legendItems.map((item, index) => (
          <Box key={index} display="flex" alignItems="center" gap={0.5}>
            <HeatMapCell
              intensity={item.intensity}
              colorscheme={colorScheme}
              customcolors={customColors}
              sx={{
                width: 16,
                height: 16,
                borderRadius: 1,
                border: '1px solid rgba(0, 0, 0, 0.1)',
              }}
            />
            <Typography variant="caption" color="text.secondary">
              {item.label}
            </Typography>
          </Box>
        ))}
      </Box>
    );
  };

  return (
    <Box>
      {(title || subtitle) && (
        <Box mb={3} textAlign="center">
          {title && (
            <Typography variant="h6" component="h3" gutterBottom>
              {title}
            </Typography>
          )}
          {subtitle && (
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
      )}

      <Box display="flex" flexDirection="column" alignItems="center">
        {/* Mapa de calor */}
        <Box>
          {/* Etiquetas del eje X */}
          <Box display="flex" mb={1}>
            <Box width={cellSize} /> {/* Espacio para etiquetas Y */}
            {uniqueXLabels.map((label, index) => (
              <Box
                key={index}
                width={cellSize}
                display="flex"
                justifyContent="center"
                alignItems="center"
              >
                <Typography 
                  variant="caption" 
                  color="text.secondary"
                  sx={{ 
                    transform: 'rotate(-45deg)',
                    fontSize: '0.7rem',
                    fontWeight: 500,
                  }}
                >
                  {label}
                </Typography>
              </Box>
            ))}
          </Box>

          {/* Filas del mapa */}
          {dataMatrix.map((row, rowIndex) => (
            <Box key={rowIndex} display="flex" alignItems="center">
              {/* Etiqueta del eje Y */}
              <Box
                width={cellSize}
                height={cellSize}
                display="flex"
                alignItems="center"
                justifyContent="center"
                pr={1}
              >
                <Typography 
                  variant="caption" 
                  color="text.secondary"
                  sx={{ fontSize: '0.7rem', fontWeight: 500 }}
                >
                  {uniqueYLabels[rowIndex]}
                </Typography>
              </Box>

              {/* Celdas de datos */}
              {row.map((cell, cellIndex) => (
                <Tooltip
                  key={cellIndex}
                  title={
                    <Box>
                      <Typography variant="subtitle2">
                        {cell.label || `${cell.x} - ${cell.y}`}
                      </Typography>
                      <Typography variant="body2">
                        Valor: {cell.value}
                      </Typography>
                      {cell.category && (
                        <Typography variant="caption">
                          Categoría: {cell.category}
                        </Typography>
                      )}
                    </Box>
                  }
                  arrow
                >
                  <HeatMapCell
                    intensity={normalizeValue(cell.value)}
                    colorscheme={colorScheme}
                    customcolors={customColors}
                    sx={{
                      width: cellSize,
                      height: cellSize,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      borderRadius: 1,
                      margin: '1px',
                    }}
                  >
                    {showValues && (
                      <Typography 
                        variant="caption" 
                        sx={{ 
                          fontWeight: 600,
                          fontSize: '0.7rem',
                        }}
                      >
                        {cell.value}
                      </Typography>
                    )}
                  </HeatMapCell>
                </Tooltip>
              ))}
            </Box>
          ))}
        </Box>

        {/* Leyenda */}
        <ColorLegend />
      </Box>

      {/* Información adicional */}
      <Box mt={2}>
        <Typography variant="caption" color="text.secondary" display="block" textAlign="center">
          Mapa de calor - Los colores indican la intensidad o severidad de los valores
        </Typography>
      </Box>
    </Box>
  );
};

export default HeatMap;
