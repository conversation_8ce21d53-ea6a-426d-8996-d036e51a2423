import React, { useState, useCallback } from 'react';
import { DataGridErrorBoundary } from '../ErrorBoundary/ErrorBoundary';
import {
  Box,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  Typography,
  Alert,
  Tooltip,
  Avatar,
  Paper,
  Skeleton,
} from '@mui/material';
import { UserFiltersComponent, useUserFilters } from './UserFilters';
import { PaginationInfo } from './PaginationInfo';
import { SortingInfo } from './SortingInfo';
import { InlineRoleEditor } from './InlineRoleEditor';
import { CreateUserModal, CreateUserData } from './CreateUserModal';
import {
  DataGrid,
  GridColDef,
  GridActionsCellItem,
  GridRowParams,
  GridToolbar,
  GridRenderCellParams,
  GridToolbarContainer,
  GridToolbarFilterButton,
  GridToolbarExport,
  GridToolbarColumnsButton,
  GridToolbarDensitySelector,
} from '@mui/x-data-grid';
import {
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  AdminPanelSettings as AdminIcon,
  Psychology as PsychologyIcon,
  PersonOutline as PatientIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  PersonAdd as AddUserIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { UserProfile, UserRole } from '../../services/userManagement';
import { useNotifier } from '../../hooks/useNotifier';
import { DataGridSkeleton } from '../Common/TableSkeleton';
import { useDataGridState } from '../../hooks/useDataGridState';

interface ProfessionalUserTableProps {
  users: UserProfile[];
  loading: boolean;
  onUpdateUserRole: (userId: string, role: UserRole) => Promise<void>;
  onUpdateUserStatus: (userId: string, isActive: boolean) => Promise<void>;
  onDeleteUser: (userId: string) => Promise<void>;
  onRefresh: () => void;
  selectedUsers?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
}

const roleConfig = {
  administrador: {
    label: 'Administrador',
    color: 'error' as const,
    icon: <AdminIcon fontSize="small" />,
    bgColor: '#ffebee',
  },
  psicologo: {
    label: 'Psicólogo',
    color: 'primary' as const,
    icon: <PsychologyIcon fontSize="small" />,
    bgColor: '#e3f2fd',
  },
  paciente: {
    label: 'Paciente',
    color: 'success' as const,
    icon: <PatientIcon fontSize="small" />,
    bgColor: '#e8f5e8',
  },
};

// Toolbar personalizada
const CustomToolbar = ({
  onRefresh,
  onCreateUser,
}: {
  onRefresh: () => void;
  onCreateUser: () => void;
}) => {
  return (
    <GridToolbarContainer sx={{ p: 2, justifyContent: 'space-between' }}>
      <Box sx={{ display: 'flex', gap: 1 }}>
        <GridToolbarFilterButton />
        <GridToolbarColumnsButton />
        <GridToolbarDensitySelector />
        <GridToolbarExport />
      </Box>
      <Box sx={{ display: 'flex', gap: 1 }}>
        <Button
          variant="contained"
          startIcon={<AddUserIcon />}
          onClick={onCreateUser}
          size="small"
          sx={{ backgroundColor: '#011129' }}
        >
          Crear Usuario
        </Button>
        <Button
          size="small"
          startIcon={<RefreshIcon />}
          onClick={onRefresh}
          variant="outlined"
        >
          Actualizar
        </Button>
      </Box>
    </GridToolbarContainer>
  );
};

export const ProfessionalUserTable: React.FC<ProfessionalUserTableProps> = ({
  users = [], // Default to empty array
  loading,
  onUpdateUserRole,
  onUpdateUserStatus,
  onDeleteUser,
  onRefresh,
  selectedUsers = [],
  onSelectionChange,
}) => {
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [newRole, setNewRole] = useState<UserRole>('paciente');
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [createUserModalOpen, setCreateUserModalOpen] = useState(false);
  const { showNotification } = useNotifier();

  // Hook para filtros de usuarios
  const {
    filters,
    setFilters,
    filteredUsers,
    totalUsers,
    filteredCount,
  } = useUserFilters(users || []);

  // Estado para paginación con inicialización segura
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 20,
  });

  // Estado para ordenamiento
  const [sortModel, setSortModel] = useState([
    { field: 'created_at', sort: 'desc' as const }
  ]);

  // Validaciones de seguridad para props
  const safeUsers = users || [];
  const safeSelectedUsers = selectedUsers || [];
  const safeFilteredUsers = filteredUsers || [];

  // Debug logging para identificar problemas
  console.log('🔍 ProfessionalUserTable Debug:', {
    usersLength: safeUsers.length,
    filteredUsersLength: safeFilteredUsers.length,
    selectedUsersLength: safeSelectedUsers.length,
    paginationModel,
    loading
  });

  // Verificar si hay filtros activos
  const hasActiveFilters =
    filters.searchTerm !== '' ||
    filters.roleFilter !== 'all' ||
    filters.statusFilter !== 'all';

  // Función para limpiar ordenamiento
  const handleClearSort = () => {
    setSortModel([]);
  };

  // Funciones para creación de usuarios
  const handleOpenCreateModal = () => {
    setCreateUserModalOpen(true);
  };

  const handleCreateUser = async (userData: CreateUserData) => {
    try {
      // Aquí se implementaría la lógica de creación
      // Por ahora solo mostramos una notificación
      showNotification(`Usuario ${userData.full_name} creado correctamente`, 'success');
      onRefresh(); // Actualizar la lista
    } catch (error) {
      showNotification('Error al crear el usuario', 'error');
      throw error;
    }
  };

  const handleEditUser = useCallback((user: UserProfile) => {
    setSelectedUser(user);
    setNewRole(user.role);
    setEditDialogOpen(true);
  }, []);

  const handleUpdateRole = async () => {
    if (!selectedUser) return;

    try {
      setActionLoading('update');
      await onUpdateUserRole(selectedUser.id, newRole);
      showNotification('Rol actualizado correctamente', 'success');
      setEditDialogOpen(false);
    } catch (error) {
      showNotification('Error al actualizar el rol', 'error');
    } finally {
      setActionLoading(null);
    }
  };

  // Función para el editor en línea
  const handleInlineRoleUpdate = async (userId: string, newRole: UserRole) => {
    try {
      setActionLoading(userId);
      await onUpdateUserRole(userId, newRole);
      showNotification('Rol actualizado correctamente', 'success');
    } catch (error) {
      showNotification('Error al actualizar el rol', 'error');
      throw error; // Re-throw para que el componente pueda manejar el error
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (
      !window.confirm('¿Estás seguro de que quieres eliminar este usuario?')
    ) {
      return;
    }

    try {
      setActionLoading(userId);
      await onDeleteUser(userId);
      showNotification('Usuario eliminado correctamente', 'success');
    } catch (error) {
      showNotification('Error al eliminar el usuario', 'error');
    } finally {
      setActionLoading(null);
    }
  };

  const handleToggleStatus = async (user: UserProfile) => {
    const newStatus = !user.is_active;
    const action = newStatus ? 'activar' : 'desactivar';

    if (
      !window.confirm(`¿Estás seguro de que quieres ${action} este usuario?`)
    ) {
      return;
    }

    try {
      setActionLoading(user.id);
      await onUpdateUserStatus(user.id, newStatus);
      showNotification(
        `Usuario ${newStatus ? 'activado' : 'desactivado'} correctamente`,
        'success'
      );
      onRefresh();
    } catch (error) {
      showNotification(`Error al ${action} el usuario`, 'error');
    } finally {
      setActionLoading(null);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((word) => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const columns: GridColDef[] = [
    {
      field: 'avatar',
      headerName: '',
      width: 60,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams) => {
        // Programación defensiva: verificar que params y params.row existan
        if (!params || !params.row) {
          return <Avatar sx={{ width: 32, height: 32, bgcolor: '#f5f5f5' }}>?</Avatar>;
        }

        const role = params.row.role || 'paciente';
        const name = params.row.full_name || params.row.email || 'N/A';

        return (
          <Avatar
            sx={{
              width: 32,
              height: 32,
              bgcolor: roleConfig[role]?.bgColor || '#f5f5f5',
              color: '#011129',
              fontSize: '0.75rem',
              fontWeight: 600,
            }}
          >
            {getInitials(name)}
          </Avatar>
        );
      },
    },
    {
      field: 'full_name',
      headerName: 'Nombre',
      flex: 1,
      minWidth: 180,
      sortable: true,
      sortComparator: (v1: string, v2: string) => {
        const name1 = v1 || '';
        const name2 = v2 || '';
        return name1.localeCompare(name2, 'es', { sensitivity: 'base' });
      },
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" sx={{ fontWeight: 600 }}>
          {params.value || 'Sin nombre'}
        </Typography>
      ),
    },
    {
      field: 'email',
      headerName: 'Email',
      flex: 1,
      minWidth: 200,
      sortable: true,
      sortComparator: (v1: string, v2: string) => {
        return v1.localeCompare(v2, 'es', { sensitivity: 'base' });
      },
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" color="text.secondary">
          {params.value || 'Sin email'}
        </Typography>
      ),
    },
    {
      field: 'role',
      headerName: 'Rol',
      width: 200,
      sortable: true,
      sortComparator: (v1: string, v2: string) => {
        // Orden personalizado: administrador > psicologo > paciente
        const roleOrder = { 'administrador': 3, 'psicologo': 2, 'paciente': 1 };
        const order1 = roleOrder[v1 as keyof typeof roleOrder] || 0;
        const order2 = roleOrder[v2 as keyof typeof roleOrder] || 0;
        return order2 - order1; // Orden descendente
      },
      renderCell: (params: GridRenderCellParams) => {
        // Programación defensiva: verificar que params y params.row existan
        if (!params || !params.row) {
          return <Typography variant="body2">Error</Typography>;
        }

        return (
          <InlineRoleEditor
            currentRole={(params.value as UserRole) || 'paciente'}
            userId={params.row.id || ''}
            userName={params.row.full_name || params.row.email || 'N/A'}
            onRoleChange={handleInlineRoleUpdate}
            disabled={actionLoading === params.row.id}
          />
        );
      },
    },
    {
      field: 'is_active',
      headerName: 'Estado',
      width: 120,
      sortable: true,
      sortComparator: (v1: boolean, v2: boolean) => {
        // Activos primero
        return v2 === v1 ? 0 : v2 ? 1 : -1;
      },
      renderCell: (params: GridRenderCellParams) => {
        // Programación defensiva: verificar que params exista
        if (!params) {
          return <Chip label="Error" color="error" size="small" variant="outlined" />;
        }

        const isActive = params.value === true || params.value === 'true';
        return (
          <Chip
            icon={isActive ? <CheckCircleIcon /> : <BlockIcon />}
            label={isActive ? 'Activo' : 'Inactivo'}
            color={isActive ? 'success' : 'error'}
            size="small"
            variant={isActive ? 'outlined' : 'filled'}
            sx={{
              fontWeight: 'bold',
              ...(isActive ? {} : {
                backgroundColor: 'error.main',
                color: 'error.contrastText',
                '& .MuiChip-icon': {
                  color: 'error.contrastText',
                },
              }),
            }}
          />
        );
      },
    },
    {
      field: 'created_at',
      headerName: 'Fecha de Registro',
      width: 150,
      sortable: true,
      type: 'dateTime',
      valueGetter: (params: any) => {
        return params?.value || null;
      },
      valueFormatter: (params: any) => {
        // Protección robusta contra params null y valores null/undefined
        if (!params || !params.value || params.value === null || params.value === undefined) {
          return 'No disponible';
        }

        try {
          const date = new Date(params.value);
          // Verificar si la fecha es válida
          if (isNaN(date.getTime())) {
            return 'Fecha inválida';
          }

          return date.toLocaleDateString('es-ES', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          });
        } catch (error) {
          console.error('Error formatting date:', error);
          return 'Error en fecha';
        }
      },
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Acciones',
      width: 120,
      getActions: (params: GridRowParams) => [
        <GridActionsCellItem
          key="edit"
          icon={
            <Tooltip title="Editar usuario">
              <EditIcon />
            </Tooltip>
          }
          label="Editar"
          onClick={() => handleEditUser(params.row as UserProfile)}
          disabled={actionLoading === params.id}
        />,
        <GridActionsCellItem
          key="toggle-status"
          icon={
            <Tooltip title={params.row?.is_active ? 'Desactivar usuario' : 'Activar usuario'}>
              {params.row?.is_active ? <BlockIcon /> : <CheckCircleIcon />}
            </Tooltip>
          }
          label={params.row?.is_active ? 'Desactivar' : 'Activar'}
          onClick={() => handleToggleStatus(params.row as UserProfile)}
          disabled={actionLoading === params.id}
        />,
        <GridActionsCellItem
          key="delete"
          icon={
            <Tooltip title="Eliminar usuario">
              <DeleteIcon />
            </Tooltip>
          }
          label="Eliminar"
          onClick={() => handleDeleteUser(params.id as string)}
          disabled={actionLoading === params.id}
        />,
      ],
    },
  ];

  // Manejo de estados de carga y error
  if (loading) {
    return <DataGridSkeleton rows={10} />;
  }

  // Verificación adicional de datos
  if (!Array.isArray(safeFilteredUsers)) {
    console.error('❌ filteredUsers is not an array:', filteredUsers);
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography color="error">
          Error: Los datos de usuarios no están en el formato correcto
        </Typography>
      </Box>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Componente de filtros */}
      <UserFiltersComponent
        filters={filters}
        onFiltersChange={setFilters}
        totalUsers={totalUsers}
        filteredUsers={filteredCount}
      />

      {/* Información de ordenamiento */}
      <SortingInfo
        sortModel={sortModel}
        onClearSort={handleClearSort}
      />

      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGridErrorBoundary>
            <DataGrid
              loading={loading}
              rows={safeFilteredUsers}
            columns={columns}
            paginationModel={paginationModel}
            onPaginationModelChange={(newModel) => {
              console.log('📄 Pagination changed:', newModel);
              setPaginationModel(newModel);
            }}
            pageSizeOptions={[10, 20, 50, 100]}
            paginationMode="client"
            sortModel={sortModel}
            onSortModelChange={(newModel) => {
              console.log('🔄 Sort changed:', newModel);
              setSortModel(newModel as any);
            }}
            sortingMode="client"
            checkboxSelection
            disableRowSelectionOnClick
            rowSelectionModel={safeSelectedUsers}
            onRowSelectionModelChange={(newSelection) => {
              console.log('✅ Selection changed:', newSelection);
              const selectionArray = Array.isArray(newSelection) ? newSelection : [];
              onSelectionChange?.(selectionArray as string[]);
            }}
            slots={{
              toolbar: () => (
                <CustomToolbar
                  onRefresh={onRefresh}
                  onCreateUser={handleOpenCreateModal}
                />
              ),
            }}
            sx={{
              border: 'none',
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: '#f8fafc',
                borderBottom: '2px solid #e2e8f0',
              },
              '& .MuiDataGrid-row:hover': {
                backgroundColor: '#f8fafc',
              },
            }}
          />
        </DataGridErrorBoundary>
      </Paper>

      {/* Información de paginación */}
      <PaginationInfo
        totalUsers={totalUsers}
        filteredUsers={filteredCount}
        currentPage={paginationModel.page}
        pageSize={paginationModel.pageSize}
        hasActiveFilters={hasActiveFilters}
        totalPages={Math.ceil(filteredCount / paginationModel.pageSize)}
      />

      {/* Dialog para editar usuario */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Editar Usuario</DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Box sx={{ pt: 2 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                <strong>Usuario:</strong>{' '}
                {selectedUser.full_name || selectedUser.email}
              </Typography>
              <FormControl fullWidth>
                <InputLabel>Rol</InputLabel>
                <Select
                  value={newRole}
                  label="Rol"
                  onChange={(e) => setNewRole(e.target.value as UserRole)}
                >
                  <MenuItem value="administrador">Administrador</MenuItem>
                  <MenuItem value="psicologo">Psicólogo</MenuItem>
                  <MenuItem value="paciente">Paciente</MenuItem>
                </Select>
              </FormControl>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancelar</Button>
          <Button
            onClick={handleUpdateRole}
            variant="contained"
            disabled={actionLoading === 'update'}
            sx={{ backgroundColor: '#011129' }}
          >
            {actionLoading === 'update' ? 'Actualizando...' : 'Actualizar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modal de creación de usuarios */}
      <CreateUserModal
        open={createUserModalOpen}
        onClose={() => setCreateUserModalOpen(false)}
        onCreateUser={handleCreateUser}
      />
    </motion.div>
  );
};
