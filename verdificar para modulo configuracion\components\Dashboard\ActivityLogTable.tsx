import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Typography,
  Box,
  Chip,
  Skeleton,
} from '@mui/material';
import {
  PersonAdd as PersonAddIcon,
  Edit as EditIcon,
  Assessment as AssessmentIcon,
  Login as LoginIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

interface ActivityLog {
  id: string;
  type:
    | 'user_created'
    | 'user_updated'
    | 'test_completed'
    | 'user_login'
    | 'system_config';
  description: string;
  user: string;
  timestamp: string;
  details?: string;
}

interface ActivityLogTableProps {
  logs: ActivityLog[];
  loading?: boolean;
}

const getActivityIcon = (type: ActivityLog['type']) => {
  const iconProps = { fontSize: 'small' as const };

  switch (type) {
    case 'user_created':
      return <PersonAddIcon {...iconProps} />;
    case 'user_updated':
      return <EditIcon {...iconProps} />;
    case 'test_completed':
      return <AssessmentIcon {...iconProps} />;
    case 'user_login':
      return <LoginIcon {...iconProps} />;
    case 'system_config':
      return <SettingsIcon {...iconProps} />;
    default:
      return <SettingsIcon {...iconProps} />;
  }
};

const getActivityColor = (type: ActivityLog['type']) => {
  switch (type) {
    case 'user_created':
      return 'success';
    case 'user_updated':
      return 'primary';
    case 'test_completed':
      return 'warning';
    case 'user_login':
      return 'info';
    case 'system_config':
      return 'secondary';
    default:
      return 'default';
  }
};

const getActivityLabel = (type: ActivityLog['type']) => {
  switch (type) {
    case 'user_created':
      return 'Usuario Creado';
    case 'user_updated':
      return 'Usuario Actualizado';
    case 'test_completed':
      return 'Test Completado';
    case 'user_login':
      return 'Inicio de Sesión';
    case 'system_config':
      return 'Configuración';
    default:
      return 'Actividad';
  }
};

const formatTimeAgo = (timestamp: string) => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffInMinutes = Math.floor(
    (now.getTime() - time.getTime()) / (1000 * 60),
  );

  if (diffInMinutes < 1) return 'Ahora mismo';
  if (diffInMinutes < 60) return `Hace ${diffInMinutes}m`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `Hace ${diffInHours}h`;

  const diffInDays = Math.floor(diffInHours / 24);
  return `Hace ${diffInDays}d`;
};

export const ActivityLogTable: React.FC<ActivityLogTableProps> = ({
  logs,
  loading = false,
}) => {
  if (loading) {
    return (
      <Box sx={{ p: 1 }}>
        <AnimatePresence>
          {[...Array(5)].map((_, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <motion.div
                  animate={{ opacity: [0.3, 1, 0.3] }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: index * 0.2,
                  }}
                >
                  <Skeleton
                    variant="circular"
                    width={32}
                    height={32}
                    sx={{ mr: 2 }}
                  />
                </motion.div>
                <Box sx={{ flex: 1 }}>
                  <Skeleton variant="text" width="80%" height={20} />
                  <Skeleton variant="text" width="60%" height={16} />
                </Box>
                <Skeleton variant="text" width={60} height={16} />
              </Box>
            </motion.div>
          ))}
        </AnimatePresence>
      </Box>
    );
  }

  return (
    <TableContainer sx={{ maxHeight: 300 }}>
      <Table size="small" stickyHeader>
        <TableHead>
          <TableRow>
            <TableCell sx={{ fontWeight: 600 }}>Actividad</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Usuario</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Tiempo</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <AnimatePresence>
            {logs.map((log, index) => (
              <motion.tr
                key={log.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                component={TableRow}
                whileHover={{
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  transition: { duration: 0.2 },
                }}
                sx={{
                  cursor: 'pointer',
                }}
              >
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Avatar
                      sx={{
                        width: 32,
                        height: 32,
                        bgcolor: `${getActivityColor(log.type)}.main`,
                      }}
                    >
                      {getActivityIcon(log.type)}
                    </Avatar>
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {log.description}
                      </Typography>
                      {log.details && (
                        <Typography variant="caption" color="text.secondary">
                          {log.details}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {log.user}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="caption" color="text.secondary">
                    {formatTimeAgo(log.timestamp)}
                  </Typography>
                </TableCell>
              </motion.tr>
            ))}
          </AnimatePresence>
        </TableBody>
      </Table>
    </TableContainer>
  );
};
