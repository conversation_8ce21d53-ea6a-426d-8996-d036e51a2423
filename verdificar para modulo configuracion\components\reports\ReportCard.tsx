import React from 'react';
import {
  <PERSON>,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  Box,
  Avatar,
  Divider,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  PictureAsPdf as PdfIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { Report } from '../../services/reports';
import { generateReportPDF, PDF_PRESETS } from '../../utils/pdfGenerator';

interface ReportCardProps {
  report: Report;
  onView: (report: Report) => void;
  onGeneratePdf?: (report: Report) => void;
}

/**
 * Componente de tarjeta individual para mostrar un reporte
 * Extraído de Informes.tsx para mejorar la modularidad
 */
const ReportCard: React.FC<ReportCardProps> = ({
  report,
  onView,
  onGeneratePdf,
}) => {
  const handlePdfGeneration = async () => {
    try {
      await generateReportPDF(report, PDF_PRESETS.maciReport);
      // Llamar callback si existe
      onGeneratePdf?.(report);
    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  };
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'draft':
        return 'warning';
      case 'pending':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'Completado';
      case 'draft':
        return 'Borrador';
      case 'pending':
        return 'Pendiente';
      default:
        return 'Sin estado';
    }
  };

  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: 4,
        },
      }}
    >
      <CardContent sx={{ flexGrow: 1 }}>
        {/* Header con avatar y estado */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
            <PersonIcon />
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" component="h3" noWrap>
              {report.patient_name || 'Paciente sin nombre'}
            </Typography>
            <Chip
              label={getStatusLabel(report.status)}
              color={getStatusColor(report.status)}
              size="small"
            />
          </Box>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Información del reporte */}
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <CalendarIcon
              sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }}
            />
            <Typography variant="body2" color="text.secondary">
              {formatDate(report.created_at)}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <AssessmentIcon
              sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }}
            />
            <Typography variant="body2" color="text.secondary">
              ID: {report.id}
            </Typography>
          </Box>
        </Box>

        {/* Información adicional si está disponible */}
        {report.patient_age && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Edad: {report.patient_age} años
          </Typography>
        )}

        {report.patient_gender && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Género: {report.patient_gender}
          </Typography>
        )}

        {/* Resumen del contenido */}
        {report.qualitative_interpretation && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              mt: 2,
              display: '-webkit-box',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {report.qualitative_interpretation.substring(0, 150)}...
          </Typography>
        )}
      </CardContent>

      {/* Acciones */}
      <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
        <Button
          startIcon={<VisibilityIcon />}
          onClick={() => onView(report)}
          variant="outlined"
          size="small"
          sx={{ flexGrow: 1, mr: 1 }}
        >
          Ver Reporte
        </Button>

        <Button
          startIcon={<PdfIcon />}
          onClick={handlePdfGeneration}
          variant="contained"
          size="small"
          color="secondary"
          sx={{ flexGrow: 1, ml: 1 }}
        >
          PDF
        </Button>
      </CardActions>
    </Card>
  );
};

export default ReportCard;
