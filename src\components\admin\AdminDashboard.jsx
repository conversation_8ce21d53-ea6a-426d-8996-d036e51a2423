import React, { useState, useEffect } from 'react';
import { FaUsers, FaTrendingUp, FaUserPlus, FaClipboardCheck } from 'react-icons/fa';
import supabase from '../../api/supabaseClient';

// Componente para las tarjetas de estadísticas
const StatCard = ({ title, value, icon: Icon, color, percentage, trend, subtitle }) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 border-l-4" style={{ borderLeftColor: color }}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-3xl font-bold text-gray-900">{value}</p>
          {subtitle && (
            <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
          )}
        </div>
        <div className="flex-shrink-0">
          <div 
            className="w-12 h-12 rounded-full flex items-center justify-center text-white"
            style={{ backgroundColor: color }}
          >
            <Icon className="w-6 h-6" />
          </div>
        </div>
      </div>
      {percentage && (
        <div className="mt-4 flex items-center">
          <span 
            className={`text-sm font-medium ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}
          >
            {trend === 'up' ? '+' : ''}{percentage}%
          </span>
          <span className="text-sm text-gray-500 ml-2">vs ayer</span>
        </div>
      )}
    </div>
  );
};

// Componente para el gráfico de actividad (simulado)
const ActivityChart = ({ data }) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Actividad de Usuarios (Últimos 7 días)</h3>
      <div className="h-64 flex items-end justify-between space-x-2">
        {data.map((item, index) => (
          <div key={index} className="flex-1 flex flex-col items-center">
            <div className="w-full bg-gray-200 rounded-t-lg relative" style={{ height: '200px' }}>
              {/* Nuevos Usuarios */}
              <div 
                className="absolute bottom-0 w-full bg-blue-500 rounded-t-lg opacity-80"
                style={{ height: `${(item.newUsers / Math.max(...data.map(d => d.newUsers + d.activeUsers + d.completedTests))) * 200}px` }}
              ></div>
              {/* Usuarios Activos */}
              <div 
                className="absolute bottom-0 w-full bg-green-500 rounded-t-lg opacity-80"
                style={{ 
                  height: `${((item.newUsers + item.activeUsers) / Math.max(...data.map(d => d.newUsers + d.activeUsers + d.completedTests))) * 200}px`,
                  bottom: `${(item.newUsers / Math.max(...data.map(d => d.newUsers + d.activeUsers + d.completedTests))) * 200}px`
                }}
              ></div>
              {/* Tests Completados */}
              <div 
                className="absolute bottom-0 w-full bg-orange-500 rounded-t-lg opacity-80"
                style={{ 
                  height: `${((item.newUsers + item.activeUsers + item.completedTests) / Math.max(...data.map(d => d.newUsers + d.activeUsers + d.completedTests))) * 200}px`,
                  bottom: `${((item.newUsers + item.activeUsers) / Math.max(...data.map(d => d.newUsers + d.activeUsers + d.completedTests))) * 200}px`
                }}
              ></div>
            </div>
            <p className="text-xs text-gray-600 mt-2">{item.date}</p>
          </div>
        ))}
      </div>
      <div className="mt-4 flex justify-center space-x-6">
        <div className="flex items-center">
          <div className="w-3 h-3 bg-blue-500 rounded mr-2"></div>
          <span className="text-sm text-gray-600">Nuevos Usuarios: 1</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
          <span className="text-sm text-gray-600">Usuarios Activos: 7</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-orange-500 rounded mr-2"></div>
          <span className="text-sm text-gray-600">Tests Completados: 3</span>
        </div>
      </div>
    </div>
  );
};

// Componente para la tabla de actividad reciente
const ActivityTable = ({ activities }) => {
  const getActivityIcon = (type) => {
    switch (type) {
      case 'user_created':
        return <FaUserPlus className="w-4 h-4 text-green-600" />;
      case 'test_completed':
        return <FaClipboardCheck className="w-4 h-4 text-orange-600" />;
      case 'user_login':
        return <FaUsers className="w-4 h-4 text-blue-600" />;
      default:
        return <FaTrendingUp className="w-4 h-4 text-gray-600" />;
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'user_created':
        return 'bg-green-100 text-green-800';
      case 'test_completed':
        return 'bg-orange-100 text-orange-800';
      case 'user_login':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Actividad Reciente</h3>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actividad
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Usuario
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tiempo
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {activities.map((activity, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 mr-3">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {activity.description}
                      </div>
                      <div className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActivityColor(activity.type)}`}>
                        {activity.type.replace('_', ' ')}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {activity.user}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {activity.time}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeToday: 0,
    newUsersWeek: 0,
    completedTests: 0
  });
  const [chartData, setChartData] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadStats(),
        loadChartData(),
        loadRecentActivities()
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setError('Error al cargar los datos del dashboard');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      // Obtener total de usuarios
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      // Obtener usuarios creados en la última semana
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);

      const { count: newUsersWeek } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', weekAgo.toISOString());

      // Simular otros datos por ahora
      setStats({
        totalUsers: totalUsers || 20,
        activeToday: Math.floor((totalUsers || 20) * 0.3), // 30% activos hoy
        newUsersWeek: newUsersWeek || 0,
        completedTests: Math.floor((totalUsers || 20) * 1.5) // 1.5 tests por usuario promedio
      });
    } catch (error) {
      console.error('Error loading stats:', error);
      // Datos de fallback
      setStats({
        totalUsers: 20,
        activeToday: 6,
        newUsersWeek: 0,
        completedTests: 30
      });
    }
  };

  const loadChartData = () => {
    // Datos simulados para el gráfico
    const data = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      data.push({
        date: date.toLocaleDateString('es-ES', { day: '2-digit', month: 'short' }),
        newUsers: Math.floor(Math.random() * 3),
        activeUsers: Math.floor(Math.random() * 8) + 2,
        completedTests: Math.floor(Math.random() * 5)
      });
    }
    setChartData(data);
  };

  const loadRecentActivities = () => {
    // Datos simulados para actividades recientes
    const activities = [
      {
        type: 'user_created',
        description: 'Nuevo usuario registrado',
        user: '<EMAIL>',
        time: 'Hace 1h'
      },
      {
        type: 'test_completed',
        description: 'Test MACI-II completado',
        user: '<EMAIL>',
        time: 'Hace 45m'
      },
      {
        type: 'user_login',
        description: 'Rol de usuario actualizado',
        user: '<EMAIL>',
        time: 'Hace 3h'
      }
    ];
    setRecentActivities(activities);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="text-red-800">{error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Dashboard de Administración</h2>
        <p className="text-gray-600 mt-2">Resumen general del sistema</p>
      </div>

      {/* Tarjetas de estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Usuarios Totales"
          value={stats.totalUsers}
          icon={FaUsers}
          color="#3B82F6"
          subtitle="vs ayer"
        />
        <StatCard
          title="Activos Hoy"
          value={stats.activeToday}
          icon={FaTrendingUp}
          color="#10B981"
          percentage="+8%"
          trend="up"
          subtitle="vs ayer anterior"
        />
        <StatCard
          title="Nuevos (7 días)"
          value={stats.newUsersWeek}
          icon={FaUserPlus}
          color="#06B6D4"
          percentage="+8%"
          trend="up"
          subtitle="vs sem anterior"
        />
        <StatCard
          title="Tests Completados"
          value={stats.completedTests}
          icon={FaClipboardCheck}
          color="#F59E0B"
          percentage="-3%"
          trend="down"
          subtitle="este mes"
        />
      </div>

      {/* Gráfico y tabla */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ActivityChart data={chartData} />
        <ActivityTable activities={recentActivities} />
      </div>
    </div>
  );
};

export default AdminDashboard;
