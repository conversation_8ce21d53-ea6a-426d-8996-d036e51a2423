import React, { useState, useEffect } from 'react';
import { useAnswersForComponents } from '../../stores/useQuestionnaireStore';
import { useNotify } from '../../stores/useUIStore';
import { useQuestionnaireAutoSave } from '../../hooks/useAutoSave';
import {
  Box,
  Grid,
  Typography,
  Button,
  LinearProgress,
  Paper,
  Pagination,
} from '@mui/material';
import QuestionItem from '../QuestionItem';
import { Question } from '../../types/questions';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import AutoSaveIndicator, { SavingProgress } from '../ui/AutoSaveIndicator';
import ProgressIndicator from '../ui/ProgressIndicator';

interface QuestionnaireFormProps {
  questions: Question[];
  patientId?: string; // Para el guardado automático
  questionnaireId?: string; // Para el guardado automático
  onSubmit: (
    answers: Array<{ id: number; answer: boolean | null }>,
    duration: number,
  ) => void; // Added duration
  onSave?: (
    answers: Array<{ id: number; answer: boolean | null }>,
    duration: number,
  ) => void; // Optional save handler
  autoSaveEnabled?: boolean; // Habilitar/deshabilitar guardado automático
}

const QuestionnaireForm: React.FC<QuestionnaireFormProps> = ({
  questions,
  patientId,
  questionnaireId = 'maci-ii',
  onSubmit,
  onSave,
  autoSaveEnabled = true,
}) => {
  const [startTime] = useState(() => Date.now());
  const [currentPage, setCurrentPage] = useState(1);
  const questionsPerPage = 20;

  // Usar el nuevo selector que maneja booleanos automáticamente
  const { answers, setAnswer, resetAnswers, isCompleted, error, clearError } =
    useAnswersForComponents();

  // Hook moderno para notificaciones
  const notify = useNotify();

  // Configurar guardado automático
  const autoSave = useQuestionnaireAutoSave(
    patientId || '',
    questionnaireId,
    answers,
    {
      enabled: autoSaveEnabled && !!patientId,
      interval: 30000, // 30 segundos
      onSave: (success, error) => {
        if (success) {
          console.log('Progreso guardado automáticamente');
        } else {
          console.error('Error en guardado automático:', error);
        }
      },
      onError: (error) => {
        notify.error(`Error al guardar: ${error}`);
      },
    }
  );

  // Crear mapa de respuestas para acceso rápido por ID de pregunta
  const answersMap = new Map(
    questions.map((q, index) => [q.id, answers[index]]),
  );

  const handleAnswer = (questionId: number, value: boolean | null) => {
    setAnswer(questionId, value);
  };

  const answeredCount = answers.filter((answer) => answer !== null).length;
  const progress = (answeredCount / questions.length) * 100;

  const handleSave = () => {
    if (!onSave) return;

    const duration = Math.floor((Date.now() - startTime) / 1000);
    const answersArray = questions.map((q) => ({
      id: q.id,
      answer: answersMap.get(q.id) ?? null,
    }));
    onSave(answersArray, duration);
  };

  const handleSubmit = () => {
    const duration = Math.floor((Date.now() - startTime) / 1000);

    // Verificar que todas las preguntas estén respondidas
    const unansweredCount = answers.filter((answer) => answer === null).length;
    if (unansweredCount > 0) {
      notify.error(`Faltan ${unansweredCount} preguntas por responder.`);
      return;
    }

    const answersArray = questions.map((q) => ({
      id: q.id,
      answer: answersMap.get(q.id) ?? null,
    }));
    onSubmit(answersArray, duration);
  };

  const handlePageChange = (
    _event: React.ChangeEvent<unknown> | null,
    value: number,
  ) => {
    setCurrentPage(value);
  };

  const indexOfLastQuestion = currentPage * questionsPerPage;
  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;
  const currentQuestions = questions.slice(
    indexOfFirstQuestion,
    indexOfLastQuestion,
  );

  const unansweredQuestions = currentQuestions.filter(
    (question) => answersMap.get(question.id) === null,
  );

  return (
    <Box component="div" sx={{ mb: 4 }}>
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Typography variant="h6">
            Progreso del cuestionario
          </Typography>
          {autoSaveEnabled && patientId && (
            <AutoSaveIndicator
              isSaving={autoSave.isSaving}
              lastSaved={autoSave.lastSaved}
              saveCount={autoSave.saveCount}
              error={autoSave.error}
              variant="chip"
              showLastSaved={true}
            />
          )}
        </Box>

        <ProgressIndicator
          value={progress}
          current={answeredCount}
          total={questions.length}
          variant="linear"
          size="medium"
          label={`${answeredCount} de ${questions.length} preguntas respondidas`}
          showPercentage={true}
        />
      </Paper>

      {/* Indicador de guardado flotante */}
      <SavingProgress
        show={autoSave.isSaving}
        message="Guardando progreso automáticamente..."
      />

      <Grid container spacing={2}>
        {currentQuestions.map((question) => (
          <Grid item xs={12} sm={6} md={4} key={question.id}>
            <QuestionItem
              question={{
                ...question,
                text: `${indexOfFirstQuestion + currentQuestions.indexOf(question) + 1}. ${question.text}`,
              }}
              onAnswer={handleAnswer}
              answer={answersMap.get(question.id) ?? null}
            />
          </Grid>
        ))}
      </Grid>

      {unansweredQuestions.length > 0 && (
        <Paper sx={{ mt: 3, p: 2, backgroundColor: '#fff3e0' }}>
          <Typography variant="h6" gutterBottom color="warning.main">
            Preguntas sin responder en esta página:
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {unansweredQuestions.map((q, index) => (
              <React.Fragment key={q.id}>
                {index > 0 && ', '}
                {indexOfFirstQuestion + currentQuestions.indexOf(q) + 1}
              </React.Fragment>
            ))}
          </Typography>
        </Paper>
      )}

      <Box
        sx={{
          mt: 3,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <Button
          variant="outlined"
          onClick={() => handlePageChange(null, currentPage - 1)}
          disabled={currentPage === 1}
          startIcon={<NavigateBeforeIcon />}
        >
          Anterior
        </Button>
        <Pagination
          count={Math.ceil(questions.length / questionsPerPage)}
          page={currentPage}
          onChange={handlePageChange}
          color="primary"
          size="large"
        />
        <Button
          variant="outlined"
          onClick={() => handlePageChange(null, currentPage + 1)}
          disabled={
            currentPage === Math.ceil(questions.length / questionsPerPage)
          }
          endIcon={<NavigateNextIcon />}
        >
          Siguiente
        </Button>
      </Box>
      <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        {onSave && (
          <Button
            variant="outlined"
            color="primary"
            onClick={handleSave}
          >
            Guardar Progreso
          </Button>
        )}
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={!isCompleted}
        >
          Finalizar cuestionario
        </Button>
      </Box>
    </Box>
  );
};

export default QuestionnaireForm;
