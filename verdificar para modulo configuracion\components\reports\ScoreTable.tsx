import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Avatar,
  Box,
  Chip,
} from '@mui/material';
import { scaleNames, validityScaleNames } from '../../config/scalesData';

interface ScoreData {
  [key: string]: number;
}

interface ScoreTableProps {
  title: string;
  scores: ScoreData;
  centileScores?: ScoreData;
  category:
    | 'personalityPatterns'
    | 'expressedConcerns'
    | 'clinicalSyndromes'
    | 'grossmanFacets'
    | 'validity';
  showCentiles?: boolean;
}

/**
 * Componente de tabla de puntuaciones para reportes MACI-II
 * Extraído de Informes.tsx para mejorar la modularidad
 */
const ScoreTable: React.FC<ScoreTableProps> = ({
  title,
  scores,
  centileScores,
  category,
  showCentiles = false,
}) => {
  const getScaleName = (id: string): string => {
    if (category === 'validity') {
      return (
        validityScaleNames[id as keyof typeof validityScaleNames] ||
        `Escala ${id}`
      );
    }

    const categoryData = scaleNames[category];
    if (categoryData) {
      return categoryData[id as keyof typeof categoryData] || `Escala ${id}`;
    }

    return `Escala ${id}`;
  };

  const getScoreColor = (score: number, isPC: boolean = true): string => {
    if (isPC) {
      // Colores para puntuaciones PC
      if (score >= 85) return '#d32f2f'; // Rojo - Muy alto
      if (score >= 75) return '#f57c00'; // Naranja - Alto
      if (score >= 60) return '#fbc02d'; // Amarillo - Moderado
      return '#388e3c'; // Verde - Normal
    } else {
      // Colores para puntuaciones PR (Grossman)
      if (score >= 90) return '#d32f2f'; // Rojo - Muy prominente
      if (score >= 75) return '#f57c00'; // Naranja - Clínicamente relevante
      return '#388e3c'; // Verde - No significativo
    }
  };

  const getScoreInterpretation = (
    score: number,
    isPC: boolean = true,
  ): string => {
    if (isPC) {
      if (score >= 85) return 'Muy Alto';
      if (score >= 75) return 'Alto';
      if (score >= 60) return 'Moderado';
      return 'Normal';
    } else {
      if (score >= 90) return 'Muy Prominente';
      if (score >= 75) return 'Clínicamente Relevante';
      return 'No Significativo';
    }
  };

  const isGrossmanCategory = category === 'grossmanFacets';
  const scoreType = isGrossmanCategory ? 'PR' : 'PC';

  return (
    <Paper sx={{ mb: 3 }}>
      <Box sx={{ p: 2, bgcolor: 'primary.main', color: 'white' }}>
        <Typography variant="h6" component="h3">
          {title}
        </Typography>
      </Box>

      <TableContainer>
        <Table size="small">
          <TableHead>
            <TableRow sx={{ bgcolor: 'grey.50' }}>
              <TableCell sx={{ fontWeight: 'bold' }}>ID</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Escala</TableCell>
              <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                Puntuación {scoreType}
              </TableCell>
              {showCentiles && centileScores && (
                <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                  Percentil
                </TableCell>
              )}
              <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                Interpretación
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {Object.entries(scores).map(([scaleId, score]) => {
              const scaleName = getScaleName(scaleId);
              const centileScore = centileScores?.[scaleId];
              const scoreColor = getScoreColor(score, !isGrossmanCategory);
              const interpretation = getScoreInterpretation(
                score,
                !isGrossmanCategory,
              );

              return (
                <TableRow
                  key={scaleId}
                  sx={{
                    '&:hover': { bgcolor: 'grey.50' },
                    '&:nth-of-type(even)': { bgcolor: 'grey.25' },
                  }}
                >
                  <TableCell>
                    <Avatar
                      sx={{
                        width: 32,
                        height: 32,
                        fontSize: '0.75rem',
                        bgcolor: 'primary.main',
                      }}
                    >
                      {scaleId}
                    </Avatar>
                  </TableCell>

                  <TableCell>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {scaleName}
                    </Typography>
                  </TableCell>

                  <TableCell align="center">
                    <Chip
                      label={score}
                      sx={{
                        bgcolor: scoreColor,
                        color: 'white',
                        fontWeight: 'bold',
                        minWidth: 50,
                      }}
                      size="small"
                    />
                  </TableCell>

                  {showCentiles && centileScores && (
                    <TableCell align="center">
                      <Typography variant="body2">
                        {centileScore !== undefined ? centileScore : '-'}
                      </Typography>
                    </TableCell>
                  )}

                  <TableCell align="center">
                    <Chip
                      label={interpretation}
                      variant="outlined"
                      sx={{
                        borderColor: scoreColor,
                        color: scoreColor,
                        fontSize: '0.75rem',
                      }}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
};

export default ScoreTable;
