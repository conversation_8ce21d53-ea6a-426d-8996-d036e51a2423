import React, { useState, useCallback } from 'react';
import {
  Box,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  Typography,
  Alert,
  Tooltip,
  Avatar,
} from '@mui/material';
import {
  DataGrid,
  GridColDef,
  GridActionsCellItem,
  GridRowParams,
  GridToolbar,
  GridValueGetterParams,
} from '@mui/x-data-grid';
import {
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  AdminPanelSettings as AdminIcon,
  Psychology as PsychologyIcon,
  PersonOutline as PatientIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
} from '@mui/icons-material';
import { User, UserRole } from '../../services/userManagement';
import { useNotifier } from '../../hooks/useNotifier';

interface EnhancedUserTableProps {
  users: User[];
  loading: boolean;
  onUpdateUser: (userId: string, updates: Partial<User>) => Promise<void>;
  onDeleteUser: (userId: string) => Promise<void>;
  onRefresh: () => void;
}

const EnhancedUserTable: React.FC<EnhancedUserTableProps> = ({
  users,
  loading,
  onUpdateUser,
  onDeleteUser,
  onRefresh,
}) => {
  const { showNotification } = useNotifier();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [actionMenuAnchor, setActionMenuAnchor] = useState<null | HTMLElement>(
    null,
  );
  const [editRoleDialog, setEditRoleDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [newRole, setNewRole] = useState<UserRole>('paciente');

  const handleActionMenuOpen = useCallback(
    (event: React.MouseEvent<HTMLElement>, user: User) => {
      event.stopPropagation();
      setActionMenuAnchor(event.currentTarget);
      setSelectedUser(user);
    },
    [],
  );

  const handleActionMenuClose = useCallback(() => {
    setActionMenuAnchor(null);
    setSelectedUser(null);
  }, []);

  const handleEditRole = useCallback(() => {
    if (selectedUser) {
      setNewRole(selectedUser.role);
      setEditRoleDialog(true);
    }
    handleActionMenuClose();
  }, [selectedUser, handleActionMenuClose]);

  const handleDeleteUser = useCallback(() => {
    setDeleteDialog(true);
    handleActionMenuClose();
  }, [handleActionMenuClose]);

  const handleToggleStatus = useCallback(async () => {
    if (selectedUser) {
      try {
        await onUpdateUser(selectedUser.id, {
          is_active: !selectedUser.is_active,
        });
        showNotification(
          `Usuario ${selectedUser.is_active ? 'desactivado' : 'activado'} correctamente`,
          'success',
        );
        onRefresh();
      } catch (error) {
        showNotification('Error al cambiar el estado del usuario', 'error');
      }
    }
    handleActionMenuClose();
  }, [
    selectedUser,
    onUpdateUser,
    showNotification,
    onRefresh,
    handleActionMenuClose,
  ]);

  const handleSaveRole = useCallback(async () => {
    if (selectedUser && newRole !== selectedUser.role) {
      try {
        await onUpdateUser(selectedUser.id, { role: newRole });
        showNotification('Rol actualizado correctamente', 'success');
        onRefresh();
      } catch (error) {
        showNotification('Error al actualizar el rol', 'error');
      }
    }
    setEditRoleDialog(false);
    setSelectedUser(null);
  }, [selectedUser, newRole, onUpdateUser, showNotification, onRefresh]);

  const handleConfirmDelete = useCallback(async () => {
    if (selectedUser) {
      try {
        await onDeleteUser(selectedUser.id);
        showNotification('Usuario eliminado correctamente', 'success');
        onRefresh();
      } catch (error) {
        showNotification('Error al eliminar el usuario', 'error');
      }
    }
    setDeleteDialog(false);
    setSelectedUser(null);
  }, [selectedUser, onDeleteUser, showNotification, onRefresh]);

  const getRoleIcon = (role: UserRole | string | undefined) => {
    const safeRole = role || 'paciente';
    switch (safeRole) {
      case 'administrador':
        return <AdminIcon sx={{ color: '#f44336', fontSize: 16 }} />;
      case 'psicologo':
        return <PsychologyIcon sx={{ color: '#2196f3', fontSize: 16 }} />;
      case 'paciente':
        return <PatientIcon sx={{ color: '#4caf50', fontSize: 16 }} />;
      default:
        return <PatientIcon sx={{ color: '#4caf50', fontSize: 16 }} />;
    }
  };

  const getRoleColor = (role: UserRole | string | undefined) => {
    const safeRole = role || 'paciente';
    switch (safeRole) {
      case 'administrador':
        return '#f44336';
      case 'psicologo':
        return '#2196f3';
      case 'paciente':
        return '#4caf50';
      default:
        return '#4caf50';
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'avatar',
      headerName: '',
      width: 60,
      sortable: false,
      filterable: false,
      renderCell: (params) => {
        const name =
          params.row.name || params.row.full_name || params.row.email || 'U';
        const firstLetter =
          typeof name === 'string' && name.length > 0
            ? name.charAt(0).toUpperCase()
            : 'U';

        return (
          <Avatar
            sx={{
              width: 32,
              height: 32,
              bgcolor: getRoleColor(params.row.role || 'paciente'),
              fontSize: '0.875rem',
            }}
          >
            {firstLetter}
          </Avatar>
        );
      },
    },
    {
      field: 'name',
      headerName: 'Nombre',
      flex: 1,
      minWidth: 200,
      valueGetter: (params) => {
        const row = params.row || {};
        return row.full_name || row.name || row.email || 'Sin nombre';
      },
      renderCell: (params) => {
        const name = params.value || 'Sin nombre';
        const email = params.row?.email || 'Sin email';

        return (
          <Box>
            <Typography variant="body2" fontWeight="medium">
              {name}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {email}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'role',
      headerName: 'Rol',
      width: 150,
      renderCell: (params) => {
        const role = params.value || 'paciente';
        const roleLabel =
          role === 'administrador'
            ? 'Administrador'
            : role === 'psicologo'
              ? 'Psicólogo'
              : 'Paciente';

        return (
          <Chip
            icon={getRoleIcon(role)}
            label={roleLabel}
            size="small"
            sx={{
              backgroundColor: `${getRoleColor(role)}20`,
              color: getRoleColor(role),
              fontWeight: 'medium',
            }}
          />
        );
      },
    },
    {
      field: 'is_active',
      headerName: 'Estado',
      width: 120,
      renderCell: (params) => {
        const isActive = params.value !== false; // Default to true if undefined

        return (
          <Chip
            icon={isActive ? <CheckCircleIcon /> : <BlockIcon />}
            label={isActive ? 'Activo' : 'Inactivo'}
            size="small"
            color={isActive ? 'success' : 'error'}
            variant="outlined"
          />
        );
      },
    },
    {
      field: 'created_at',
      headerName: 'Fecha de Registro',
      width: 180,
      valueGetter: (params: GridValueGetterParams) => {
        if (!params.value) return 'Sin fecha';

        try {
          return new Date(params.value).toLocaleDateString('es-ES', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
          });
        } catch (error) {
          return 'Fecha inválida';
        }
      },
    },
    {
      field: 'last_sign_in_at',
      headerName: 'Último Acceso',
      width: 180,
      valueGetter: (params: GridValueGetterParams) => {
        if (!params.value) return 'Nunca';
        return new Date(params.value).toLocaleDateString('es-ES', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        });
      },
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Acciones',
      width: 80,
      getActions: (params: GridRowParams) => [
        <GridActionsCellItem
          key="more"
          icon={
            <Tooltip title="Más opciones">
              <MoreVertIcon />
            </Tooltip>
          }
          label="Más opciones"
          onClick={(event) => handleActionMenuOpen(event, params.row as User)}
        />,
      ],
    },
  ];

  return (
    <Box sx={{ height: 600, width: '100%' }}>
      <DataGrid
        rows={users}
        columns={columns}
        loading={loading}
        pagination
        pageSizeOptions={[10, 25, 50]}
        initialState={{
          pagination: { paginationModel: { pageSize: 10 } },
        }}
        slots={{ toolbar: GridToolbar }}
        slotProps={{
          toolbar: {
            showQuickFilter: true,
            quickFilterProps: { debounceMs: 500 },
          },
        }}
        sx={{
          '& .MuiDataGrid-root': {
            border: 'none',
          },
          '& .MuiDataGrid-cell': {
            borderBottom: '1px solid #f0f0f0',
          },
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: '#f8f9fa',
            borderBottom: '2px solid #e0e0e0',
          },
          '& .MuiDataGrid-row:hover': {
            backgroundColor: '#f5f5f5',
          },
        }}
        disableRowSelectionOnClick
      />

      {/* Menu de Acciones */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleEditRole}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Cambiar Rol</ListItemText>
        </MenuItem>

        <MenuItem onClick={handleToggleStatus}>
          <ListItemIcon>
            {selectedUser?.is_active ? (
              <BlockIcon fontSize="small" />
            ) : (
              <CheckCircleIcon fontSize="small" />
            )}
          </ListItemIcon>
          <ListItemText>
            {selectedUser?.is_active ? 'Desactivar' : 'Activar'}
          </ListItemText>
        </MenuItem>

        <MenuItem onClick={handleDeleteUser} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Eliminar Usuario</ListItemText>
        </MenuItem>
      </Menu>

      {/* Dialog para Editar Rol */}
      <Dialog open={editRoleDialog} onClose={() => setEditRoleDialog(false)}>
        <DialogTitle>Cambiar Rol de Usuario</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Usuario: {selectedUser?.name}
          </Typography>
          <FormControl fullWidth>
            <InputLabel>Nuevo Rol</InputLabel>
            <Select
              value={newRole}
              label="Nuevo Rol"
              onChange={(e) => setNewRole(e.target.value as UserRole)}
            >
              <MenuItem value="paciente">Paciente</MenuItem>
              <MenuItem value="psicologo">Psicólogo</MenuItem>
              <MenuItem value="administrador">Administrador</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditRoleDialog(false)}>Cancelar</Button>
          <Button onClick={handleSaveRole} variant="contained">
            Guardar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para Confirmar Eliminación */}
      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
        <DialogTitle>Confirmar Eliminación</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Esta acción no se puede deshacer.
          </Alert>
          <Typography>
            ¿Estás seguro de que quieres eliminar al usuario{' '}
            <strong>{selectedUser?.name}</strong>?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog(false)}>Cancelar</Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
          >
            Eliminar
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EnhancedUserTable;
