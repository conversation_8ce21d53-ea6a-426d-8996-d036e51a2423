import { useState, useEffect, useCallback } from 'react';
// import { profileService } from '../services/profileService';
// import { telemetryService } from '../services/telemetryService';
import { supabase } from '../lib/supabaseClient';
import type { UserProfile, ProfileUpdateData, ProfileStats, ProfileFormData } from '../types/profile';

interface UseProfileReturn {
  // Estados de datos
  profile: UserProfile | null;
  stats: ProfileStats | null;
  formData: ProfileFormData;
  
  // Estados de carga
  loading: boolean;
  saving: boolean;
  loadingStats: boolean;
  
  // Funciones
  updateFormData: (field: keyof ProfileFormData, value: string) => void;
  saveProfile: () => Promise<boolean>;
  refreshProfile: () => Promise<void>;
  refreshStats: () => Promise<void>;
  
  // Validación
  hasChanges: boolean;
  isValid: boolean;
  errors: Partial<Record<keyof ProfileFormData, string>>;
}

/**
 * Hook personalizado para gestión de perfil de usuario
 */
export const useProfile = (): UseProfileReturn => {
  // Estados de datos
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [stats, setStats] = useState<ProfileStats | null>(null);
  const [formData, setFormData] = useState<ProfileFormData>({
    fullName: '',
    phone: '',
    department: '',
    bio: '',
  });

  // Estados de carga
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [loadingStats, setLoadingStats] = useState(false);

  // Estados de validación
  const [errors, setErrors] = useState<Partial<Record<keyof ProfileFormData, string>>>({});

  // Cargar perfil inicial
  useEffect(() => {
    loadProfile();
  }, []);

  // Cargar estadísticas cuando se carga el perfil
  useEffect(() => {
    if (profile?.id) {
      loadStats();
    }
  }, [profile?.id]);

  /**
   * Carga el perfil del usuario actual
   */
  const loadProfile = useCallback(async () => {
    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Usuario no autenticado');
        return;
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error loading profile:', error);
      } else if (data) {
        setProfile(data as UserProfile);
        setFormData({
          fullName: data.full_name || '',
          phone: data.phone || '',
          department: data.department || '',
          bio: data.bio || '',
        });
      }
    } catch (error) {
      console.error('Unexpected error loading profile:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Carga las estadísticas del perfil
   */
  const loadStats = useCallback(async () => {
    if (!profile?.id) return;

    setLoadingStats(true);
    try {
      // Estadísticas simuladas por ahora
      setStats({
        totalEvaluations: 0,
        completedReports: 0,
        lastActivity: new Date().toISOString(),
        accountAge: 0,
      });
    } catch (error) {
      console.error('Unexpected error loading profile stats:', error);
    } finally {
      setLoadingStats(false);
    }
  }, [profile?.id]);

  /**
   * Actualiza un campo del formulario
   */
  const updateFormData = useCallback((field: keyof ProfileFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Limpiar error del campo cuando se modifica
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  }, [errors]);

  /**
   * Valida los datos del formulario
   */
  const validateForm = useCallback((): boolean => {
    const newErrors: Partial<Record<keyof ProfileFormData, string>> = {};

    // Validar nombre completo (requerido)
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'El nombre completo es requerido';
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = 'El nombre debe tener al menos 2 caracteres';
    }

    // Validar teléfono (opcional, pero si se proporciona debe ser válido)
    if (formData.phone && !/^\+?[\d\s\-\(\)]{10,}$/.test(formData.phone)) {
      newErrors.phone = 'Formato de teléfono inválido';
    }

    // Validar bio (máximo 500 caracteres)
    if (formData.bio && formData.bio.length > 500) {
      newErrors.bio = 'La biografía no puede exceder 500 caracteres';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  /**
   * Guarda los cambios del perfil
   */
  const saveProfile = useCallback(async (): Promise<boolean> => {
    if (!validateForm()) {
      return false;
    }

    setSaving(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Usuario no autenticado');
        return false;
      }

      const updateData: ProfileUpdateData = {
        full_name: formData.fullName.trim(),
        phone: formData.phone.trim() || undefined,
        department: formData.department.trim() || undefined,
        bio: formData.bio.trim() || undefined,
      };

      const { error } = await supabase
        .from('profiles')
        .update({
          ...updateData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (error) {
        console.error('Error updating profile:', error);
        return false;
      }

      // Recargar perfil para obtener datos actualizados
      await loadProfile();
      return true;
    } catch (error) {
      console.error('Unexpected error saving profile:', error);
      return false;
    } finally {
      setSaving(false);
    }
  }, [formData, validateForm, loadProfile]);

  /**
   * Refresca el perfil
   */
  const refreshProfile = useCallback(async () => {
    await loadProfile();
  }, [loadProfile]);

  /**
   * Refresca las estadísticas
   */
  const refreshStats = useCallback(async () => {
    await loadStats();
  }, [loadStats]);

  // Calcular si hay cambios
  const hasChanges = profile ? (
    formData.fullName !== (profile.full_name || '') ||
    formData.phone !== (profile.phone || '') ||
    formData.department !== (profile.department || '') ||
    formData.bio !== (profile.bio || '')
  ) : false;

  // Validar si el formulario es válido
  const isValid = formData.fullName.trim().length >= 2 && Object.keys(errors).length === 0;

  return {
    // Estados de datos
    profile,
    stats,
    formData,
    
    // Estados de carga
    loading,
    saving,
    loadingStats,
    
    // Funciones
    updateFormData,
    saveProfile,
    refreshProfile,
    refreshStats,
    
    // Validación
    hasChanges,
    isValid,
    errors,
  };
};
