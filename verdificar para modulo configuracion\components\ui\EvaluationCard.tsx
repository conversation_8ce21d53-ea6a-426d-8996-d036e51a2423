import React from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Box,
  Avatar,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  PlayArrow as StartIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import StatusBadge, { StatusType } from './StatusBadge';
import ProgressIndicator from './ProgressIndicator';

interface EvaluationCardProps {
  id: string;
  title: string;
  patientName?: string;
  psychologistName?: string;
  status: StatusType;
  progress?: number;
  currentQuestion?: number;
  totalQuestions?: number;
  assignedDate?: Date;
  dueDate?: Date;
  completedDate?: Date;
  description?: string;
  onStart?: () => void;
  onContinue?: () => void;
  onView?: () => void;
  onEdit?: () => void;
  variant?: 'patient' | 'psychologist' | 'admin';
  priority?: 'low' | 'medium' | 'high';
}

const StyledCard = styled(Card)<{ priority?: string }>(({ theme, priority }) => ({
  transition: 'all 0.3s ease-in-out',
  cursor: 'pointer',
  position: 'relative',
  overflow: 'visible',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
  },
  ...(priority === 'high' && {
    borderLeft: `4px solid #F28A7C`,
  }),
  ...(priority === 'medium' && {
    borderLeft: `4px solid #DDA15E`,
  }),
  ...(priority === 'low' && {
    borderLeft: `4px solid #63B4A9`,
  }),
}));

const PriorityIndicator = styled(Box)<{ priority: string }>(({ priority }) => ({
  position: 'absolute',
  top: 12,
  right: 12,
  width: 8,
  height: 8,
  borderRadius: '50%',
  backgroundColor: 
    priority === 'high' ? '#F28A7C' :
    priority === 'medium' ? '#DDA15E' : '#63B4A9',
}));

export const EvaluationCard: React.FC<EvaluationCardProps> = ({
  id,
  title,
  patientName,
  psychologistName,
  status,
  progress = 0,
  currentQuestion,
  totalQuestions,
  assignedDate,
  dueDate,
  completedDate,
  description,
  onStart,
  onContinue,
  onView,
  onEdit,
  variant = 'patient',
  priority = 'medium',
}) => {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('es-ES', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    }).format(date);
  };

  const isOverdue = dueDate && new Date() > dueDate && status !== 'completada';
  const actualPriority = isOverdue ? 'high' : priority;

  const getActionButton = () => {
    switch (status) {
      case 'asignada':
        return (
          <Button
            variant="contained"
            color="primary"
            startIcon={<StartIcon />}
            onClick={onStart}
            size="small"
          >
            Comenzar
          </Button>
        );
      case 'en_progreso':
        return (
          <Button
            variant="contained"
            color="primary"
            startIcon={<PlayArrow />}
            onClick={onContinue}
            size="small"
          >
            Continuar
          </Button>
        );
      case 'completada':
      case 'revisada':
        return (
          <Button
            variant="outlined"
            color="primary"
            startIcon={<ViewIcon />}
            onClick={onView}
            size="small"
          >
            Ver Resultados
          </Button>
        );
      default:
        return null;
    }
  };

  return (
    <StyledCard priority={actualPriority}>
      {priority && <PriorityIndicator priority={actualPriority} />}
      
      <CardContent sx={{ pb: 1 }}>
        {/* Header con título y estado */}
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Box flex={1} mr={2}>
            <Typography variant="h6" component="h3" gutterBottom>
              {title}
            </Typography>
            {description && (
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                {description}
              </Typography>
            )}
          </Box>
          <StatusBadge status={status} />
        </Box>

        {/* Información del paciente/psicólogo */}
        {(patientName || psychologistName) && (
          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <Avatar sx={{ width: 32, height: 32, bgcolor: '#5A92C8' }}>
              <PersonIcon fontSize="small" />
            </Avatar>
            <Box>
              {variant === 'psychologist' && patientName && (
                <Typography variant="body2" fontWeight={500}>
                  {patientName}
                </Typography>
              )}
              {variant === 'patient' && psychologistName && (
                <Typography variant="body2" fontWeight={500}>
                  Dr. {psychologistName}
                </Typography>
              )}
              {variant === 'admin' && patientName && psychologistName && (
                <>
                  <Typography variant="body2" fontWeight={500}>
                    {patientName}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Dr. {psychologistName}
                  </Typography>
                </>
              )}
            </Box>
          </Box>
        )}

        {/* Progreso */}
        {(status === 'en_progreso' || status === 'completada') && (
          <Box mb={2}>
            <ProgressIndicator
              value={progress}
              current={currentQuestion}
              total={totalQuestions}
              variant={totalQuestions ? 'steps' : 'linear'}
              size="small"
              label={totalQuestions ? 'Progreso del cuestionario' : undefined}
              showPercentage={!totalQuestions}
            />
          </Box>
        )}

        {/* Fechas */}
        <Box display="flex" gap={2} flexWrap="wrap">
          {assignedDate && (
            <Box display="flex" alignItems="center" gap={0.5}>
              <AssignmentIcon fontSize="small" color="action" />
              <Typography variant="caption" color="text.secondary">
                Asignada: {formatDate(assignedDate)}
              </Typography>
            </Box>
          )}
          {dueDate && (
            <Box display="flex" alignItems="center" gap={0.5}>
              <ScheduleIcon 
                fontSize="small" 
                color={isOverdue ? "error" : "action"} 
              />
              <Typography 
                variant="caption" 
                color={isOverdue ? "error" : "text.secondary"}
                fontWeight={isOverdue ? 600 : 400}
              >
                Vence: {formatDate(dueDate)}
              </Typography>
            </Box>
          )}
          {completedDate && (
            <Box display="flex" alignItems="center" gap={0.5}>
              <Typography variant="caption" color="success.main" fontWeight={500}>
                Completada: {formatDate(completedDate)}
              </Typography>
            </Box>
          )}
        </Box>
      </CardContent>

      <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>
        <Box display="flex" justifyContent="space-between" width="100%">
          <Box>
            {getActionButton()}
          </Box>
          
          {variant === 'psychologist' && (
            <Box>
              {onEdit && (
                <Tooltip title="Editar evaluación">
                  <IconButton size="small" onClick={onEdit}>
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          )}
        </Box>
      </CardActions>
    </StyledCard>
  );
};

export default EvaluationCard;
