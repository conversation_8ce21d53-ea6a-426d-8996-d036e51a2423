import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardHeader,
  CardContent,
  Typography,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Box,
  Alert,
  Button,
  Divider,
  Grid,
  Paper,
  Switch,
  Slider,
  Chip,
} from '@mui/material';
import {
  Palette as PaletteIcon,
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
  SettingsBrightness as AutoModeIcon,
  Contrast as ContrastIcon,
  FormatSize as FontSizeIcon,
  Accessibility as AccessibilityIcon,
} from '@mui/icons-material';

interface ThemePreferences {
  mode: 'light' | 'dark' | 'auto';
  primaryColor: string;
  fontSize: number;
  highContrast: boolean;
  reducedMotion: boolean;
  compactMode: boolean;
}

const defaultPreferences: ThemePreferences = {
  mode: 'light',
  primaryColor: '#5A92C8',
  fontSize: 14,
  highContrast: false,
  reducedMotion: false,
  compactMode: false,
};

const colorOptions = [
  { name: '<PERSON>zul <PERSON>I', value: '#5A92C8', description: 'Color principal del sistema' },
  { name: 'Verde Terapéutico', value: '#63B4A9', description: 'Calma y tranquilidad' },
  { name: 'Coral Cálido', value: '#F28A7C', description: 'Energía y calidez' },
  { name: 'Púrpura Profesional', value: '#8E7CC3', description: 'Elegancia y profesionalismo' },
  { name: 'Naranja Motivacional', value: '#FF9800', description: 'Optimismo y motivación' },
  { name: 'Índigo Concentración', value: '#3F51B5', description: 'Concentración y estudio' },
];

const ThemeSettings: React.FC = () => {
  const [preferences, setPreferences] = useState<ThemePreferences>(defaultPreferences);
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = () => {
    try {
      const saved = localStorage.getItem('maci-theme-preferences');
      if (saved) {
        const parsed = JSON.parse(saved);
        setPreferences({ ...defaultPreferences, ...parsed });
      }
    } catch (error) {
      console.error('Error loading theme preferences:', error);
    }
  };

  const savePreferences = async () => {
    try {
      setSaving(true);
      localStorage.setItem('maci-theme-preferences', JSON.stringify(preferences));
      
      // Aplicar cambios inmediatamente
      applyTheme(preferences);
      
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      console.error('Error saving theme preferences:', error);
    } finally {
      setSaving(false);
    }
  };

  const applyTheme = (prefs: ThemePreferences) => {
    const root = document.documentElement;
    
    // Aplicar modo de color
    if (prefs.mode === 'auto') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      root.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
    } else {
      root.setAttribute('data-theme', prefs.mode);
    }
    
    // Aplicar color primario
    root.style.setProperty('--primary-color', prefs.primaryColor);
    
    // Aplicar tamaño de fuente
    root.style.setProperty('--base-font-size', `${prefs.fontSize}px`);
    
    // Aplicar alto contraste
    if (prefs.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
    
    // Aplicar movimiento reducido
    if (prefs.reducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }
    
    // Aplicar modo compacto
    if (prefs.compactMode) {
      root.classList.add('compact-mode');
    } else {
      root.classList.remove('compact-mode');
    }
  };

  const handlePreferenceChange = (key: keyof ThemePreferences, value: any) => {
    const newPreferences = { ...preferences, [key]: value };
    setPreferences(newPreferences);
    
    // Aplicar cambios en tiempo real
    applyTheme(newPreferences);
  };

  const resetToDefaults = () => {
    setPreferences(defaultPreferences);
    applyTheme(defaultPreferences);
  };

  const getThemeIcon = (mode: string) => {
    switch (mode) {
      case 'light':
        return <LightModeIcon />;
      case 'dark':
        return <DarkModeIcon />;
      case 'auto':
        return <AutoModeIcon />;
      default:
        return <LightModeIcon />;
    }
  };

  return (
    <Card elevation={3} sx={{ borderRadius: 2 }}>
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PaletteIcon color="primary" />
            <Typography variant="h6">Configuración de Tema</Typography>
          </Box>
        }
        subheader="Personaliza la apariencia de la aplicación"
        action={
          <Chip
            label={`Tema ${preferences.mode}`}
            color="primary"
            variant="outlined"
            icon={getThemeIcon(preferences.mode)}
          />
        }
      />
      <Divider />
      <CardContent>
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            ¡Configuración de tema guardada correctamente!
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Modo de Color */}
          <Grid item xs={12} md={6}>
            <FormControl component="fieldset" sx={{ width: '100%' }}>
              <FormLabel component="legend" sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {getThemeIcon(preferences.mode)}
                  <Typography variant="subtitle1">Modo de Color</Typography>
                </Box>
              </FormLabel>
              <RadioGroup
                value={preferences.mode}
                onChange={(e) => handlePreferenceChange('mode', e.target.value)}
              >
                <FormControlLabel
                  value="light"
                  control={<Radio />}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LightModeIcon fontSize="small" />
                      <Typography>Claro</Typography>
                    </Box>
                  }
                />
                <FormControlLabel
                  value="dark"
                  control={<Radio />}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <DarkModeIcon fontSize="small" />
                      <Typography>Oscuro</Typography>
                    </Box>
                  }
                />
                <FormControlLabel
                  value="auto"
                  control={<Radio />}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AutoModeIcon fontSize="small" />
                      <Typography>Automático</Typography>
                    </Box>
                  }
                />
              </RadioGroup>
            </FormControl>
          </Grid>

          {/* Color Primario */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <PaletteIcon fontSize="small" />
              Color Primario
            </Typography>
            <Grid container spacing={1}>
              {colorOptions.map((color) => (
                <Grid item xs={6} key={color.value}>
                  <Paper
                    sx={{
                      p: 1,
                      cursor: 'pointer',
                      border: preferences.primaryColor === color.value ? 2 : 1,
                      borderColor: preferences.primaryColor === color.value ? color.value : 'divider',
                      '&:hover': { bgcolor: 'action.hover' },
                    }}
                    onClick={() => handlePreferenceChange('primaryColor', color.value)}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box
                        sx={{
                          width: 20,
                          height: 20,
                          borderRadius: '50%',
                          bgcolor: color.value,
                        }}
                      />
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {color.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {color.description}
                        </Typography>
                      </Box>
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Grid>

          {/* Tamaño de Fuente */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <FontSizeIcon fontSize="small" />
              Tamaño de Fuente
            </Typography>
            <Box sx={{ px: 2 }}>
              <Slider
                value={preferences.fontSize}
                onChange={(_, value) => handlePreferenceChange('fontSize', value)}
                min={12}
                max={18}
                step={1}
                marks={[
                  { value: 12, label: 'Pequeño' },
                  { value: 14, label: 'Normal' },
                  { value: 16, label: 'Grande' },
                  { value: 18, label: 'Muy Grande' },
                ]}
                valueLabelDisplay="auto"
                valueLabelFormat={(value) => `${value}px`}
              />
            </Box>
          </Grid>

          {/* Opciones de Accesibilidad */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <AccessibilityIcon fontSize="small" />
              Accesibilidad
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={preferences.highContrast}
                    onChange={(e) => handlePreferenceChange('highContrast', e.target.checked)}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2">Alto Contraste</Typography>
                    <Typography variant="caption" color="text.secondary">
                      Mejora la legibilidad con mayor contraste
                    </Typography>
                  </Box>
                }
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={preferences.reducedMotion}
                    onChange={(e) => handlePreferenceChange('reducedMotion', e.target.checked)}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2">Movimiento Reducido</Typography>
                    <Typography variant="caption" color="text.secondary">
                      Reduce animaciones y transiciones
                    </Typography>
                  </Box>
                }
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={preferences.compactMode}
                    onChange={(e) => handlePreferenceChange('compactMode', e.target.checked)}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2">Modo Compacto</Typography>
                    <Typography variant="caption" color="text.secondary">
                      Interfaz más densa con menos espaciado
                    </Typography>
                  </Box>
                }
              />
            </Box>
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            onClick={savePreferences}
            disabled={saving}
            sx={{
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            }}
          >
            {saving ? 'Guardando...' : 'Guardar Configuración'}
          </Button>
          
          <Button
            variant="outlined"
            onClick={resetToDefaults}
          >
            Restaurar Predeterminados
          </Button>
        </Box>

        <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            ℹ️ Información sobre temas:
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • Los cambios se aplican inmediatamente<br/>
            • La configuración se guarda en tu navegador<br/>
            • El modo automático sigue las preferencias del sistema<br/>
            • Las opciones de accesibilidad mejoran la usabilidad
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ThemeSettings;
