import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Chip,
  Button,
  Collapse,
  IconButton,
  Grid,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { getCacheStats, invalidateAnalyticsCache } from '../../services/analyticsService';

interface CacheStatsPanelProps {
  show?: boolean;
}

const CacheStatsPanel: React.FC<CacheStatsPanelProps> = ({ show = false }) => {
  const [expanded, setExpanded] = useState(false);
  const [stats, setStats] = useState(getCacheStats());

  const refreshStats = () => {
    setStats(getCacheStats());
  };

  const clearCache = () => {
    invalidateAnalyticsCache();
    setStats(getCacheStats());
  };

  const getHitRateColor = (hitRate: number) => {
    if (hitRate >= 0.8) return 'success';
    if (hitRate >= 0.6) return 'warning';
    return 'error';
  };

  const getHitRateText = (hitRate: number) => {
    return `${(hitRate * 100).toFixed(1)}%`;
  };

  if (!show && process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <Box sx={{ position: 'fixed', bottom: 16, right: 16, zIndex: 1000 }}>
      <Card sx={{ minWidth: 300, maxWidth: 400 }}>
        <CardContent sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6" component="div" sx={{ fontSize: '0.9rem' }}>
              📊 Cache Stats
            </Typography>
            <Box>
              <IconButton size="small" onClick={refreshStats}>
                <RefreshIcon fontSize="small" />
              </IconButton>
              <IconButton size="small" onClick={clearCache}>
                <DeleteIcon fontSize="small" />
              </IconButton>
              <IconButton
                size="small"
                onClick={() => setExpanded(!expanded)}
                sx={{
                  transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: 'transform 0.3s',
                }}
              >
                <ExpandMoreIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>

          <Grid container spacing={1} sx={{ mt: 1 }}>
            <Grid item xs={6}>
              <Chip
                label={`Entradas: ${stats.totalEntries}`}
                size="small"
                variant="outlined"
                color="primary"
              />
            </Grid>
            <Grid item xs={6}>
              <Chip
                label={`Hit Rate: ${getHitRateText(stats.hitRate)}`}
                size="small"
                variant="outlined"
                color={getHitRateColor(stats.hitRate)}
              />
            </Grid>
          </Grid>

          <Collapse in={expanded}>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Detalles del Caché
              </Typography>
              
              <Grid container spacing={1}>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 1, bgcolor: '#e3f2fd', borderRadius: 1 }}>
                    <Typography variant="h6" color="primary">
                      {stats.validEntries}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Válidas
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 1, bgcolor: '#fff3e0', borderRadius: 1 }}>
                    <Typography variant="h6" sx={{ color: '#f57c00' }}>
                      {stats.expiredEntries}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Expiradas
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 1, bgcolor: '#e8f5e8', borderRadius: 1 }}>
                    <Typography variant="h6" sx={{ color: '#2e7d32' }}>
                      {stats.hits}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Hits
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 1, bgcolor: '#ffebee', borderRadius: 1 }}>
                    <Typography variant="h6" sx={{ color: '#d32f2f' }}>
                      {stats.misses}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Misses
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Rendimiento
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip
                    label={`Total: ${stats.totalEntries}`}
                    size="small"
                    variant="outlined"
                  />
                  <Chip
                    label={`Hit Rate: ${getHitRateText(stats.hitRate)}`}
                    size="small"
                    variant="outlined"
                    color={getHitRateColor(stats.hitRate)}
                  />
                  {stats.storageSize && (
                    <Chip
                      label={`Tamaño: ${stats.storageSize}`}
                      size="small"
                      variant="outlined"
                      color="info"
                    />
                  )}
                </Box>
              </Box>

              <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={refreshStats}
                  startIcon={<RefreshIcon />}
                >
                  Actualizar
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  color="warning"
                  onClick={clearCache}
                  startIcon={<DeleteIcon />}
                >
                  Limpiar
                </Button>
              </Box>

              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                💡 El caché mejora el rendimiento almacenando consultas frecuentes.
                Las entradas expiran automáticamente según su TTL.
              </Typography>
            </Box>
          </Collapse>
        </CardContent>
      </Card>
    </Box>
  );
};

export default CacheStatsPanel;
