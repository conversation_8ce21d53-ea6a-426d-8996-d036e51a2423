import { useState, useEffect, useCallback, useMemo } from 'react';
import { reportsService, Report } from '../services/reports';
import { addColumnsToReportsTable } from '../utils/addColumnsToReports';
import { generateReportPDF, PDF_PRESETS } from '../utils/pdfGenerator';
import { useNotify } from '../stores/useUIStore';

/**
 * Configuración para filtros de reportes
 */
export interface ReportFilters {
  searchTerm?: string;
  dateFrom?: string;
  dateTo?: string;
  patientName?: string;
  status?: string;
}

/**
 * Configuración para paginación
 */
export interface PaginationConfig {
  page: number;
  pageSize: number;
  total: number;
}

/**
 * Estado del hook useReports
 */
export interface ReportsState {
  // Datos
  reports: Report[];
  filteredReports: Report[];
  selectedReport: Report | null;

  // Estados de carga
  loading: boolean;
  error: string | null;
  tableStructureError: string | null;

  // UI State
  dialogOpen: boolean;
  showDatabaseSetup: boolean;

  // Filtros y paginación
  filters: ReportFilters;
  pagination: PaginationConfig;
}

/**
 * Acciones disponibles en el hook useReports
 */
export interface ReportsActions {
  // Operaciones CRUD
  loadReports: () => Promise<void>;
  deleteReport: (reportId: string) => Promise<void>;
  refreshReports: () => Promise<void>;

  // Gestión de selección
  selectReport: (report: Report | null) => void;
  openReportDialog: (report: Report) => void;
  closeReportDialog: () => void;

  // Filtrado y búsqueda
  setFilters: (filters: Partial<ReportFilters>) => void;
  clearFilters: () => void;
  searchReports: (searchTerm: string) => void;

  // Paginación
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;

  // Utilidades
  generatePDF: (report: Report) => Promise<void>;
  calculateAge: (birthDate: string) => number;
  formatDate: (dateString: string) => string;

  // Gestión de errores
  clearError: () => void;
  clearTableStructureError: () => void;

  // Gestión de UI
  closeDatabaseSetup: () => void;
}

/**
 * Hook personalizado para gestión completa de reportes MACI-II
 */
export const useReports = () => {
  const notify = useNotify();

  // Estado principal
  const [state, setState] = useState<ReportsState>({
    reports: [],
    filteredReports: [],
    selectedReport: null,
    loading: true,
    error: null,
    tableStructureError: null,
    dialogOpen: false,
    showDatabaseSetup: false,
    filters: {},
    pagination: {
      page: 0,
      pageSize: 10,
      total: 0,
    },
  });

  // Función para actualizar estado de forma inmutable
  const updateState = useCallback((updates: Partial<ReportsState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  }, []);

  // Verificar estructura de la tabla
  const checkTableStructure = useCallback(async () => {
    try {
      const columnResult = await addColumnsToReportsTable();
      if (!columnResult.success && columnResult.needsManualFix) {
        updateState({ tableStructureError: columnResult.message });
        return;
      }

      const data = await reportsService.getReports();
      if (data && data.length > 0) {
        const firstReport = data[0];
        if (
          !('scores' in firstReport) ||
          !('converted_scores' in firstReport)
        ) {
          updateState({
            tableStructureError:
              'Los informes existentes no tienen los datos de puntuaciones. Es necesario regenerar los informes.',
          });
        }
      }
    } catch (error) {
      console.error('Error checking table structure:', error);
      updateState({
        tableStructureError:
          'Error al verificar la estructura de la tabla de informes.',
      });
    }
  }, [updateState]);

  // Cargar reportes
  const loadReports = useCallback(async () => {
    try {
      updateState({ loading: true, error: null });
      const data = await reportsService.getReports();
      updateState({
        reports: data,
        filteredReports: data,
        pagination: { ...state.pagination, total: data.length },
      });
    } catch (err: any) {
      console.error('Error loading reports:', err);
      if (
        err.message?.includes('no such table') ||
        err.message?.includes('database')
      ) {
        updateState({ showDatabaseSetup: true });
      } else {
        updateState({ error: 'Error al cargar los informes: ' + err.message });
        notify.error('Error al cargar los informes');
      }
    } finally {
      updateState({ loading: false });
    }
  }, [updateState, notify, state.pagination]);

  // Eliminar reporte
  const deleteReport = useCallback(
    async (reportId: string) => {
      try {
        await reportsService.deleteReport(reportId);
        const updatedReports = state.reports.filter((r) => r.id !== reportId);
        updateState({
          reports: updatedReports,
          filteredReports: updatedReports.filter((report) =>
            applyFilters(report, state.filters),
          ),
        });
        notify.success('Informe eliminado correctamente');
      } catch (err: any) {
        const errorMessage = 'Error al eliminar el informe: ' + err.message;
        updateState({ error: errorMessage });
        notify.error(errorMessage);
      }
    },
    [state.reports, state.filters, updateState, notify],
  );

  // Aplicar filtros a los reportes
  const applyFilters = useCallback(
    (report: Report, filters: ReportFilters): boolean => {
      if (filters.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase();
        const patientName = (
          report.patient?.name ||
          `${report.patient?.first_name || ''} ${report.patient?.last_name || ''}`.trim() ||
          'Paciente'
        ).toLowerCase();

        if (
          !patientName.includes(searchLower) &&
          !report.title.toLowerCase().includes(searchLower) &&
          !report.id.toString().includes(searchLower)
        ) {
          return false;
        }
      }

      if (filters.patientName) {
        const patientName = (
          report.patient?.name ||
          `${report.patient?.first_name || ''} ${report.patient?.last_name || ''}`.trim() ||
          'Paciente'
        ).toLowerCase();
        if (!patientName.includes(filters.patientName.toLowerCase())) {
          return false;
        }
      }

      if (filters.dateFrom) {
        const reportDate = new Date(report.created_at);
        const fromDate = new Date(filters.dateFrom);
        if (reportDate < fromDate) return false;
      }

      if (filters.dateTo) {
        const reportDate = new Date(report.created_at);
        const toDate = new Date(filters.dateTo);
        if (reportDate > toDate) return false;
      }

      return true;
    },
    [],
  );

  // Filtrar reportes cuando cambien los filtros
  const filteredReports = useMemo(() => {
    return state.reports.filter((report) =>
      applyFilters(report, state.filters),
    );
  }, [state.reports, state.filters, applyFilters]);

  // Reportes paginados
  const paginatedReports = useMemo(() => {
    const startIndex = state.pagination.page * state.pagination.pageSize;
    const endIndex = startIndex + state.pagination.pageSize;
    return filteredReports.slice(startIndex, endIndex);
  }, [filteredReports, state.pagination]);

  // Acciones
  const actions: ReportsActions = {
    // CRUD Operations
    loadReports,
    deleteReport,
    refreshReports: loadReports,

    // Selection Management
    selectReport: useCallback(
      (report: Report | null) => {
        updateState({ selectedReport: report });
      },
      [updateState],
    ),

    openReportDialog: useCallback(
      (report: Report) => {
        updateState({ selectedReport: report, dialogOpen: true });
      },
      [updateState],
    ),

    closeReportDialog: useCallback(() => {
      updateState({ selectedReport: null, dialogOpen: false });
    }, [updateState]),

    // Filtering and Search
    setFilters: useCallback(
      (newFilters: Partial<ReportFilters>) => {
        const updatedFilters = { ...state.filters, ...newFilters };
        updateState({
          filters: updatedFilters,
          pagination: { ...state.pagination, page: 0 }, // Reset to first page
        });
      },
      [state.filters, state.pagination, updateState],
    ),

    clearFilters: useCallback(() => {
      updateState({
        filters: {},
        pagination: { ...state.pagination, page: 0 },
      });
    }, [state.pagination, updateState]),

    searchReports: useCallback(
      (searchTerm: string) => {
        updateState({
          filters: { ...state.filters, searchTerm },
          pagination: { ...state.pagination, page: 0 },
        });
      },
      [state.filters, state.pagination, updateState],
    ),

    // Pagination
    setPage: useCallback(
      (page: number) => {
        updateState({ pagination: { ...state.pagination, page } });
      },
      [state.pagination, updateState],
    ),

    setPageSize: useCallback(
      (pageSize: number) => {
        updateState({
          pagination: { ...state.pagination, pageSize, page: 0 },
        });
      },
      [state.pagination, updateState],
    ),

    // Utilities
    generatePDF: useCallback(
      async (report: Report) => {
        try {
          await generateReportPDF(report, PDF_PRESETS.maciReport);
          notify.success('PDF generado correctamente');
        } catch (error) {
          console.error('Error generating PDF:', error);
          notify.error('Error al generar el PDF');
        }
      },
      [notify],
    ),

    calculateAge: useCallback((birthDate: string): number => {
      if (!birthDate) return 0;
      const today = new Date();
      const birth = new Date(birthDate);
      let age = today.getFullYear() - birth.getFullYear();
      const monthDiff = today.getMonth() - birth.getMonth();
      if (
        monthDiff < 0 ||
        (monthDiff === 0 && today.getDate() < birth.getDate())
      ) {
        age--;
      }
      return age;
    }, []),

    formatDate: useCallback((dateString: string): string => {
      return new Date(dateString).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    }, []),

    // Error Management
    clearError: useCallback(() => {
      updateState({ error: null });
    }, [updateState]),

    clearTableStructureError: useCallback(() => {
      updateState({ tableStructureError: null });
    }, [updateState]),

    closeDatabaseSetup: useCallback(() => {
      updateState({ showDatabaseSetup: false });
    }, [updateState]),
  };

  // Efectos
  useEffect(() => {
    loadReports();
    checkTableStructure();
  }, []);

  // Actualizar filteredReports cuando cambien los reportes o filtros
  useEffect(() => {
    updateState({
      filteredReports,
      pagination: { ...state.pagination, total: filteredReports.length },
    });
  }, [filteredReports, updateState]);

  return {
    // Estado
    ...state,
    filteredReports,
    paginatedReports,

    // Acciones
    ...actions,
  };
};
