import { useEffect, useRef, useState, useCallback } from 'react';
import { supabase } from '../lib/supabaseClient';
import { useAuth } from './useAuth';

interface AutoSaveOptions {
  interval?: number; // Intervalo en milisegundos (default: 30000 = 30 segundos)
  enabled?: boolean; // Si el guardado automático está habilitado
  onSave?: (success: boolean, error?: string) => void; // Callback cuando se guarda
  onError?: (error: string) => void; // Callback cuando hay error
}

interface AutoSaveState {
  isSaving: boolean;
  lastSaved: Date | null;
  saveCount: number;
  error: string | null;
}

export const useAutoSave = (
  data: any,
  saveFunction: (data: any) => Promise<void>,
  options: AutoSaveOptions = {}
) => {
  const {
    interval = 30000, // 30 segundos por defecto
    enabled = true,
    onSave,
    onError,
  } = options;

  const [state, setState] = useState<AutoSaveState>({
    isSaving: false,
    lastSaved: null,
    saveCount: 0,
    error: null,
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastDataRef = useRef<string>('');
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Función para guardar datos
  const save = useCallback(async () => {
    if (!enabled || state.isSaving) return;

    const currentDataString = JSON.stringify(data);
    
    // No guardar si los datos no han cambiado
    if (currentDataString === lastDataRef.current) {
      return;
    }

    setState(prev => ({ ...prev, isSaving: true, error: null }));

    try {
      await saveFunction(data);
      
      setState(prev => ({
        ...prev,
        isSaving: false,
        lastSaved: new Date(),
        saveCount: prev.saveCount + 1,
        error: null,
      }));

      lastDataRef.current = currentDataString;
      onSave?.(true);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      
      setState(prev => ({
        ...prev,
        isSaving: false,
        error: errorMessage,
      }));

      onSave?.(false, errorMessage);
      onError?.(errorMessage);
    }
  }, [data, saveFunction, enabled, state.isSaving, onSave, onError]);

  // Función para guardar inmediatamente (debounced)
  const saveNow = useCallback(() => {
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }
    
    saveTimeoutRef.current = setTimeout(() => {
      save();
    }, 1000); // Debounce de 1 segundo
  }, [save]);

  // Configurar intervalo de guardado automático
  useEffect(() => {
    if (!enabled) return;

    intervalRef.current = setInterval(() => {
      save();
    }, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [save, interval, enabled]);

  // Limpiar timeouts al desmontar
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Guardar cuando los datos cambien (debounced)
  useEffect(() => {
    if (enabled && data) {
      saveNow();
    }
  }, [data, enabled, saveNow]);

  return {
    ...state,
    save: saveNow,
    forceSync: save, // Guardar inmediatamente sin debounce
  };
};

// Hook específico para cuestionarios
export const useQuestionnaireAutoSave = (
  patientId: string,
  questionnaireId: string,
  responses: any[],
  options: Omit<AutoSaveOptions, 'saveFunction'> = {}
) => {
  const { user } = useAuth();

  const saveQuestionnaireResponses = useCallback(async (data: any[]) => {
    if (!user || !patientId || !questionnaireId) {
      throw new Error('Faltan datos requeridos para guardar');
    }

    // Filtrar solo las respuestas que no son null
    const validResponses = data
      .map((answer, index) => ({
        question_id: index + 1,
        answer: answer,
      }))
      .filter(response => response.answer !== null);

    if (validResponses.length === 0) {
      return; // No hay nada que guardar
    }

    // Preparar datos para Supabase
    const responseData = {
      patient_id: patientId,
      questionnaire_id: questionnaireId,
      responses: validResponses,
      status: 'en_progreso' as const,
      updated_at: new Date().toISOString(),
    };

    // Intentar actualizar primero, si no existe, crear
    const { data: existingResponse } = await supabase
      .from('questionnaire_responses')
      .select('id')
      .eq('patient_id', patientId)
      .eq('questionnaire_id', questionnaireId)
      .single();

    if (existingResponse) {
      // Actualizar respuesta existente
      const { error } = await supabase
        .from('questionnaire_responses')
        .update({
          responses: responseData.responses,
          status: responseData.status,
          updated_at: responseData.updated_at,
        })
        .eq('id', existingResponse.id);

      if (error) throw error;
    } else {
      // Crear nueva respuesta
      const { error } = await supabase
        .from('questionnaire_responses')
        .insert(responseData);

      if (error) throw error;
    }
  }, [user, patientId, questionnaireId]);

  return useAutoSave(responses, saveQuestionnaireResponses, options);
};

export default useAutoSave;
