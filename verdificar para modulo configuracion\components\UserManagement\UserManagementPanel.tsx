import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Fab,
  Divider,
} from '@mui/material';
import {
  PersonAdd as PersonAddIcon,
  Refresh as RefreshIcon,
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  PersonOutline as PersonOutlineIcon,
  AdminPanelSettings as AdminIcon,
  Block as BlockIcon,
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import { useUserManagement } from '../../hooks/useUserManagement';
import { User } from '../../services/userManagement';
import { ProfessionalCreateUserDialog } from './ProfessionalCreateUserDialog';
import { ProfessionalUserTable } from './ProfessionalUserTable';
import { ModuleWrapper, OptimizedModuleWrapper } from '../Admin/ModuleFallback';
import { BulkActionsToolbar } from './BulkActionsToolbar';
import { useBulkUserActions } from '../../hooks/useBulkUserActions';
import { useNotifier } from '../../hooks/useNotifier';

export const UserManagementPanel: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const {
    users,
    loading,
    error,
    updateUserRole,
    updateUserStatus,
    refreshData,
    deleteUser,
    createUser,
    cacheInfo,
  } = useUserManagement();

  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const { bulkRoleChange, bulkStatusChange, bulkDelete } = useBulkUserActions();
  const { showSuccess, showError } = useNotifier();

  // 🔐 CONTROL DE ACCESO: Solo administradores
  useEffect(() => {
    if (user && user.role !== 'administrador') {
      console.warn('🚫 Acceso denegado: Usuario no es administrador');
      navigate('/dashboard'); // Redirigir a dashboard
    }
  }, [user, navigate]);

  // Si no es administrador, mostrar mensaje de acceso denegado
  if (user && user.role !== 'administrador') {
    return (
      <ModuleWrapper loading={false} error={null}>
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <BlockIcon sx={{ fontSize: 80, color: 'error.main', mb: 2 }} />
          <Typography variant="h4" gutterBottom color="error">
            Acceso Denegado
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Solo los administradores pueden acceder al módulo de Gestión de Usuarios.
          </Typography>
          <Button
            variant="contained"
            onClick={() => navigate('/dashboard')}
            sx={{ mt: 2 }}
          >
            Volver al Dashboard
          </Button>
        </Box>
      </ModuleWrapper>
    );
  }

  const getUserStats = () => {
    if (!users)
      return { total: 0, active: 0, admins: 0, psychologists: 0, patients: 0 };

    return users.reduce(
      (acc, user) => {
        acc.total++;
        if (user.is_active) acc.active++;
        if (user.role === 'administrador') acc.admins++;
        if (user.role === 'psicologo') acc.psychologists++;
        if (user.role === 'paciente') acc.patients++;
        return acc;
      },
      { total: 0, active: 0, admins: 0, psychologists: 0, patients: 0 },
    );
  };

  const stats = getUserStats();

  const handleBulkRoleChange = async (userIds: string[], newRole: string) => {
    try {
      await bulkRoleChange(userIds, newRole);
      await refreshData();
      showSuccess(`Rol actualizado para ${userIds.length} usuarios`);
    } catch (error) {
      showError('Error al cambiar roles en lote');
    }
  };

  const handleBulkStatusChange = async (userIds: string[], isActive: boolean) => {
    try {
      await bulkStatusChange(userIds, isActive);
      await refreshData();
      showSuccess(`Estado actualizado para ${userIds.length} usuarios`);
    } catch (error) {
      showError('Error al cambiar estado en lote');
    }
  };

  const handleBulkDelete = async (userIds: string[]) => {
    try {
      await bulkDelete(userIds);
      await refreshData();
      showSuccess(`${userIds.length} usuarios eliminados`);
    } catch (error) {
      showError('Error al eliminar usuarios en lote');
    }
  };

  return (
    <OptimizedModuleWrapper
      loading={loading}
      error={error}
      onRetry={() => refreshData(true)} // Forzar refresh
      cacheInfo={cacheInfo?.users}
    >
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography
          variant="h5"
          gutterBottom
          sx={{ fontWeight: 'bold', color: '#011129' }}
        >
          Gestión de Usuarios
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Administra usuarios, roles y permisos del sistema con herramientas
          profesionales
        </Typography>
      </Box>

      {/* Estadísticas */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              textAlign: 'center',
              backgroundColor: 'transparent',
              border: '1px solid',
              borderColor: 'divider',
            }}
          >
            <CardContent>
              <PeopleIcon sx={{ fontSize: 40, mb: 1, color: 'primary.main' }} />
              <Typography variant="h4" fontWeight="bold">
                {stats.total}
              </Typography>
              <Typography variant="body2">Total Usuarios</Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              textAlign: 'center',
              backgroundColor: 'transparent',
              border: '1px solid',
              borderColor: 'divider',
            }}
          >
            <CardContent>
              <TrendingUpIcon sx={{ fontSize: 40, mb: 1, color: 'success.main' }} />
              <Typography variant="h4" fontWeight="bold">
                {stats.active}
              </Typography>
              <Typography variant="body2">Usuarios Activos</Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              textAlign: 'center',
              backgroundColor: 'transparent',
              border: '1px solid',
              borderColor: 'divider',
            }}
          >
            <CardContent>
              <AdminIcon sx={{ fontSize: 40, mb: 1, color: 'info.main' }} />
              <Typography variant="h4" fontWeight="bold">
                {stats.admins}
              </Typography>
              <Typography variant="body2">Administradores</Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              textAlign: 'center',
              backgroundColor: 'transparent',
              border: '1px solid',
              borderColor: 'divider',
            }}
          >
            <CardContent>
              <PersonOutlineIcon sx={{ fontSize: 40, mb: 1, color: 'warning.main' }} />
              <Typography variant="h4" fontWeight="bold">
                {stats.psychologists + stats.patients}
              </Typography>
              <Typography variant="body2">Psicólogos y Pacientes</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Divider sx={{ mb: 3 }} />

      {/* Botón flotante para crear usuario */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          backgroundColor: '#011129',
          '&:hover': { backgroundColor: '#022247' },
        }}
        onClick={() => setCreateDialogOpen(true)}
      >
        <PersonAddIcon />
      </Fab>

      {/* Acciones en Lote */}
      <BulkActionsToolbar
        selectedUsers={selectedUsers}
        onClearSelection={() => setSelectedUsers([])}
        onBulkRoleChange={handleBulkRoleChange}
        onBulkStatusChange={handleBulkStatusChange}
        onBulkDelete={handleBulkDelete}
      />

      {/* Tabla de Usuarios Profesional */}
      <ProfessionalUserTable
        users={users || []}
        loading={loading}
        onUpdateUserRole={updateUserRole}
        onUpdateUserStatus={updateUserStatus}
        onDeleteUser={deleteUser}
        onRefresh={refreshData}
        selectedUsers={selectedUsers}
        onSelectionChange={setSelectedUsers}
      />

      {/* Dialog para Crear Usuario Profesional */}
      <ProfessionalCreateUserDialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        onUserCreated={async (userData) => {
          await createUser(userData);
          setCreateDialogOpen(false);
          refreshData();
        }}
      />
    </Box>
    </OptimizedModuleWrapper>
  );
};
