import React, { useEffect } from 'react';
import {
  createTheme,
  ThemeProvider as MuiThemeProvider,
} from '@mui/material/styles';
import { useUIStore } from '../../stores/useUIStore';
import { getDesignTokens } from '../../theme';

interface UnifiedThemeProviderProps {
  children: React.ReactNode;
}

/**
 * Proveedor de tema unificado que usa useUIStore
 * Reemplaza el ThemeContext.jsx anterior
 */
export const UnifiedThemeProvider: React.FC<UnifiedThemeProviderProps> = ({
  children,
}) => {
  const { theme: themeMode } = useUIStore();

  // Aplicar atributos CSS para el tema
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', themeMode);
    document.documentElement.style.colorScheme = themeMode;
  }, [themeMode]);

  // Crear tema de Material-UI basado en el modo actual
  const muiTheme = React.useMemo(() => {
    return createTheme(getDesignTokens(themeMode));
  }, [themeMode]);

  return <MuiThemeProvider theme={muiTheme}>{children}</MuiThemeProvider>;
};

export default UnifiedThemeProvider;
