import { useState, useCallback } from 'react';

export interface LoadingStates {
  [key: string]: boolean;
}

export interface UseLoadingStatesReturn {
  loadingStates: LoadingStates;
  isLoading: (key: string) => boolean;
  setLoading: (key: string, loading: boolean) => void;
  startLoading: (key: string) => void;
  stopLoading: (key: string) => void;
  resetLoading: () => void;
}

/**
 * Hook personalizado para gestionar múltiples estados de carga
 * Permite tener estados de carga granulares para diferentes operaciones
 */
export const useLoadingStates = (
  initialStates: LoadingStates = {},
): UseLoadingStatesReturn => {
  const [loadingStates, setLoadingStates] =
    useState<LoadingStates>(initialStates);

  const isLoading = useCallback(
    (key: string): boolean => {
      return loadingStates[key] || false;
    },
    [loadingStates],
  );

  const setLoading = useCallback((key: string, loading: boolean) => {
    setLoadingStates((prev) => ({
      ...prev,
      [key]: loading,
    }));
  }, []);

  const startLoading = useCallback(
    (key: string) => {
      setLoading(key, true);
    },
    [setLoading],
  );

  const stopLoading = useCallback(
    (key: string) => {
      setLoading(key, false);
    },
    [setLoading],
  );

  const resetLoading = useCallback(() => {
    setLoadingStates({});
  }, []);

  return {
    loadingStates,
    isLoading,
    setLoading,
    startLoading,
    stopLoading,
    resetLoading,
  };
};
