import React from 'react';
import { Box, Typography, TextField, Button, Paper } from '@mui/material';

interface PatientSearchBoxProps {
  onSearch: (searchTerm: string) => void;
}

const PatientSearchBox: React.FC<PatientSearchBoxProps> = ({ onSearch }) => {
  const [searchTerm, setSearchTerm] = React.useState('');

  const handleSearch = () => {
    onSearch(searchTerm);
  };

  return (
    <Paper
      elevation={3}
      sx={{
        width: '80%',
        p: 3,
        borderRadius: '16px',
        backgroundColor: 'rgba(46, 125, 50, 0.15)',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        textAlign: 'center',
        border: '1px solid rgba(46, 125, 50, 0.2)',
        margin: '0 auto',
      }}
    >
      <Typography
        variant="h6"
        component="h2"
        sx={{ fontWeight: 600, color: 'primary.main', mb: 2 }}
      >
        Select a patient to begin the MACI-II questionnaire
      </Typography>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: '1px solid #ccc',
          paddingBottom: '0.5rem',
          mb: 2,
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{ fontWeight: 500, color: 'text.secondary' }}
        >
          Search patient by name
        </Typography>
      </Box>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <TextField
          placeholder="Enter patient name..."
          variant="outlined"
          size="small"
          sx={{ width: '70%' }}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <Button variant="contained" color="primary" onClick={handleSearch}>
          Search
        </Button>
      </Box>
    </Paper>
  );
};

export default PatientSearchBox;
