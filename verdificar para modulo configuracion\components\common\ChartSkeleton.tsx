import React from 'react';
import { Box, Skeleton, Typography, Card, CardContent, CircularProgress } from '@mui/material';

interface ChartSkeletonProps {
  height?: number;
  title?: string;
  showLegend?: boolean;
  variant?: 'line' | 'bar' | 'radar' | 'pie';
}

// Constants to replace magic numbers
const SKELETON_DIMENSIONS = {
  TITLE_WIDTH: '60%',
  SUBTITLE_WIDTH: '80%',
  LEGEND_ITEM_WIDTH: 60,
  LEGEND_ITEM_HEIGHT: 16,
  LEGEND_ICON_SIZE: 12,
};

const BAR_CHART_DEFAULTS = {
  BAR_COUNT: 6,
  BAR_WIDTH_PERCENT: '15%',
  LABEL_WIDTH_PERCENT: '12%',
  LABEL_HEIGHT: 20,
  MIN_BAR_HEIGHT_PERCENT: 40,
  RANDOM_HEIGHT_FACTOR: 60,
};

const LINE_CHART_DEFAULTS = {
  LABEL_COUNT: 5,
  LA<PERSON>L_WIDTH_PERCENT: '15%',
  LABEL_HEIGHT: 20,
};

const RADIAL_CHART_DEFAULTS = {
  RADAR_SCALE: 0.8,
  PIE_SCALE: 0.7,
};

const ChartSkeleton: React.FC<ChartSkeletonProps> = ({ 
  height = 300, 
  title = "Cargando gráfico...",
  showLegend = true,
  variant = 'line'
}) => {
  const renderChartSkeleton = () => {
    switch (variant) {
      case 'radar':
        return (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height }}>
            <Skeleton
              variant="circular"
              width={height * RADIAL_CHART_DEFAULTS.RADAR_SCALE}
              height={height * RADIAL_CHART_DEFAULTS.RADAR_SCALE}
            />
          </Box>
        );

      case 'pie':
        return (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height }}>
            <Skeleton
              variant="circular"
              width={height * RADIAL_CHART_DEFAULTS.PIE_SCALE}
              height={height * RADIAL_CHART_DEFAULTS.PIE_SCALE}
            />
          </Box>
        );
      
      case 'bar':
        return (
          <Box sx={{ height, p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'end', height: '80%', gap: 1 }}>
              {Array.from({ length: BAR_CHART_DEFAULTS.BAR_COUNT }).map((_, index) => (
                <Skeleton
                  key={index}
                  variant="rectangular"
                  width={BAR_CHART_DEFAULTS.BAR_WIDTH_PERCENT}
                  height={`${Math.random() * BAR_CHART_DEFAULTS.RANDOM_HEIGHT_FACTOR + BAR_CHART_DEFAULTS.MIN_BAR_HEIGHT_PERCENT}%`}
                  sx={{ borderRadius: 1 }}
                />
              ))}
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 1 }}>
              {Array.from({ length: BAR_CHART_DEFAULTS.BAR_COUNT }).map((_, index) => (
                <Skeleton
                  key={index}
                  variant="text"
                  width={BAR_CHART_DEFAULTS.LABEL_WIDTH_PERCENT}
                  height={BAR_CHART_DEFAULTS.LABEL_HEIGHT}
                />
              ))}
            </Box>
          </Box>
        );
      
      default: // line
        return (
          <Box sx={{ height, p: 2 }}>
            <Box sx={{ position: 'relative', height: '80%' }}>
              <Skeleton
                variant="rectangular"
                width="100%"
                height="100%"
                sx={{ borderRadius: 1 }}
              />
              {/* Simular líneas de datos */}
              <Box sx={{ 
                position: 'absolute', 
                top: '20%', 
                left: '10%', 
                right: '10%', 
                height: '60%',
                display: 'flex',
                alignItems: 'center'
              }}>
                <Skeleton
                  variant="rectangular"
                  width="100%"
                  height="2px"
                  sx={{ 
                    borderRadius: 1,
                    transform: 'rotate(-5deg)',
                    opacity: 0.6
                  }}
                />
              </Box>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
              {Array.from({ length: LINE_CHART_DEFAULTS.LABEL_COUNT }).map((_, index) => (
                <Skeleton
                  key={index}
                  variant="text"
                  width={LINE_CHART_DEFAULTS.LABEL_WIDTH_PERCENT}
                  height={LINE_CHART_DEFAULTS.LABEL_HEIGHT}
                />
              ))}
            </Box>
          </Box>
        );
    }
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            <Skeleton variant="text" width={SKELETON_DIMENSIONS.TITLE_WIDTH} />
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <Skeleton variant="text" width={SKELETON_DIMENSIONS.SUBTITLE_WIDTH} />
          </Typography>
        </Box>
        
        {renderChartSkeleton()}
        
        {showLegend && (
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            gap: 2, 
            mt: 2,
            flexWrap: 'wrap'
          }}>
            {Array.from({ length: 3 }).map((_, index) => (
              <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Skeleton
                  variant="circular"
                  width={SKELETON_DIMENSIONS.LEGEND_ICON_SIZE}
                  height={SKELETON_DIMENSIONS.LEGEND_ICON_SIZE}
                />
                <Skeleton
                  variant="text"
                  width={SKELETON_DIMENSIONS.LEGEND_ITEM_WIDTH}
                  height={SKELETON_DIMENSIONS.LEGEND_ITEM_HEIGHT}
                />
              </Box>
            ))}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

// Simplified exports - use ChartSkeleton with variant prop instead of separate components
// Example: <ChartSkeleton variant="bar" height={300} />

// Compact skeleton for small spaces
export const CompactChartSkeleton: React.FC = () => (
  <Box sx={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: 200,
    flexDirection: 'column',
    gap: 1
  }}>
    <CircularProgress size={24} />
    <Typography variant="body2" color="text.secondary">
      Cargando gráfico...
    </Typography>
  </Box>
);

export default ChartSkeleton;
