import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  Grid,
  Collapse,
  IconButton,
  Divider,
  Autocomplete,
} from '@mui/material';
import {
  FilterList as FilterListIcon,
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CalendarToday as CalendarIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { PatientStatus } from '../../types/patient';

export interface FilterOptions {
  status?: PatientStatus[];
  dateRange?: {
    start?: Date | null;
    end?: Date | null;
  };
  evaluationDateRange?: {
    start?: Date | null;
    end?: Date | null;
  };
  psychologist?: string[];
  ageRange?: {
    min?: number;
    max?: number;
  };
  gender?: string[];
}

interface AdvancedFiltersProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  psychologists?: Array<{ id: string; name: string }>;
  totalFiltersApplied?: number;
}

const statusOptions = [
  { value: 'activo', label: 'Activo', color: '#4caf50' },
  { value: 'inactivo', label: 'Inactivo', color: '#f44336' },
  { value: 'pendiente_evaluacion', label: 'Pendiente Evaluación', color: '#ff9800' },
];

const genderOptions = [
  { value: 'M', label: 'Masculino' },
  { value: 'F', label: 'Femenino' },
  { value: 'O', label: 'Otro' },
];

export const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  filters,
  onFiltersChange,
  psychologists = [],
  totalFiltersApplied = 0,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [localFilters, setLocalFilters] = useState<FilterOptions>(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (key: keyof FilterOptions, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleStatusChange = (selectedStatuses: PatientStatus[]) => {
    handleFilterChange('status', selectedStatuses.length > 0 ? selectedStatuses : undefined);
  };

  const handleGenderChange = (selectedGenders: string[]) => {
    handleFilterChange('gender', selectedGenders.length > 0 ? selectedGenders : undefined);
  };

  const handlePsychologistChange = (selectedPsychologists: string[]) => {
    handleFilterChange('psychologist', selectedPsychologists.length > 0 ? selectedPsychologists : undefined);
  };

  const handleDateRangeChange = (type: 'dateRange' | 'evaluationDateRange', field: 'start' | 'end', value: Date | null) => {
    const currentRange = localFilters[type] || {};
    const newRange = { ...currentRange, [field]: value };
    
    // Si ambos campos están vacíos, eliminar el filtro
    if (!newRange.start && !newRange.end) {
      handleFilterChange(type, undefined);
    } else {
      handleFilterChange(type, newRange);
    }
  };

  const handleAgeRangeChange = (field: 'min' | 'max', value: string) => {
    const numValue = value ? parseInt(value) : undefined;
    const currentRange = localFilters.ageRange || {};
    const newRange = { ...currentRange, [field]: numValue };
    
    // Si ambos campos están vacíos, eliminar el filtro
    if (!newRange.min && !newRange.max) {
      handleFilterChange('ageRange', undefined);
    } else {
      handleFilterChange('ageRange', newRange);
    }
  };

  const clearAllFilters = () => {
    const emptyFilters: FilterOptions = {};
    setLocalFilters(emptyFilters);
    onFiltersChange(emptyFilters);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (localFilters.status?.length) count++;
    if (localFilters.dateRange?.start || localFilters.dateRange?.end) count++;
    if (localFilters.evaluationDateRange?.start || localFilters.evaluationDateRange?.end) count++;
    if (localFilters.psychologist?.length) count++;
    if (localFilters.ageRange?.min || localFilters.ageRange?.max) count++;
    if (localFilters.gender?.length) count++;
    return count;
  };

  const renderFilterChips = () => {
    const chips = [];

    if (localFilters.status?.length) {
      chips.push(
        <Chip
          key="status"
          label={`Estado: ${localFilters.status.map(s => statusOptions.find(opt => opt.value === s)?.label).join(', ')}`}
          onDelete={() => handleFilterChange('status', undefined)}
          size="small"
          color="primary"
          variant="outlined"
        />
      );
    }

    if (localFilters.gender?.length) {
      chips.push(
        <Chip
          key="gender"
          label={`Género: ${localFilters.gender.map(g => genderOptions.find(opt => opt.value === g)?.label).join(', ')}`}
          onDelete={() => handleFilterChange('gender', undefined)}
          size="small"
          color="primary"
          variant="outlined"
        />
      );
    }

    if (localFilters.psychologist?.length) {
      chips.push(
        <Chip
          key="psychologist"
          label={`Psicólogo: ${localFilters.psychologist.length} seleccionado(s)`}
          onDelete={() => handleFilterChange('psychologist', undefined)}
          size="small"
          color="primary"
          variant="outlined"
        />
      );
    }

    if (localFilters.dateRange?.start || localFilters.dateRange?.end) {
      chips.push(
        <Chip
          key="dateRange"
          label="Fecha de Registro"
          onDelete={() => handleFilterChange('dateRange', undefined)}
          size="small"
          color="primary"
          variant="outlined"
        />
      );
    }

    if (localFilters.evaluationDateRange?.start || localFilters.evaluationDateRange?.end) {
      chips.push(
        <Chip
          key="evaluationDateRange"
          label="Fecha de Evaluación"
          onDelete={() => handleFilterChange('evaluationDateRange', undefined)}
          size="small"
          color="primary"
          variant="outlined"
        />
      );
    }

    if (localFilters.ageRange?.min || localFilters.ageRange?.max) {
      chips.push(
        <Chip
          key="ageRange"
          label={`Edad: ${localFilters.ageRange?.min || 0}-${localFilters.ageRange?.max || '∞'}`}
          onDelete={() => handleFilterChange('ageRange', undefined)}
          size="small"
          color="primary"
          variant="outlined"
        />
      );
    }

    return chips;
  };

  return (
    <Paper elevation={1} sx={{ mb: 3, overflow: 'hidden' }}>
        {/* Header del filtro */}
        <Box
          sx={{
            p: 2,
            backgroundColor: '#f8f9fa',
            borderBottom: '1px solid #e9ecef',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            cursor: 'pointer',
          }}
          onClick={() => setExpanded(!expanded)}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <FilterListIcon sx={{ color: '#3498db' }} />
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
              Filtros Avanzados
            </Typography>
            {getActiveFiltersCount() > 0 && (
              <Chip
                label={`${getActiveFiltersCount()} activo(s)`}
                size="small"
                color="primary"
                sx={{ fontWeight: 600 }}
              />
            )}
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {getActiveFiltersCount() > 0 && (
              <Button
                size="small"
                startIcon={<ClearIcon />}
                onClick={(e) => {
                  e.stopPropagation();
                  clearAllFilters();
                }}
                sx={{ textTransform: 'none' }}
              >
                Limpiar
              </Button>
            )}
            <IconButton size="small">
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
        </Box>

        {/* Chips de filtros activos */}
        {getActiveFiltersCount() > 0 && (
          <Box sx={{ p: 2, backgroundColor: '#f8f9fa', borderBottom: '1px solid #e9ecef' }}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {renderFilterChips()}
            </Box>
          </Box>
        )}

        {/* Panel de filtros expandible */}
        <Collapse in={expanded}>
          <Box sx={{ p: 3 }}>
            <Grid container spacing={3}>
              {/* Estado del paciente */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Estado del Paciente</InputLabel>
                  <Select
                    multiple
                    value={localFilters.status || []}
                    onChange={(e) => handleStatusChange(e.target.value as PatientStatus[])}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {(selected as PatientStatus[]).map((value) => {
                          const option = statusOptions.find(opt => opt.value === value);
                          return (
                            <Chip
                              key={value}
                              label={option?.label}
                              size="small"
                              sx={{ backgroundColor: option?.color, color: 'white' }}
                            />
                          );
                        })}
                      </Box>
                    )}
                  >
                    {statusOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              borderRadius: '50%',
                              backgroundColor: option.color,
                            }}
                          />
                          {option.label}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Género */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Género</InputLabel>
                  <Select
                    multiple
                    value={localFilters.gender || []}
                    onChange={(e) => handleGenderChange(e.target.value as string[])}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {(selected as string[]).map((value) => {
                          const option = genderOptions.find(opt => opt.value === value);
                          return <Chip key={value} label={option?.label} size="small" />;
                        })}
                      </Box>
                    )}
                  >
                    {genderOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Psicólogo asignado */}
              {psychologists.length > 0 && (
                <Grid item xs={12}>
                  <Autocomplete
                    multiple
                    options={psychologists}
                    getOptionLabel={(option) => option.name}
                    value={psychologists.filter(p => localFilters.psychologist?.includes(p.id)) || []}
                    onChange={(_, newValue) => {
                      handlePsychologistChange(newValue.map(p => p.id));
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Psicólogo Asignado"
                        placeholder="Seleccionar psicólogos..."
                      />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          variant="outlined"
                          label={option.name}
                          {...getTagProps({ index })}
                          key={option.id}
                        />
                      ))
                    }
                  />
                </Grid>
              )}

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600, color: '#2c3e50' }}>
                  Rangos de Fechas
                </Typography>
              </Grid>

              {/* Fecha de registro */}
              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
                  Fecha de Registro
                </Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <DatePicker
                    label="Desde"
                    value={localFilters.dateRange?.start || null}
                    onChange={(value) => handleDateRangeChange('dateRange', 'start', value)}
                    slotProps={{ textField: { size: 'small', fullWidth: true } }}
                  />
                  <DatePicker
                    label="Hasta"
                    value={localFilters.dateRange?.end || null}
                    onChange={(value) => handleDateRangeChange('dateRange', 'end', value)}
                    slotProps={{ textField: { size: 'small', fullWidth: true } }}
                  />
                </Box>
              </Grid>

              {/* Fecha de evaluación */}
              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
                  Fecha de Última Evaluación
                </Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <DatePicker
                    label="Desde"
                    value={localFilters.evaluationDateRange?.start || null}
                    onChange={(value) => handleDateRangeChange('evaluationDateRange', 'start', value)}
                    slotProps={{ textField: { size: 'small', fullWidth: true } }}
                  />
                  <DatePicker
                    label="Hasta"
                    value={localFilters.evaluationDateRange?.end || null}
                    onChange={(value) => handleDateRangeChange('evaluationDateRange', 'end', value)}
                    slotProps={{ textField: { size: 'small', fullWidth: true } }}
                  />
                </Box>
              </Grid>

              {/* Rango de edad */}
              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
                  Rango de Edad
                </Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <TextField
                    label="Edad mínima"
                    type="number"
                    size="small"
                    fullWidth
                    value={localFilters.ageRange?.min || ''}
                    onChange={(e) => handleAgeRangeChange('min', e.target.value)}
                    inputProps={{ min: 0, max: 120 }}
                  />
                  <TextField
                    label="Edad máxima"
                    type="number"
                    size="small"
                    fullWidth
                    value={localFilters.ageRange?.max || ''}
                    onChange={(e) => handleAgeRangeChange('max', e.target.value)}
                    inputProps={{ min: 0, max: 120 }}
                  />
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Collapse>
      </Paper>
  );
};
