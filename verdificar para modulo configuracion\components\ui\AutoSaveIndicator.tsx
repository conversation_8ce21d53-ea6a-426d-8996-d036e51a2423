import React from 'react';
import {
  Box,
  Typography,
  Chip,
  Tooltip,
  CircularProgress,
  Fade,
} from '@mui/material';
import {
  CloudDone as SavedIcon,
  CloudSync as SavingIcon,
  CloudOff as ErrorIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

interface AutoSaveIndicatorProps {
  isSaving: boolean;
  lastSaved: Date | null;
  saveCount: number;
  error: string | null;
  variant?: 'chip' | 'inline' | 'minimal';
  showLastSaved?: boolean;
  showSaveCount?: boolean;
}

const StyledChip = styled(Chip)<{ savestatus: string }>(({ theme, savestatus }) => ({
  transition: 'all 0.3s ease-in-out',
  fontWeight: 500,
  ...(savestatus === 'saving' && {
    backgroundColor: 'rgba(90, 146, 200, 0.1)',
    color: '#5A92C8',
    border: '1px solid rgba(90, 146, 200, 0.3)',
  }),
  ...(savestatus === 'saved' && {
    backgroundColor: 'rgba(99, 180, 169, 0.1)',
    color: '#63B4A9',
    border: '1px solid rgba(99, 180, 169, 0.3)',
  }),
  ...(savestatus === 'error' && {
    backgroundColor: 'rgba(242, 138, 124, 0.1)',
    color: '#F28A7C',
    border: '1px solid rgba(242, 138, 124, 0.3)',
  }),
}));

const PulsingIcon = styled(Box)(({ theme }) => ({
  animation: 'pulse 1.5s ease-in-out infinite',
  '@keyframes pulse': {
    '0%': {
      opacity: 1,
    },
    '50%': {
      opacity: 0.5,
    },
    '100%': {
      opacity: 1,
    },
  },
}));

export const AutoSaveIndicator: React.FC<AutoSaveIndicatorProps> = ({
  isSaving,
  lastSaved,
  saveCount,
  error,
  variant = 'chip',
  showLastSaved = true,
  showSaveCount = false,
}) => {
  const formatLastSaved = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'hace unos segundos';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `hace ${minutes} min`;
    } else {
      return date.toLocaleTimeString('es-ES', {
        hour: '2-digit',
        minute: '2-digit',
      });
    }
  };

  const getStatus = () => {
    if (error) return 'error';
    if (isSaving) return 'saving';
    return 'saved';
  };

  const getIcon = () => {
    if (error) return <ErrorIcon fontSize="small" />;
    if (isSaving) return (
      <PulsingIcon>
        <SavingIcon fontSize="small" />
      </PulsingIcon>
    );
    return <SavedIcon fontSize="small" />;
  };

  const getLabel = () => {
    if (error) return 'Error al guardar';
    if (isSaving) return 'Guardando...';
    return 'Guardado';
  };

  const getTooltipContent = () => {
    const parts = [];
    
    if (error) {
      parts.push(`Error: ${error}`);
    } else if (isSaving) {
      parts.push('Guardando progreso automáticamente...');
    } else {
      parts.push('Progreso guardado automáticamente');
    }
    
    if (lastSaved && showLastSaved) {
      parts.push(`Último guardado: ${formatLastSaved(lastSaved)}`);
    }
    
    if (showSaveCount && saveCount > 0) {
      parts.push(`Guardados: ${saveCount}`);
    }
    
    return parts.join('\n');
  };

  if (variant === 'minimal') {
    return (
      <Fade in={isSaving || error !== null}>
        <Tooltip title={getTooltipContent()} arrow>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              color: getStatus() === 'error' ? '#F28A7C' : '#5A92C8',
            }}
          >
            {getIcon()}
          </Box>
        </Tooltip>
      </Fade>
    );
  }

  if (variant === 'inline') {
    return (
      <Box display="flex" alignItems="center" gap={1}>
        {getIcon()}
        <Typography
          variant="caption"
          color={getStatus() === 'error' ? 'error' : 'text.secondary'}
          sx={{ fontWeight: 500 }}
        >
          {getLabel()}
        </Typography>
        {lastSaved && showLastSaved && !isSaving && !error && (
          <Typography variant="caption" color="text.disabled">
            • {formatLastSaved(lastSaved)}
          </Typography>
        )}
      </Box>
    );
  }

  // Chip variant (default)
  return (
    <Tooltip title={getTooltipContent()} arrow>
      <StyledChip
        icon={getIcon()}
        label={
          <Box display="flex" alignItems="center" gap={0.5}>
            <span>{getLabel()}</span>
            {lastSaved && showLastSaved && !isSaving && !error && (
              <Typography
                component="span"
                variant="caption"
                sx={{ opacity: 0.7, fontSize: '0.7rem' }}
              >
                • {formatLastSaved(lastSaved)}
              </Typography>
            )}
          </Box>
        }
        size="small"
        savestatus={getStatus()}
        variant="outlined"
      />
    </Tooltip>
  );
};

// Componente de progreso de guardado con animación
export const SavingProgress: React.FC<{
  show: boolean;
  message?: string;
}> = ({ show, message = 'Guardando progreso...' }) => {
  return (
    <Fade in={show}>
      <Box
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          zIndex: 1300,
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(8px)',
          borderRadius: 2,
          padding: 2,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
          border: '1px solid rgba(0, 0, 0, 0.06)',
          display: 'flex',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <CircularProgress size={20} thickness={4} />
        <Typography variant="body2" color="text.secondary">
          {message}
        </Typography>
      </Box>
    </Fade>
  );
};

export default AutoSaveIndicator;
