import { useState, useEffect } from 'react';

/**
 * Hook personalizado para debounce de valores
 * Útil para búsquedas en tiempo real sin hacer demasiadas consultas
 * 
 * @param value - El valor a debounce
 * @param delay - El retraso en milisegundos
 * @returns El valor debounced
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Establecer un timer que actualice el valor debounced después del delay
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Limpiar el timeout si el valor cambia antes de que se complete el delay
    // Esto previene que se ejecute la función si el usuario sigue escribiendo
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Hook personalizado para debounce de funciones
 * Útil para funciones que no queremos que se ejecuten muy frecuentemente
 * 
 * @param callback - La función a debounce
 * @param delay - El retraso en milisegundos
 * @returns La función debounced
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const debouncedCallback = ((...args: Parameters<T>) => {
    // Limpiar el timer anterior si existe
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Establecer un nuevo timer
    const newTimer = setTimeout(() => {
      callback(...args);
    }, delay);

    setDebounceTimer(newTimer);
  }) as T;

  // Limpiar el timer cuando el componente se desmonte
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return debouncedCallback;
}
