import React from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
} from 'recharts';
import { Box, Typography, useTheme } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';

interface ActivityData {
  date: string;
  newUsers: number;
  activeUsers: number;
  completedTests: number;
}

interface UserActivityChartProps {
  data: ActivityData[];
  loading?: boolean;
}

export const UserActivityChart: React.FC<UserActivityChartProps> = ({
  data,
  loading = false,
}) => {
  const theme = useTheme();

  if (loading) {
    return (
      <Box
        sx={{
          height: 250,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Box
          sx={{
            width: '100%',
            height: '100%',
            bgcolor: 'grey.100',
            borderRadius: 1,
            animation: 'pulse 1.5s ease-in-out infinite',
          }}
        />
      </Box>
    );
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Box
          sx={{
            bgcolor: 'background.paper',
            p: 2,
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 1,
            boxShadow: 2,
          }}
        >
          <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
            {label}
          </Typography>
          {payload.map((entry: any, index: number) => (
            <Typography
              key={index}
              variant="body2"
              sx={{
                color: entry.color,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <Box
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  bgcolor: entry.color,
                }}
              />
              {entry.name}: {entry.value}
            </Typography>
          ))}
        </Box>
      );
    }
    return null;
  };

  return (
    <Box sx={{ width: '100%', height: 250 }}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={data}
          margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
        >
          <defs>
            <linearGradient id="colorNewUsers" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="5%"
                stopColor={theme.palette.primary.main}
                stopOpacity={0.3}
              />
              <stop
                offset="95%"
                stopColor={theme.palette.primary.main}
                stopOpacity={0}
              />
            </linearGradient>
            <linearGradient id="colorActiveUsers" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="5%"
                stopColor={theme.palette.success.main}
                stopOpacity={0.3}
              />
              <stop
                offset="95%"
                stopColor={theme.palette.success.main}
                stopOpacity={0}
              />
            </linearGradient>
            <linearGradient
              id="colorCompletedTests"
              x1="0"
              y1="0"
              x2="0"
              y2="1"
            >
              <stop
                offset="5%"
                stopColor={theme.palette.warning.main}
                stopOpacity={0.3}
              />
              <stop
                offset="95%"
                stopColor={theme.palette.warning.main}
                stopOpacity={0}
              />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <XAxis
            dataKey="date"
            stroke={theme.palette.text.secondary}
            fontSize={12}
          />
          <YAxis stroke={theme.palette.text.secondary} fontSize={12} />
          <Tooltip content={<CustomTooltip />} />
          <Area
            type="monotone"
            dataKey="newUsers"
            stroke={theme.palette.primary.main}
            fillOpacity={1}
            fill="url(#colorNewUsers)"
            strokeWidth={2}
            name="Nuevos Usuarios"
          />
          <Area
            type="monotone"
            dataKey="activeUsers"
            stroke={theme.palette.success.main}
            fillOpacity={1}
            fill="url(#colorActiveUsers)"
            strokeWidth={2}
            name="Usuarios Activos"
          />
          <Area
            type="monotone"
            dataKey="completedTests"
            stroke={theme.palette.warning.main}
            fillOpacity={1}
            fill="url(#colorCompletedTests)"
            strokeWidth={2}
            name="Tests Completados"
          />
        </AreaChart>
      </ResponsiveContainer>
    </Box>
  );
};
