import { useAuth } from './useAuth';
import { ROUTE_PERMISSIONS } from '../config/routes';

interface RoutePermissionResult {
  hasAccess: boolean;
  reason: string | null;
  userRole?: string;
  requiredRoles?: string[];
}

/**
 * Hook para verificar permisos de ruta
 */
export const useRoutePermissions = (path: string): RoutePermissionResult => {
  const { user, isAuthenticated } = useAuth();

  const permissions = ROUTE_PERMISSIONS[path];

  if (!permissions) {
    // Si no hay permisos definidos, permitir acceso por defecto
    return { hasAccess: true, reason: null };
  }

  if (permissions.requireAuth !== false && !isAuthenticated) {
    return { hasAccess: false, reason: 'authentication_required' };
  }

  if (isAuthenticated && user) {
    const hasRolePermission = permissions.allowedRoles.includes(user.role);
    if (!hasRolePermission) {
      return {
        hasAccess: false,
        reason: 'insufficient_permissions',
        userRole: user.role,
        requiredRoles: permissions.allowedRoles,
      };
    }
  }

  return { hasAccess: true, reason: null };
};
