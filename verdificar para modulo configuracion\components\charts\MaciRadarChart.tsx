import React from 'react';
import {
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  ResponsiveContainer,
  Tooltip,
  Legend,
} from 'recharts';
import { Box, Typography } from '@mui/material';
import { MACI_SCALE_NAMES } from '../../types/analytics';

interface MaciRadarData {
  scale: string;
  scaleName: string;
  value: number;
  fullMark: 100;
}

interface MaciRadarChartProps {
  data?: MaciRadarData[];
  height?: number;
  title?: string;
  showLegend?: boolean;
}

// Datos de ejemplo para demostración
const sampleData: MaciRadarData[] = [
  { scale: '1', scaleName: 'Introvertido', value: 65, fullMark: 100 },
  { scale: '2A', scaleName: 'Inhibido', value: 45, fullMark: 100 },
  { scale: '3', scaleName: 'Sumiso', value: 55, fullMark: 100 },
  { scale: '9', scaleName: 'Tendencia Límite', value: 75, fullMark: 100 },
  { scale: 'DD', scaleName: 'Propensión a la Impulsividad', value: 80, fullMark: 100 },
  { scale: 'EE', scaleName: 'Sentimientos de Ansiedad', value: 70, fullMark: 100 },
  { scale: 'FF', scaleName: 'Afecto Depresivo', value: 60, fullMark: 100 },
  { scale: 'GG', scaleName: 'Tendencia al Suicidio', value: 40, fullMark: 100 },
];

// Estilo para ocultar visualmente pero mantener accesible para lectores de pantalla
const visuallyHiddenStyle = {
  border: 0,
  clip: 'rect(0 0 0 0)',
  height: '1px',
  margin: '-1px',
  overflow: 'hidden',
  padding: 0,
  position: 'absolute' as const,
  width: '1px',
};

const MaciRadarChart: React.FC<MaciRadarChartProps> = ({
  data = sampleData,
  height = 400,
  title = "Perfil MACI-II",
  showLegend = true
}) => {
  const chartId = `maci-radar-chart-${Math.random().toString(36).substr(2, 9)}`;
  const descriptionId = `${chartId}-desc`;
  const tableId = `${chartId}-table`;
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div style={{
          backgroundColor: '#fff',
          border: '1px solid #5A92C8',
          borderRadius: '8px',
          padding: '12px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          maxWidth: '200px',
        }}>
          <p style={{ margin: 0, fontWeight: 'bold', color: '#333', marginBottom: '4px' }}>
            {data.scaleName}
          </p>
          <p style={{ margin: 0, color: '#666', fontSize: '12px', marginBottom: '4px' }}>
            Escala: {data.scale}
          </p>
          <p style={{ margin: 0, color: '#5A92C8', fontWeight: 'bold' }}>
            Puntuación: {data.value}
          </p>
        </div>
      );
    }
    return null;
  };

  // Función para determinar el color basado en la puntuación
  const getScoreColor = (value: number) => {
    if (value >= 85) return '#d32f2f'; // Rojo para puntuaciones muy altas
    if (value >= 75) return '#f57c00'; // Naranja para puntuaciones altas
    if (value >= 65) return '#fbc02d'; // Amarillo para puntuaciones moderadamente altas
    if (value >= 35) return '#388e3c'; // Verde para puntuaciones normales
    return '#1976d2'; // Azul para puntuaciones bajas
  };

  return (
    <Box>
      {/* Descripción accesible */}
      <Typography id={descriptionId} sx={visuallyHiddenStyle}>
        Gráfico de radar que muestra las puntuaciones del perfil MACI-II.
        Las puntuaciones van de 0 a 100, donde valores superiores a 75 se consideran elevados
        y superiores a 85 muy elevados. {title} incluye {data.length} escalas evaluadas.
      </Typography>

      <ResponsiveContainer width="100%" height={height}>
        <RadarChart
          data={data}
          margin={{ top: 20, right: 30, bottom: 20, left: 30 }}
          aria-label={`${title} - Gráfico de radar del perfil psicológico`}
          aria-describedby={descriptionId}
          role="img"
        >
          <PolarGrid
            stroke="#e0e0e0"
            strokeWidth={1}
          />
          <PolarAngleAxis
            dataKey="scaleName"
            tick={{ fontSize: 11, fill: '#666' }}
            className="radar-angle-axis"
          />
          <PolarRadiusAxis
            angle={90}
            domain={[0, 100]}
            tick={{ fontSize: 10, fill: '#999' }}
            tickCount={6}
          />
          <Radar
            name={title}
            dataKey="value"
            stroke="#5A92C8"
            fill="#5A92C8"
            fillOpacity={0.3}
            strokeWidth={2}
            dot={{ fill: '#5A92C8', strokeWidth: 2, r: 4 }}
          />
          <Tooltip content={<CustomTooltip />} />
          {showLegend && (
            <Legend
              verticalAlign="bottom"
              height={36}
              iconType="circle"
            />
          )}
        </RadarChart>
      </ResponsiveContainer>

      {/* Tabla de datos alternativa para accesibilidad */}
      <Box component="table" sx={visuallyHiddenStyle} id={tableId}>
        <caption>Datos del {title}</caption>
        <thead>
          <tr>
            <th scope="col">Escala</th>
            <th scope="col">Nombre Completo</th>
            <th scope="col">Puntuación</th>
            <th scope="col">Interpretación</th>
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => {
            const interpretation = item.value >= 85 ? 'Muy Elevado' :
                                 item.value >= 75 ? 'Elevado' : 'Normal';
            return (
              <tr key={`${item.scale}-${index}`}>
                <td>{item.scale}</td>
                <td>{item.scaleName}</td>
                <td>{item.value}</td>
                <td>{interpretation}</td>
              </tr>
            );
          })}
        </tbody>
      </Box>
    </Box>
  );
};

export default MaciRadarChart;

/**
 * Función utilitaria para transformar datos de evaluación a formato de radar
 */
export const transformEvaluationToRadarData = (
  scores: any,
  scalesToShow: string[] = ['1', '2A', '3', '9', 'DD', 'EE', 'FF', 'GG']
): MaciRadarData[] => {
  const radarData: MaciRadarData[] = [];
  
  scalesToShow.forEach(scale => {
    // Buscar la puntuación en todas las categorías
    const categories = ['personalityPatterns', 'expressedConcerns', 'clinicalSyndromes', 'modifierIndices'];
    
    for (const category of categories) {
      const scaleData = scores?.[category]?.[scale];
      if (scaleData?.pc !== undefined) {
        radarData.push({
          scale,
          scaleName: MACI_SCALE_NAMES[scale] || scale,
          value: scaleData.pc,
          fullMark: 100,
        });
        break;
      }
    }
  });
  
  return radarData;
};
