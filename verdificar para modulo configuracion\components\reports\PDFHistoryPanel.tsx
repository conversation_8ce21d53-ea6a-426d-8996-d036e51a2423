import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  PictureAsPdf as PdfIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { useNotifier } from '../../hooks/useNotifier';
import { PDFService } from '../../services/pdfService';

interface PDFRecord {
  id: string;
  fileName: string;
  filePath: string;
  createdAt: string;
  fileSize?: number;
  status: 'available' | 'deleted' | 'error';
}

interface PDFHistoryPanelProps {
  evaluationId: string;
}

const PDFHistoryPanel: React.FC<PDFHistoryPanelProps> = ({ evaluationId }) => {
  const [pdfHistory, setPdfHistory] = useState<PDFRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedPdf, setSelectedPdf] = useState<PDFRecord | null>(null);
  const { showSuccess, showError } = useNotifier();

  useEffect(() => {
    loadPDFHistory();
  }, [evaluationId]);

  const loadPDFHistory = async () => {
    setLoading(true);
    try {
      // Simular carga de historial de PDFs
      // En una implementación real, esto vendría de la base de datos
      const mockHistory: PDFRecord[] = [
        {
          id: '1',
          fileName: `evaluacion_${evaluationId}_${new Date().toISOString().split('T')[0]}.pdf`,
          filePath: `/storage/pdfs/evaluacion_${evaluationId}.pdf`,
          createdAt: new Date().toISOString(),
          fileSize: 1024 * 1024 * 2.5, // 2.5 MB
          status: 'available'
        }
      ];
      
      setPdfHistory(mockHistory);
    } catch (error) {
      console.error('Error loading PDF history:', error);
      showError('Error al cargar el historial de PDFs');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadPDF = async (pdf: PDFRecord) => {
    try {
      const success = await PDFService.downloadPDF(pdf.filePath, pdf.fileName);
      if (success) {
        showSuccess('PDF descargado exitosamente');
      } else {
        showError('Error al descargar el PDF');
      }
    } catch (error) {
      console.error('Error downloading PDF:', error);
      showError('Error inesperado al descargar');
    }
  };

  const handleDeletePDF = (pdf: PDFRecord) => {
    setSelectedPdf(pdf);
    setDeleteDialogOpen(true);
  };

  const confirmDeletePDF = async () => {
    if (!selectedPdf) return;

    try {
      // Aquí iría la lógica para eliminar el PDF del storage
      // await PDFService.deletePDF(selectedPdf.filePath);
      
      setPdfHistory(prev => prev.filter(pdf => pdf.id !== selectedPdf.id));
      showSuccess('PDF eliminado exitosamente');
    } catch (error) {
      console.error('Error deleting PDF:', error);
      showError('Error al eliminar el PDF');
    } finally {
      setDeleteDialogOpen(false);
      setSelectedPdf(null);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Historial de PDFs
        </Typography>
        <Button
          startIcon={<RefreshIcon />}
          onClick={loadPDFHistory}
          disabled={loading}
          size="small"
        >
          Actualizar
        </Button>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : pdfHistory.length === 0 ? (
        <Alert severity="info" icon={<InfoIcon />}>
          No se han generado PDFs para esta evaluación aún.
        </Alert>
      ) : (
        <List>
          {pdfHistory.map((pdf, index) => (
            <React.Fragment key={pdf.id}>
              <ListItem>
                <ListItemIcon>
                  <PdfIcon color="error" />
                </ListItemIcon>
                <ListItemText
                  primary={pdf.fileName}
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Creado: {formatDate(pdf.createdAt)}
                      </Typography>
                      {pdf.fileSize && (
                        <Typography variant="body2" color="text.secondary">
                          Tamaño: {formatFileSize(pdf.fileSize)}
                        </Typography>
                      )}
                      <Chip
                        label={pdf.status === 'available' ? 'Disponible' : 'No disponible'}
                        color={pdf.status === 'available' ? 'success' : 'error'}
                        size="small"
                        sx={{ mt: 0.5 }}
                      />
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    onClick={() => handleDownloadPDF(pdf)}
                    disabled={pdf.status !== 'available'}
                    sx={{ mr: 1 }}
                  >
                    <DownloadIcon />
                  </IconButton>
                  <IconButton
                    edge="end"
                    onClick={() => handleDeletePDF(pdf)}
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
              {index < pdfHistory.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>
      )}

      {/* Dialog de confirmación para eliminar */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirmar Eliminación</DialogTitle>
        <DialogContent>
          <Typography>
            ¿Estás seguro de que deseas eliminar el PDF "{selectedPdf?.fileName}"?
            Esta acción no se puede deshacer.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Cancelar
          </Button>
          <Button onClick={confirmDeletePDF} color="error" variant="contained">
            Eliminar
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default PDFHistoryPanel;
