/**
 * Panel de Informes de Uso - Generación de reportes detallados
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider
} from '@mui/material';
import {
  Download as DownloadIcon,
  Assessment as AssessmentIcon,
  DateRange as DateRangeIcon,
  Person as PersonIcon,
  Apps as AppsIcon,
  TrendingUp as TrendingUpIcon,
  PictureAsPdf as PdfIcon,
  TableChart as ExcelIcon
} from '@mui/icons-material';
import { appUsageService, supabase, UsageStatistics, UserUsageInfo, AppUsageHistory } from '../../services/appUsageService';

interface ReportFilters {
  startDate: string;
  endDate: string;
  appId: string;
  userId: string;
  reportType: 'summary' | 'detailed' | 'user_activity' | 'app_usage';
}

const UsageReportsPanel: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [statistics, setStatistics] = useState<UsageStatistics[]>([]);
  const [users, setUsers] = useState<UserUsageInfo[]>([]);
  const [appLimits, setAppLimits] = useState<any[]>([]);
  const [reportData, setReportData] = useState<any[]>([]);
  const [showReportDialog, setShowReportDialog] = useState(false);
  
  const [filters, setFilters] = useState<ReportFilters>({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 días atrás
    endDate: new Date().toISOString().split('T')[0],
    appId: '',
    userId: '',
    reportType: 'summary'
  });

  useEffect(() => {
    // Cargar datos iniciales
    loadInitialData();

    // Configurar suscripciones en tiempo real
    const subscriptions = setupRealtimeSubscriptions();

    // Cleanup: cancelar suscripciones al desmontar el componente
    return () => {
      subscriptions.forEach(subscription => {
        appUsageService.unsubscribeFromChannel(subscription);
      });
    };
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      const [statsResult, usersResult, limitsResult] = await Promise.all([
        appUsageService.getUsageStatistics(),
        appUsageService.getAllUsersUsage(),
        appUsageService.getAppLimits()
      ]);

      if (statsResult.success) setStatistics(statsResult.statistics);
      if (usersResult.success) setUsers(usersResult.users);
      if (limitsResult.success) setAppLimits(limitsResult.limits);
    } catch (err) {
      setError('Error al cargar datos iniciales');
    } finally {
      setLoading(false);
    }
  };

  const setupRealtimeSubscriptions = () => {
    const subscriptions: any[] = [];

    // Suscripción a cambios en estadísticas (app_usage y app_usage_history)
    const usageSubscription = appUsageService.subscribeToUsersUsage(async (payload) => {
      console.log('Usage data changed in reports panel:', payload);
      // Actualizar estadísticas y usuarios
      const [statsResult, usersResult] = await Promise.all([
        appUsageService.getUsageStatistics(),
        appUsageService.getAllUsersUsage()
      ]);

      if (statsResult.success) setStatistics(statsResult.statistics);
      if (usersResult.success) setUsers(usersResult.users);
    });
    subscriptions.push(usageSubscription);

    // Suscripción a cambios en límites de aplicaciones
    const limitsSubscription = appUsageService.subscribeToAppLimits(async (payload) => {
      console.log('App limits changed in reports panel:', payload);
      const limitsResult = await appUsageService.getAppLimits();
      if (limitsResult.success) setAppLimits(limitsResult.limits);
    });
    subscriptions.push(limitsSubscription);

    // Suscripción a cambios en historial de uso
    const historySubscription = appUsageService.subscribeToUsageHistory(async (payload) => {
      console.log('Usage history changed in reports panel:', payload);
      // Actualizar estadísticas cuando hay cambios en el historial
      const statsResult = await appUsageService.getUsageStatistics();
      if (statsResult.success) setStatistics(statsResult.statistics);
    });
    subscriptions.push(historySubscription);

    return subscriptions;
  };

  const generateReport = async () => {
    setLoading(true);
    setError(null);

    try {
      let data: any[] = [];

      switch (filters.reportType) {
        case 'summary':
          data = generateSummaryReport();
          break;
        case 'detailed':
          data = await generateDetailedReport();
          break;
        case 'user_activity':
          data = generateUserActivityReport();
          break;
        case 'app_usage':
          data = generateAppUsageReport();
          break;
      }

      setReportData(data);
      setShowReportDialog(true);
    } catch (err) {
      setError('Error al generar el informe');
    } finally {
      setLoading(false);
    }
  };

  const generateSummaryReport = () => {
    return statistics.map(stat => ({
      aplicacion: stat.app_name,
      usuarios_totales: stat.total_users,
      usos_asignados: stat.total_assigned_uses,
      usos_utilizados: stat.total_used,
      usos_restantes: stat.total_remaining_uses,
      porcentaje_uso: `${stat.usage_percentage.toFixed(1)}%`,
      promedio_restante: stat.avg_remaining_uses.toFixed(1),
      primer_uso: stat.first_use ? new Date(stat.first_use).toLocaleDateString() : 'N/A',
      ultimo_uso: stat.last_use ? new Date(stat.last_use).toLocaleDateString() : 'N/A'
    }));
  };

  const generateUserActivityReport = () => {
    const filteredUsers = filters.userId 
      ? users.filter(user => user.user_id === filters.userId)
      : users;

    return filteredUsers.flatMap(user => 
      user.usages
        .filter(usage => !filters.appId || usage.app_id === filters.appId)
        .map(usage => ({
          usuario: user.user_name,
          email: user.user_email,
          rol: user.user_role,
          aplicacion: usage.app_name,
          usos_restantes: usage.remaining_uses,
          total_asignado: usage.total_assigned,
          usos_utilizados: usage.total_assigned - usage.remaining_uses,
          porcentaje_uso: ((usage.total_assigned - usage.remaining_uses) / usage.total_assigned * 100).toFixed(1) + '%',
          ultimo_uso: usage.last_used_at ? new Date(usage.last_used_at).toLocaleDateString() : 'Nunca',
          estado: usage.remaining_uses > 0 ? 'Activo' : 'Sin usos'
        }))
    );
  };

  const generateAppUsageReport = () => {
    const filteredStats = filters.appId 
      ? statistics.filter(stat => stat.app_id === filters.appId)
      : statistics;

    return filteredStats.map(stat => {
      const appUsers = users.filter(user => 
        user.usages.some(usage => usage.app_id === stat.app_id)
      );

      return {
        aplicacion: stat.app_name,
        id_aplicacion: stat.app_id,
        usuarios_con_acceso: stat.total_users,
        usuarios_activos: appUsers.filter(user => 
          user.usages.find(usage => usage.app_id === stat.app_id)?.remaining_uses > 0
        ).length,
        usuarios_sin_usos: appUsers.filter(user => 
          user.usages.find(usage => usage.app_id === stat.app_id)?.remaining_uses === 0
        ).length,
        total_usos_asignados: stat.total_assigned_uses,
        total_usos_consumidos: stat.total_used,
        total_usos_restantes: stat.total_remaining_uses,
        eficiencia_uso: `${stat.usage_percentage.toFixed(1)}%`,
        promedio_usos_por_usuario: (stat.total_assigned_uses / stat.total_users).toFixed(1),
        fecha_primer_uso: stat.first_use ? new Date(stat.first_use).toLocaleDateString() : 'N/A',
        fecha_ultimo_uso: stat.last_use ? new Date(stat.last_use).toLocaleDateString() : 'N/A'
      };
    });
  };

  const generateDetailedReport = async () => {
    // Para el reporte detallado, necesitaríamos obtener el historial
    // Por ahora, combinamos información de usuarios y estadísticas
    const detailed = [];
    
    for (const user of users) {
      for (const usage of user.usages) {
        if (filters.appId && usage.app_id !== filters.appId) continue;
        
        detailed.push({
          fecha: new Date().toLocaleDateString(),
          usuario_id: user.user_id,
          usuario_nombre: user.user_name,
          usuario_email: user.user_email,
          usuario_rol: user.user_role,
          aplicacion_id: usage.app_id,
          aplicacion_nombre: usage.app_name,
          usos_asignados_total: usage.total_assigned,
          usos_restantes: usage.remaining_uses,
          usos_consumidos: usage.total_assigned - usage.remaining_uses,
          porcentaje_consumo: ((usage.total_assigned - usage.remaining_uses) / usage.total_assigned * 100).toFixed(1),
          ultimo_uso: usage.last_used_at ? new Date(usage.last_used_at).toLocaleString() : 'Nunca',
          estado_cuenta: usage.remaining_uses > 0 ? 'Activa' : 'Agotada',
          dias_sin_uso: usage.last_used_at 
            ? Math.floor((Date.now() - new Date(usage.last_used_at).getTime()) / (1000 * 60 * 60 * 24))
            : 'N/A'
        });
      }
    }
    
    return detailed;
  };

  const exportToCSV = (data: any[], filename: string) => {
    if (data.length === 0) return;

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          return typeof value === 'string' && value.includes(',') 
            ? `"${value}"` 
            : value;
        }).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToJSON = (data: any[], filename: string) => {
    const jsonContent = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.json`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getReportTypeLabel = (type: string) => {
    const labels = {
      summary: 'Resumen Ejecutivo',
      detailed: 'Reporte Detallado',
      user_activity: 'Actividad de Usuarios',
      app_usage: 'Uso por Aplicación'
    };
    return labels[type as keyof typeof labels] || type;
  };

  if (loading && !showReportDialog) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Typography variant="h5" gutterBottom>
        <AssessmentIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        Generación de Informes de Uso
      </Typography>

      <Grid container spacing={3}>
        {/* Panel de Filtros */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Configuración del Informe
              </Typography>

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Tipo de Informe</InputLabel>
                <Select
                  value={filters.reportType}
                  onChange={(e) => setFilters({...filters, reportType: e.target.value as any})}
                  label="Tipo de Informe"
                >
                  <MenuItem value="summary">Resumen Ejecutivo</MenuItem>
                  <MenuItem value="detailed">Reporte Detallado</MenuItem>
                  <MenuItem value="user_activity">Actividad de Usuarios</MenuItem>
                  <MenuItem value="app_usage">Uso por Aplicación</MenuItem>
                </Select>
              </FormControl>

              <TextField
                fullWidth
                label="Fecha Inicio"
                type="date"
                value={filters.startDate}
                onChange={(e) => setFilters({...filters, startDate: e.target.value})}
                InputLabelProps={{ shrink: true }}
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Fecha Fin"
                type="date"
                value={filters.endDate}
                onChange={(e) => setFilters({...filters, endDate: e.target.value})}
                InputLabelProps={{ shrink: true }}
                sx={{ mb: 2 }}
              />

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Aplicación (Opcional)</InputLabel>
                <Select
                  value={filters.appId}
                  onChange={(e) => setFilters({...filters, appId: e.target.value})}
                  label="Aplicación (Opcional)"
                >
                  <MenuItem value="">Todas las aplicaciones</MenuItem>
                  {appLimits.map((app) => (
                    <MenuItem key={app.app_id} value={app.app_id}>
                      {app.app_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>Usuario (Opcional)</InputLabel>
                <Select
                  value={filters.userId}
                  onChange={(e) => setFilters({...filters, userId: e.target.value})}
                  label="Usuario (Opcional)"
                >
                  <MenuItem value="">Todos los usuarios</MenuItem>
                  {users.map((user) => (
                    <MenuItem key={user.user_id} value={user.user_id}>
                      {user.user_name} ({user.user_email})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Button
                fullWidth
                variant="contained"
                onClick={generateReport}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <AssessmentIcon />}
              >
                {loading ? 'Generando...' : 'Generar Informe'}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Panel de Vista Previa */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Informes Rápidos
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<TrendingUpIcon />}
                    onClick={() => exportToCSV(generateSummaryReport(), 'resumen_uso')}
                  >
                    Exportar Resumen
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<PersonIcon />}
                    onClick={() => exportToCSV(generateUserActivityReport(), 'actividad_usuarios')}
                  >
                    Exportar Usuarios
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<AppsIcon />}
                    onClick={() => exportToCSV(generateAppUsageReport(), 'uso_aplicaciones')}
                  >
                    Exportar Apps
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<ExcelIcon />}
                    onClick={async () => exportToJSON(await generateDetailedReport(), 'reporte_detallado')}
                  >
                    Exportar JSON
                  </Button>
                </Grid>
              </Grid>

              <Divider sx={{ my: 3 }} />

              <Typography variant="body2" color="text.secondary">
                Los informes incluyen datos de uso, estadísticas por usuario y aplicación, 
                y métricas de eficiencia del sistema. Todos los archivos se exportan con 
                fecha actual para facilitar el seguimiento histórico.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Diálogo de Vista Previa del Informe */}
      <Dialog 
        open={showReportDialog} 
        onClose={() => setShowReportDialog(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Vista Previa: {getReportTypeLabel(filters.reportType)}
        </DialogTitle>
        <DialogContent>
          {reportData.length > 0 ? (
            <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    {Object.keys(reportData[0]).map((header) => (
                      <TableCell key={header}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {header.replace(/_/g, ' ').toUpperCase()}
                        </Typography>
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {reportData.slice(0, 50).map((row, index) => (
                    <TableRow key={index}>
                      {Object.values(row).map((value, cellIndex) => (
                        <TableCell key={cellIndex}>
                          {String(value)}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Typography>No hay datos para mostrar</Typography>
          )}
          
          {reportData.length > 50 && (
            <Typography variant="caption" color="text.secondary" sx={{ mt: 2 }}>
              Mostrando primeras 50 filas de {reportData.length} registros totales
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowReportDialog(false)}>
            Cerrar
          </Button>
          <Button 
            onClick={() => exportToCSV(reportData, `informe_${filters.reportType}`)}
            startIcon={<DownloadIcon />}
            variant="contained"
          >
            Descargar CSV
          </Button>
          <Button 
            onClick={() => exportToJSON(reportData, `informe_${filters.reportType}`)}
            startIcon={<DownloadIcon />}
            variant="outlined"
          >
            Descargar JSON
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UsageReportsPanel;
