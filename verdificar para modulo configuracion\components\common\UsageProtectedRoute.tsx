import React from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useUsageControl } from '../../hooks/useUsageControl';
import UsageBlockedMessage from './UsageBlockedMessage';
import { Box, CircularProgress } from '@mui/material';

interface UsageProtectedRouteProps {
  children: React.ReactNode;
  appId: string;
  appName?: string;
  fallbackVariant?: 'alert' | 'card' | 'inline';
  showContactInfo?: boolean;
}

/**
 * Componente que protege rutas basándose en la disponibilidad de usos
 * Solo permite el acceso si el usuario tiene usos disponibles para la aplicación
 */
const UsageProtectedRoute: React.FC<UsageProtectedRouteProps> = ({
  children,
  appId,
  appName,
  fallbackVariant = 'card',
  showContactInfo = true
}) => {
  const { user } = useAuth();
  const { canPerformAction, loading, refreshUsage } = useUsageControl();

  // Mostrar loading mientras se cargan los datos
  if (loading) {
    return (
      <Box 
        sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          minHeight: '200px' 
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Los administradores siempre pueden acceder
  if (user?.rol === 'administrador') {
    return <>{children}</>;
  }

  // Verificar si puede realizar la acción
  if (!canPerformAction(appId)) {
    return (
      <UsageBlockedMessage
        appName={appName || 'esta aplicación'}
        variant={fallbackVariant}
        showContactInfo={showContactInfo}
        onRefresh={refreshUsage}
      />
    );
  }

  // Si tiene permisos, mostrar el contenido
  return <>{children}</>;
};

export default UsageProtectedRoute;
