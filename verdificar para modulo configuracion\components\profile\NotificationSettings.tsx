import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
  FormGroup,
  FormControlLabel,
  Switch,
  Typography,
  Divider,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  Computer as DesktopIcon,
  Report as ReportIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import PushNotificationSettings from './PushNotificationSettings';
import { supabase } from '../../lib/supabaseClient';

interface NotificationPreferences {
  email_reports: boolean;
  email_patient_updates: boolean;
  email_system_alerts: boolean;
  push_reports: boolean;
  push_patient_updates: boolean;
  push_system_alerts: boolean;
  sms_urgent_only: boolean;
  daily_summary: boolean;
  weekly_reports: boolean;
}

const defaultPreferences: NotificationPreferences = {
  email_reports: true,
  email_patient_updates: true,
  email_system_alerts: true,
  push_reports: false,
  push_patient_updates: true,
  push_system_alerts: true,
  sms_urgent_only: false,
  daily_summary: false,
  weekly_reports: true,
};

const NotificationSettings: React.FC = () => {
  const [preferences, setPreferences] = useState<NotificationPreferences>(defaultPreferences);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        setPreferences(data);
      }
    } catch (error) {
      console.error('Error loading notification preferences:', error);
      setError('Error al cargar las preferencias de notificación');
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async () => {
    try {
      setSaving(true);
      setError(null);

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuario no autenticado');

      const { error } = await supabase
        .from('notification_preferences')
        .upsert({
          user_id: user.id,
          ...preferences,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;

      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      setError('Error al guardar las preferencias');
    } finally {
      setSaving(false);
    }
  };

  const handlePreferenceChange = (key: keyof NotificationPreferences) => {
    setPreferences(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const notificationSections = [
    {
      title: 'Notificaciones por Email',
      icon: <EmailIcon color="primary" />,
      description: 'Recibe actualizaciones importantes en tu correo electrónico',
      preferences: [
        {
          key: 'email_reports' as keyof NotificationPreferences,
          label: 'Informes completados',
          description: 'Cuando se genere un nuevo informe',
        },
        {
          key: 'email_patient_updates' as keyof NotificationPreferences,
          label: 'Actualizaciones de pacientes',
          description: 'Cambios en el estado de los pacientes',
        },
        {
          key: 'email_system_alerts' as keyof NotificationPreferences,
          label: 'Alertas del sistema',
          description: 'Notificaciones importantes del sistema',
        },
      ],
    },
    {
      title: 'Notificaciones Push',
      icon: <DesktopIcon color="primary" />,
      description: 'Notificaciones instantáneas en tu navegador',
      preferences: [
        {
          key: 'push_reports' as keyof NotificationPreferences,
          label: 'Informes completados',
          description: 'Notificación inmediata de nuevos informes',
        },
        {
          key: 'push_patient_updates' as keyof NotificationPreferences,
          label: 'Actualizaciones de pacientes',
          description: 'Cambios importantes en pacientes',
        },
        {
          key: 'push_system_alerts' as keyof NotificationPreferences,
          label: 'Alertas del sistema',
          description: 'Notificaciones críticas del sistema',
        },
      ],
    },
    {
      title: 'Resúmenes Programados',
      icon: <ScheduleIcon color="primary" />,
      description: 'Recibe resúmenes periódicos de actividad',
      preferences: [
        {
          key: 'daily_summary' as keyof NotificationPreferences,
          label: 'Resumen diario',
          description: 'Resumen de actividad del día',
        },
        {
          key: 'weekly_reports' as keyof NotificationPreferences,
          label: 'Reporte semanal',
          description: 'Estadísticas y resumen semanal',
        },
      ],
    },
    {
      title: 'Notificaciones SMS',
      icon: <SmsIcon color="primary" />,
      description: 'Solo para alertas críticas (requiere número de teléfono)',
      preferences: [
        {
          key: 'sms_urgent_only' as keyof NotificationPreferences,
          label: 'Solo urgentes',
          description: 'Únicamente para situaciones críticas',
        },
      ],
    },
  ];

  if (loading) {
    return (
      <Card elevation={3} sx={{ borderRadius: 2 }}>
        <CardContent>
          <Typography>Cargando preferencias...</Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card elevation={3} sx={{ borderRadius: 2 }}>
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <NotificationsIcon color="primary" />
            <Typography variant="h6">Configuración de Notificaciones</Typography>
          </Box>
        }
        subheader="Personaliza cómo y cuándo quieres recibir notificaciones"
        action={
          <Chip
            label={`${Object.values(preferences).filter(Boolean).length} activas`}
            color="primary"
            variant="outlined"
          />
        }
      />
      <Divider />
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            ¡Preferencias guardadas correctamente!
          </Alert>
        )}

        {/* Configuración de Push Notifications */}
        <Box sx={{ mb: 4 }}>
          <PushNotificationSettings />
        </Box>

        <Divider sx={{ my: 3 }} />

        {notificationSections.map((section, index) => (
          <Box key={section.title} sx={{ mb: index < notificationSections.length - 1 ? 4 : 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              {section.icon}
              <Typography variant="h6" color="primary">
                {section.title}
              </Typography>
            </Box>
            
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {section.description}
            </Typography>

            <FormGroup>
              {section.preferences.map((pref) => (
                <FormControlLabel
                  key={pref.key}
                  control={
                    <Switch
                      checked={preferences[pref.key]}
                      onChange={() => handlePreferenceChange(pref.key)}
                      color="primary"
                    />
                  }
                  label={
                    <Box>
                      <Typography variant="body1">{pref.label}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {pref.description}
                      </Typography>
                    </Box>
                  }
                  sx={{ mb: 1 }}
                />
              ))}
            </FormGroup>

            {index < notificationSections.length - 1 && <Divider sx={{ mt: 2 }} />}
          </Box>
        ))}

        <Box sx={{ mt: 3, pt: 2, borderTop: 1, borderColor: 'divider' }}>
          <Button
            variant="contained"
            onClick={savePreferences}
            disabled={saving}
            sx={{
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
              mr: 2,
            }}
          >
            {saving ? 'Guardando...' : 'Guardar Preferencias'}
          </Button>
          
          <Button
            variant="outlined"
            onClick={() => setPreferences(defaultPreferences)}
            disabled={saving}
          >
            Restaurar Predeterminadas
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default NotificationSettings;
