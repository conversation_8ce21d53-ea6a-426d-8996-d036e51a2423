import { useState, useCallback } from 'react';

export interface NotificationState {
  open: boolean;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
}

export interface UseNotifierReturn {
  notification: NotificationState;
  showNotification: (message: string, type?: NotificationState['type']) => void;
  hideNotification: () => void;
  showSuccess: (message: string) => void;
  showError: (message: string) => void;
  showWarning: (message: string) => void;
  showInfo: (message: string) => void;
}

const initialNotificationState: NotificationState = {
  open: false,
  message: '',
  type: 'success',
};

/**
 * Hook personalizado para gestionar notificaciones de manera centralizada
 * Proporciona métodos convenientes para mostrar diferentes tipos de notificaciones
 */
export const useNotifier = (): UseNotifierReturn => {
  const [notification, setNotification] = useState<NotificationState>(
    initialNotificationState,
  );

  const showNotification = useCallback(
    (message: string, type: NotificationState['type'] = 'success') => {
      setNotification({
        open: true,
        message,
        type,
      });
    },
    [],
  );

  const hideNotification = useCallback(() => {
    setNotification((prev) => ({
      ...prev,
      open: false,
    }));
  }, []);

  const showSuccess = useCallback(
    (message: string) => {
      showNotification(message, 'success');
    },
    [showNotification],
  );

  const showError = useCallback(
    (message: string) => {
      showNotification(message, 'error');
    },
    [showNotification],
  );

  const showWarning = useCallback(
    (message: string) => {
      showNotification(message, 'warning');
    },
    [showNotification],
  );

  const showInfo = useCallback(
    (message: string) => {
      showNotification(message, 'info');
    },
    [showNotification],
  );

  return {
    notification,
    showNotification,
    hideNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };
};
