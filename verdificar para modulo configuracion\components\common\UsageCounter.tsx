import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Chip,
  Tooltip,
  IconButton,
  Popover,
  Typography,
  Card,
  CardContent,
  LinearProgress,
  Alert
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { appUsageService } from '../../services/appUsageService';
import { useAuth } from '../../hooks/useAuth';

interface UsageInfo {
  app_id: string;
  app_name: string;
  remaining_uses: number;
  total_assigned: number;
  last_used_at?: string;
}

const UsageCounter: React.FC = () => {
  const { user } = useAuth();
  const [usageInfo, setUsageInfo] = useState<UsageInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  // Solo mostrar para psicólogos
  if (!user || user.rol !== 'psicologo') {
    return null;
  }

  useEffect(() => {
    loadUsageInfo();
    
    // Suscribirse a cambios en tiempo real
    const subscription = appUsageService.subscribeToUsageChanges(async () => {
      await loadUsageInfo();
    });

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [user?.id]);

  const loadUsageInfo = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const result = await appUsageService.getUserUsage(user.id);
      
      if (result.success && result.usages) {
        setUsageInfo(result.usages);
      }
    } catch (error) {
      console.error('Error loading usage info:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  // Calcular totales
  const totalRemaining = usageInfo.reduce((sum, usage) => sum + usage.remaining_uses, 0);
  const totalAssigned = usageInfo.reduce((sum, usage) => sum + usage.total_assigned, 0);
  const totalUsed = totalAssigned - totalRemaining;

  // Determinar color y estado
  const getStatusColor = () => {
    if (totalRemaining === 0) return 'error';
    if (totalRemaining <= 5) return 'warning';
    return 'success';
  };

  const getStatusIcon = () => {
    if (totalRemaining === 0) return <ErrorIcon />;
    if (totalRemaining <= 5) return <WarningIcon />;
    return <AssignmentIcon />;
  };

  if (loading) {
    return (
      <Chip
        icon={<AssignmentIcon />}
        label="Cargando..."
        size="small"
        variant="outlined"
      />
    );
  }

  return (
    <>
      <Tooltip title="Ver detalles de usos disponibles">
        <Chip
          icon={getStatusIcon()}
          label={`Usos: ${totalRemaining}`}
          color={getStatusColor()}
          size="small"
          variant={totalRemaining === 0 ? 'filled' : 'outlined'}
          onClick={handleClick}
          sx={{
            cursor: 'pointer',
            fontWeight: 'bold',
            '&:hover': {
              backgroundColor: totalRemaining === 0 ? 'error.dark' : 'action.hover',
            },
          }}
        />
      </Tooltip>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Card sx={{ minWidth: 300, maxWidth: 400 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Estado de Usos Disponibles
            </Typography>

            {totalRemaining === 0 && (
              <Alert severity="error" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Sin usos disponibles</strong><br />
                  Contacta al administrador para solicitar más usos.
                </Typography>
              </Alert>
            )}

            {totalRemaining > 0 && totalRemaining <= 5 && (
              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Pocos usos restantes</strong><br />
                  Considera solicitar más usos al administrador.
                </Typography>
              </Alert>
            )}

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Resumen General
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Disponibles:</Typography>
                <Typography variant="body2" fontWeight="bold" color={getStatusColor()}>
                  {totalRemaining}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Usados:</Typography>
                <Typography variant="body2">{totalUsed}</Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="body2">Total Asignado:</Typography>
                <Typography variant="body2" fontWeight="bold">{totalAssigned}</Typography>
              </Box>
              
              {totalAssigned > 0 && (
                <LinearProgress
                  variant="determinate"
                  value={(totalUsed / totalAssigned) * 100}
                  color={totalRemaining === 0 ? 'error' : totalRemaining <= 5 ? 'warning' : 'success'}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              )}
            </Box>

            {usageInfo.length > 0 && (
              <Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Por Aplicación
                </Typography>
                {usageInfo.map((usage) => (
                  <Box key={usage.app_id} sx={{ mb: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="caption" sx={{ fontWeight: 500 }}>
                        {usage.app_name}
                      </Typography>
                      <Typography variant="caption" color={usage.remaining_uses === 0 ? 'error' : 'text.primary'}>
                        {usage.remaining_uses}/{usage.total_assigned}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={usage.total_assigned > 0 ? (usage.remaining_uses / usage.total_assigned) * 100 : 0}
                      color={usage.remaining_uses === 0 ? 'error' : usage.remaining_uses <= 2 ? 'warning' : 'success'}
                      sx={{ height: 4, borderRadius: 2 }}
                    />
                  </Box>
                ))}
              </Box>
            )}

            {usageInfo.length === 0 && (
              <Alert severity="info">
                <Typography variant="body2">
                  No tienes usos asignados. Contacta al administrador.
                </Typography>
              </Alert>
            )}
          </CardContent>
        </Card>
      </Popover>
    </>
  );
};

export default UsageCounter;
