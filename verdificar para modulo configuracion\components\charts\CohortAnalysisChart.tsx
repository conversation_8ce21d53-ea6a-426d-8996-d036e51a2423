import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';

// Datos de ejemplo que vendrían de tu backend en el futuro
const sampleData = [
  { name: '12-15 años', Masculino: 8, Femenino: 12, Otro: 1 },
  { name: '16-18 años', Masculino: 15, Femenino: 18, Otro: 2 },
  { name: '19-25 años', Masculino: 22, Femenino: 25, Otro: 1 },
  { name: '26+ años', Masculino: 10, Femenino: 14, Otro: 0 },
];

interface CohortAnalysisChartProps {
  data?: typeof sampleData;
  height?: number;
}

const CohortAnalysisChart: React.FC<CohortAnalysisChartProps> = ({ 
  data = sampleData, 
  height = 400 
}) => {
  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart
        data={data}
        margin={{
          top: 20,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="name" 
          tick={{ fontSize: 12 }}
          stroke="#666"
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          stroke="#666"
        />
        <Tooltip
          cursor={{ fill: 'rgba(90, 146, 200, 0.1)' }}
          contentStyle={{
            backgroundColor: '#fff',
            border: '1px solid #5A92C8',
            borderRadius: '8px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          }}
          labelStyle={{ color: '#333', fontWeight: 'bold' }}
        />
        <Legend 
          wrapperStyle={{ paddingTop: '20px' }}
        />
        <Bar 
          dataKey="Masculino" 
          fill="#5A92C8" 
          name="Masculino"
          radius={[2, 2, 0, 0]}
        />
        <Bar 
          dataKey="Femenino" 
          fill="#63B4A9" 
          name="Femenino"
          radius={[2, 2, 0, 0]}
        />
        <Bar 
          dataKey="Otro" 
          fill="#F28A7C" 
          name="Otro"
          radius={[2, 2, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default CohortAnalysisChart;
