import React from 'react';
import {
  <PERSON>,
  Button,
  ButtonGroup,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import {
  PictureAsPdf as PdfIcon,
  Print as PrintIcon,
  Share as ShareIcon,
  Download as DownloadIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { Report } from '../../services/reports';
import { generateReportPDF, PDF_PRESETS } from '../../utils/pdfGenerator';

interface ReportActionsProps {
  report: Report;
  onGeneratePdf: (report: Report) => void;
  onPrint?: (report: Report) => void;
  onShare?: (report: Report) => void;
  onDownload?: (report: Report) => void;
  onClose?: () => void;
  isGeneratingPdf?: boolean;
  showCloseButton?: boolean;
  variant?: 'horizontal' | 'vertical';
}

/**
 * Componente de acciones para reportes (PDF, imprimir, compartir, etc.)
 * Extraído de Informes.tsx para mejorar la modularidad
 */
const ReportActions: React.FC<ReportActionsProps> = ({
  report,
  onGeneratePdf,
  onPrint,
  onShare,
  onDownload,
  onClose,
  isGeneratingPdf = false,
  showCloseButton = false,
  variant = 'horizontal',
}) => {
  const handlePdfGeneration = async () => {
    if (!isGeneratingPdf) {
      try {
        await generateReportPDF(report, PDF_PRESETS.maciReport);
        // Llamar callback si existe para manejar estados
        onGeneratePdf(report);
      } catch (error) {
        console.error('Error generating PDF:', error);
        // El callback puede manejar el error
        onGeneratePdf(report);
      }
    }
  };

  const buttonProps = {
    size: 'small' as const,
    disabled: isGeneratingPdf,
  };

  const buttons = [
    {
      key: 'pdf',
      icon: isGeneratingPdf ? <CircularProgress size={16} /> : <PdfIcon />,
      label: isGeneratingPdf ? 'Generando...' : 'Generar PDF',
      onClick: handlePdfGeneration,
      color: 'primary' as const,
      variant: 'contained' as const,
      tooltip: 'Generar reporte en formato PDF',
    },
    ...(onPrint
      ? [
          {
            key: 'print',
            icon: <PrintIcon />,
            label: 'Imprimir',
            onClick: () => onPrint(report),
            color: 'secondary' as const,
            variant: 'outlined' as const,
            tooltip: 'Imprimir reporte',
          },
        ]
      : []),
    ...(onShare
      ? [
          {
            key: 'share',
            icon: <ShareIcon />,
            label: 'Compartir',
            onClick: () => onShare(report),
            color: 'info' as const,
            variant: 'outlined' as const,
            tooltip: 'Compartir reporte',
          },
        ]
      : []),
    ...(onDownload
      ? [
          {
            key: 'download',
            icon: <DownloadIcon />,
            label: 'Descargar',
            onClick: () => onDownload(report),
            color: 'success' as const,
            variant: 'outlined' as const,
            tooltip: 'Descargar reporte',
          },
        ]
      : []),
    ...(showCloseButton && onClose
      ? [
          {
            key: 'close',
            icon: <CloseIcon />,
            label: 'Cerrar',
            onClick: onClose,
            color: 'error' as const,
            variant: 'outlined' as const,
            tooltip: 'Cerrar vista de reporte',
          },
        ]
      : []),
  ];

  if (variant === 'vertical') {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        {buttons.map((button) => (
          <Tooltip key={button.key} title={button.tooltip} arrow>
            <Button
              {...buttonProps}
              startIcon={button.icon}
              onClick={button.onClick}
              color={button.color}
              variant={button.variant}
              fullWidth
            >
              {button.label}
            </Button>
          </Tooltip>
        ))}
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
      <ButtonGroup variant="outlined" size="small">
        {buttons.slice(0, -1).map((button) => (
          <Tooltip key={button.key} title={button.tooltip} arrow>
            <Button
              {...buttonProps}
              startIcon={button.icon}
              onClick={button.onClick}
              color={button.color}
              variant={button.variant}
            >
              {button.label}
            </Button>
          </Tooltip>
        ))}
      </ButtonGroup>

      {/* Botón de cerrar separado si existe */}
      {showCloseButton && onClose && (
        <Tooltip title="Cerrar vista de reporte" arrow>
          <Button
            {...buttonProps}
            startIcon={<CloseIcon />}
            onClick={onClose}
            color="error"
            variant="outlined"
          >
            Cerrar
          </Button>
        </Tooltip>
      )}
    </Box>
  );
};

export default ReportActions;
