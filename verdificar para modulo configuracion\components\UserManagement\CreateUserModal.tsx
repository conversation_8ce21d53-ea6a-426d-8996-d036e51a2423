import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  CircularProgress,
  IconButton,
  Divider,
} from '@mui/material';
import {
  Close as CloseIcon,
  PersonAdd as PersonAddIcon,
  AdminPanelSettings as AdminIcon,
  Psychology as PsychologyIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { UserRole } from '../../types/user';

interface CreateUserModalProps {
  open: boolean;
  onClose: () => void;
  onCreateUser: (userData: CreateUserData) => Promise<void>;
}

export interface CreateUserData {
  email: string;
  full_name: string;
  role: UserRole;
  password: string;
}

const roleOptions = [
  {
    value: 'administrador' as UserRole,
    label: 'Administrador',
    icon: <AdminIcon />,
    description: 'Acceso completo al sistema',
  },
  {
    value: 'psicologo' as User<PERSON><PERSON>,
    label: 'Psicólogo',
    icon: <PsychologyIcon />,
    description: 'Puede gestionar pacientes y evaluaciones',
  },
  {
    value: 'paciente' as UserRole,
    label: 'Paciente',
    icon: <PersonIcon />,
    description: 'Acceso limitado a sus propias evaluaciones',
  },
];

export const CreateUserModal: React.FC<CreateUserModalProps> = ({
  open,
  onClose,
  onCreateUser,
}) => {
  const [formData, setFormData] = useState<CreateUserData>({
    email: '',
    full_name: '',
    role: 'paciente',
    password: '',
  });
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<CreateUserData & { confirmPassword: string }>>({});

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        email: '',
        full_name: '',
        role: 'paciente',
        password: '',
      });
      setConfirmPassword('');
      setErrors({});
      onClose();
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<CreateUserData & { confirmPassword: string }> = {};

    // Validar email
    if (!formData.email) {
      newErrors.email = 'El email es requerido';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'El email no tiene un formato válido';
    }

    // Validar nombre
    if (!formData.full_name.trim()) {
      newErrors.full_name = 'El nombre completo es requerido';
    } else if (formData.full_name.trim().length < 2) {
      newErrors.full_name = 'El nombre debe tener al menos 2 caracteres';
    }

    // Validar contraseña
    if (!formData.password) {
      newErrors.password = 'La contraseña es requerida';
    } else if (formData.password.length < 6) {
      newErrors.password = 'La contraseña debe tener al menos 6 caracteres';
    }

    // Validar confirmación de contraseña
    if (formData.password !== confirmPassword) {
      newErrors.confirmPassword = 'Las contraseñas no coinciden';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      await onCreateUser(formData);
      handleClose();
    } catch (error) {
      console.error('Error creating user:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof CreateUserData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpiar error del campo cuando el usuario empiece a escribir
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setFormData(prev => ({ ...prev, password }));
    setConfirmPassword(password);
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        pb: 1,
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PersonAddIcon color="primary" />
          <Typography variant="h6" fontWeight={600}>
            Crear Nuevo Usuario
          </Typography>
        </Box>
        <IconButton onClick={handleClose} disabled={isLoading}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ pt: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          {/* Información básica */}
          <Box>
            <Typography variant="subtitle2" color="primary" fontWeight={600} sx={{ mb: 2 }}>
              Información Personal
            </Typography>
            
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="Nombre Completo"
                value={formData.full_name}
                onChange={(e) => handleInputChange('full_name', e.target.value)}
                error={!!errors.full_name}
                helperText={errors.full_name}
                disabled={isLoading}
                fullWidth
                required
              />

              <TextField
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                error={!!errors.email}
                helperText={errors.email}
                disabled={isLoading}
                fullWidth
                required
              />
            </Box>
          </Box>

          {/* Rol */}
          <Box>
            <Typography variant="subtitle2" color="primary" fontWeight={600} sx={{ mb: 2 }}>
              Rol del Usuario
            </Typography>
            
            <FormControl fullWidth required>
              <InputLabel>Rol</InputLabel>
              <Select
                value={formData.role}
                label="Rol"
                onChange={(e) => handleInputChange('role', e.target.value as UserRole)}
                disabled={isLoading}
              >
                {roleOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {option.icon}
                      <Box>
                        <Typography variant="body2" fontWeight={600}>
                          {option.label}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {option.description}
                        </Typography>
                      </Box>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          {/* Contraseña */}
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="subtitle2" color="primary" fontWeight={600}>
                Contraseña
              </Typography>
              <Button
                size="small"
                onClick={generatePassword}
                disabled={isLoading}
                variant="outlined"
              >
                Generar Automática
              </Button>
            </Box>
            
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="Contraseña"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                error={!!errors.password}
                helperText={errors.password}
                disabled={isLoading}
                fullWidth
                required
              />

              <TextField
                label="Confirmar Contraseña"
                type="password"
                value={confirmPassword}
                onChange={(e) => {
                  setConfirmPassword(e.target.value);
                  if (errors.confirmPassword) {
                    setErrors(prev => ({ ...prev, confirmPassword: undefined }));
                  }
                }}
                error={!!errors.confirmPassword}
                helperText={errors.confirmPassword}
                disabled={isLoading}
                fullWidth
                required
              />
            </Box>
          </Box>

          {/* Información adicional */}
          <Alert severity="info" sx={{ mt: 1 }}>
            <Typography variant="body2">
              El usuario recibirá sus credenciales por email y deberá cambiar su contraseña en el primer inicio de sesión.
            </Typography>
          </Alert>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 2 }}>
        <Button 
          onClick={handleClose} 
          disabled={isLoading}
          color="inherit"
        >
          Cancelar
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={isLoading}
          startIcon={isLoading ? <CircularProgress size={16} /> : <PersonAddIcon />}
          sx={{ backgroundColor: '#011129' }}
        >
          {isLoading ? 'Creando...' : 'Crear Usuario'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
