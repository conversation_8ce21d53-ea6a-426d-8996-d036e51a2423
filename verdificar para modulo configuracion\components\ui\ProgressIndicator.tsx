import React from 'react';
import { 
  Box, 
  LinearProgress, 
  Typography, 
  CircularProgress,
  LinearProgressProps 
} from '@mui/material';
import { styled } from '@mui/material/styles';

interface ProgressIndicatorProps {
  value: number; // 0-100
  total?: number;
  current?: number;
  variant?: 'linear' | 'circular' | 'steps';
  size?: 'small' | 'medium' | 'large';
  showLabel?: boolean;
  showPercentage?: boolean;
  label?: string;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  thickness?: number;
}

// Componente de progreso lineal estilizado
const StyledLinearProgress = styled(LinearProgress)(({ theme }) => ({
  height: 8,
  borderRadius: 4,
  backgroundColor: 'rgba(0, 0, 0, 0.06)',
  '& .MuiLinearProgress-bar': {
    borderRadius: 4,
    background: 'linear-gradient(90deg, #5A92C8 0%, #63B4A9 100%)',
  },
  '&.MuiLinearProgress-colorSecondary .MuiLinearProgress-bar': {
    background: 'linear-gradient(90deg, #63B4A9 0%, #52A398 100%)',
  },
  '&.MuiLinearProgress-colorSuccess .MuiLinearProgress-bar': {
    background: 'linear-gradient(90deg, #63B4A9 0%, #52A398 100%)',
  },
  '&.MuiLinearProgress-colorWarning .MuiLinearProgress-bar': {
    background: 'linear-gradient(90deg, #DDA15E 0%, #D6944B 100%)',
  },
  '&.MuiLinearProgress-colorError .MuiLinearProgress-bar': {
    background: 'linear-gradient(90deg, #F28A7C 0%, #EF7169 100%)',
  },
}));

// Componente de progreso circular estilizado
const StyledCircularProgress = styled(CircularProgress)(({ theme }) => ({
  '&.MuiCircularProgress-colorPrimary': {
    color: '#5A92C8',
  },
  '&.MuiCircularProgress-colorSecondary': {
    color: '#63B4A9',
  },
}));

// Componente de pasos
const StepsProgress: React.FC<{
  current: number;
  total: number;
  size: 'small' | 'medium' | 'large';
}> = ({ current, total, size }) => {
  const stepSize = size === 'small' ? 8 : size === 'medium' ? 12 : 16;
  const spacing = size === 'small' ? 4 : size === 'medium' ? 6 : 8;

  return (
    <Box display="flex" alignItems="center" gap={`${spacing}px`}>
      {Array.from({ length: total }, (_, index) => (
        <Box
          key={index}
          sx={{
            width: stepSize,
            height: stepSize,
            borderRadius: '50%',
            backgroundColor: index < current 
              ? '#5A92C8' 
              : index === current 
                ? '#63B4A9' 
                : 'rgba(0, 0, 0, 0.12)',
            transition: 'all 0.3s ease-in-out',
            transform: index === current ? 'scale(1.2)' : 'scale(1)',
          }}
        />
      ))}
    </Box>
  );
};

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  value,
  total = 100,
  current,
  variant = 'linear',
  size = 'medium',
  showLabel = true,
  showPercentage = true,
  label,
  color = 'primary',
  thickness = 4,
}) => {
  const percentage = Math.min(Math.max(value, 0), 100);
  const actualCurrent = current ?? Math.round((percentage / 100) * total);

  const getSize = () => {
    switch (size) {
      case 'small': return { height: 6, circularSize: 24 };
      case 'large': return { height: 12, circularSize: 48 };
      default: return { height: 8, circularSize: 32 };
    }
  };

  const sizes = getSize();

  if (variant === 'steps' && total && current !== undefined) {
    return (
      <Box>
        {showLabel && label && (
          <Typography 
            variant="body2" 
            color="text.secondary" 
            sx={{ mb: 1, fontSize: size === 'small' ? '0.75rem' : '0.875rem' }}
          >
            {label}
          </Typography>
        )}
        <Box display="flex" alignItems="center" gap={2}>
          <StepsProgress current={actualCurrent} total={total} size={size} />
          {showPercentage && (
            <Typography 
              variant="caption" 
              color="text.secondary"
              sx={{ fontSize: size === 'small' ? '0.7rem' : '0.75rem' }}
            >
              {actualCurrent} de {total}
            </Typography>
          )}
        </Box>
      </Box>
    );
  }

  if (variant === 'circular') {
    return (
      <Box display="flex" alignItems="center" gap={2}>
        <Box position="relative" display="inline-flex">
          <StyledCircularProgress
            variant="determinate"
            value={percentage}
            size={sizes.circularSize}
            thickness={thickness}
            color={color}
          />
          {showPercentage && (
            <Box
              sx={{
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Typography 
                variant="caption" 
                component="div" 
                color="text.secondary"
                sx={{ fontSize: size === 'small' ? '0.6rem' : '0.75rem' }}
              >
                {`${Math.round(percentage)}%`}
              </Typography>
            </Box>
          )}
        </Box>
        {showLabel && label && (
          <Typography 
            variant="body2" 
            color="text.secondary"
            sx={{ fontSize: size === 'small' ? '0.75rem' : '0.875rem' }}
          >
            {label}
          </Typography>
        )}
      </Box>
    );
  }

  // Linear progress (default)
  return (
    <Box sx={{ width: '100%' }}>
      {showLabel && label && (
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
          <Typography 
            variant="body2" 
            color="text.secondary"
            sx={{ fontSize: size === 'small' ? '0.75rem' : '0.875rem' }}
          >
            {label}
          </Typography>
          {showPercentage && (
            <Typography 
              variant="caption" 
              color="text.secondary"
              sx={{ fontSize: size === 'small' ? '0.7rem' : '0.75rem' }}
            >
              {current !== undefined && total ? `${actualCurrent}/${total}` : `${Math.round(percentage)}%`}
            </Typography>
          )}
        </Box>
      )}
      <StyledLinearProgress
        variant="determinate"
        value={percentage}
        color={color}
        sx={{ height: sizes.height }}
      />
    </Box>
  );
};

export default ProgressIndicator;
