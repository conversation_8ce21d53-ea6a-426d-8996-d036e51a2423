import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart,
} from 'recharts';

const sampleData = [
  { month: 'Ene', evaluaciones: 12, pacientes: 8, reportes: 5 },
  { month: 'Feb', evaluaciones: 18, pacientes: 12, reportes: 8 },
  { month: 'Mar', evaluaciones: 25, pacientes: 18, reportes: 12 },
  { month: 'Abr', evaluaciones: 22, pacientes: 15, reportes: 10 },
  { month: 'May', evaluaciones: 30, pacientes: 22, reportes: 15 },
  { month: 'Jun', evaluaciones: 35, pacientes: 25, reportes: 18 },
  { month: 'Jul', evaluaciones: 28, pacientes: 20, reportes: 14 },
];

interface TemporalTrendsChartProps {
  data?: typeof sampleData;
  height?: number;
  chartType?: 'line' | 'area';
}

const TemporalTrendsChart: React.FC<TemporalTrendsChartProps> = ({ 
  data = sampleData, 
  height = 300,
  chartType = 'area'
}) => {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: '#fff',
          border: '1px solid #5A92C8',
          borderRadius: '8px',
          padding: '12px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        }}>
          <p style={{ margin: 0, fontWeight: 'bold', color: '#333', marginBottom: '8px' }}>
            {label} 2024
          </p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ 
              margin: 0, 
              color: entry.color,
              fontSize: '14px'
            }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (chartType === 'area') {
    return (
      <ResponsiveContainer width="100%" height={height}>
        <AreaChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="month" 
            tick={{ fontSize: 12 }}
            stroke="#666"
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            stroke="#666"
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Area
            type="monotone"
            dataKey="evaluaciones"
            stackId="1"
            stroke="#5A92C8"
            fill="#5A92C8"
            fillOpacity={0.6}
            name="Evaluaciones"
          />
          <Area
            type="monotone"
            dataKey="pacientes"
            stackId="1"
            stroke="#63B4A9"
            fill="#63B4A9"
            fillOpacity={0.6}
            name="Nuevos Pacientes"
          />
          <Area
            type="monotone"
            dataKey="reportes"
            stackId="1"
            stroke="#F28A7C"
            fill="#F28A7C"
            fillOpacity={0.6}
            name="Reportes Generados"
          />
        </AreaChart>
      </ResponsiveContainer>
    );
  }

  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart
        data={data}
        margin={{
          top: 20,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="month" 
          tick={{ fontSize: 12 }}
          stroke="#666"
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          stroke="#666"
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Line
          type="monotone"
          dataKey="evaluaciones"
          stroke="#5A92C8"
          strokeWidth={3}
          dot={{ fill: '#5A92C8', strokeWidth: 2, r: 4 }}
          name="Evaluaciones"
        />
        <Line
          type="monotone"
          dataKey="pacientes"
          stroke="#63B4A9"
          strokeWidth={3}
          dot={{ fill: '#63B4A9', strokeWidth: 2, r: 4 }}
          name="Nuevos Pacientes"
        />
        <Line
          type="monotone"
          dataKey="reportes"
          stroke="#F28A7C"
          strokeWidth={3}
          dot={{ fill: '#F28A7C', strokeWidth: 2, r: 4 }}
          name="Reportes Generados"
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default TemporalTrendsChart;
