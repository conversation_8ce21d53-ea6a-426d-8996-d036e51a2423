import { useState, useEffect, useCallback } from 'react';
import { 
  getCohortAnalysis, 
  getGeneralStatistics, 
  getTemporalTrends,
  invalidateAnalyticsCache,
  getCacheStats
} from '../services/analyticsService';
import type { CohortFilters, GeneralStats, TemporalTrendData } from '../types/analytics';

interface UseAnalyticsCacheReturn {
  // Estados de datos
  generalStats: GeneralStats | null;
  cohortData: Array<{ name: string; Masculino: number; Femenino: number; Otro: number }>;
  temporalData: TemporalTrendData[];
  
  // Estados de carga
  loadingGeneral: boolean;
  loadingCohort: boolean;
  loadingTemporal: boolean;
  
  // Funciones
  loadGeneralStats: () => Promise<void>;
  loadCohortData: (filters?: CohortFilters) => Promise<void>;
  loadTemporalData: () => Promise<void>;
  invalidateCache: () => void;
  
  // Estadísticas del caché
  cacheStats: ReturnType<typeof getCacheStats>;
}

/**
 * Hook personalizado para manejar el caché de analytics
 */
export const useAnalyticsCache = (): UseAnalyticsCacheReturn => {
  // Estados de datos
  const [generalStats, setGeneralStats] = useState<GeneralStats | null>(null);
  const [cohortData, setCohortData] = useState<Array<{ name: string; Masculino: number; Femenino: number; Otro: number }>>([]);
  const [temporalData, setTemporalData] = useState<TemporalTrendData[]>([]);
  
  // Estados de carga
  const [loadingGeneral, setLoadingGeneral] = useState(false);
  const [loadingCohort, setLoadingCohort] = useState(false);
  const [loadingTemporal, setLoadingTemporal] = useState(false);
  
  // Estadísticas del caché
  const [cacheStats, setCacheStats] = useState(getCacheStats());

  // Función para cargar estadísticas generales
  const loadGeneralStats = useCallback(async () => {
    if (loadingGeneral) return;
    
    try {
      setLoadingGeneral(true);
      const stats = await getGeneralStatistics();
      setGeneralStats(stats);
      setCacheStats(getCacheStats());
    } catch (error) {
      console.error('Error loading general stats:', error);
    } finally {
      setLoadingGeneral(false);
    }
  }, [loadingGeneral]);

  // Función para cargar datos de cohortes
  const loadCohortData = useCallback(async (filters: CohortFilters = {}) => {
    if (loadingCohort) return;
    
    try {
      setLoadingCohort(true);
      const data = await getCohortAnalysis(filters);
      
      // Transformar datos para el gráfico
      const ageGroups = [...new Set(data.map(item => item.age_group))];
      const chartData = ageGroups.map(ageGroup => {
        const groupData = data.filter(item => item.age_group === ageGroup);
        
        const result: any = { name: ageGroup };
        
        groupData.forEach(item => {
          const genderKey = item.gender === 'M' || item.gender === 'Masculino' ? 'Masculino' :
                           item.gender === 'F' || item.gender === 'Femenino' ? 'Femenino' : 'Otro';
          result[genderKey] = item.patient_count;
        });
        
        // Asegurar que todas las categorías existan
        if (!result.Masculino) result.Masculino = 0;
        if (!result.Femenino) result.Femenino = 0;
        if (!result.Otro) result.Otro = 0;
        
        return result;
      });
      
      setCohortData(chartData);
      setCacheStats(getCacheStats());
    } catch (error) {
      console.error('Error loading cohort data:', error);
    } finally {
      setLoadingCohort(false);
    }
  }, [loadingCohort]);

  // Función para cargar datos temporales
  const loadTemporalData = useCallback(async () => {
    if (loadingTemporal) return;
    
    try {
      setLoadingTemporal(true);
      const data = await getTemporalTrends();
      setTemporalData(data);
      setCacheStats(getCacheStats());
    } catch (error) {
      console.error('Error loading temporal data:', error);
    } finally {
      setLoadingTemporal(false);
    }
  }, [loadingTemporal]);

  // Función para invalidar caché
  const invalidateCache = useCallback(() => {
    invalidateAnalyticsCache();
    setCacheStats(getCacheStats());
    
    // Recargar datos después de invalidar caché
    loadGeneralStats();
    loadTemporalData();
  }, [loadGeneralStats, loadTemporalData]);

  // Actualizar estadísticas del caché periódicamente
  useEffect(() => {
    const interval = setInterval(() => {
      setCacheStats(getCacheStats());
    }, 30000); // Cada 30 segundos

    return () => clearInterval(interval);
  }, []);

  return {
    // Estados de datos
    generalStats,
    cohortData,
    temporalData,
    
    // Estados de carga
    loadingGeneral,
    loadingCohort,
    loadingTemporal,
    
    // Funciones
    loadGeneralStats,
    loadCohortData,
    loadTemporalData,
    invalidateCache,
    
    // Estadísticas del caché
    cacheStats,
  };
};
