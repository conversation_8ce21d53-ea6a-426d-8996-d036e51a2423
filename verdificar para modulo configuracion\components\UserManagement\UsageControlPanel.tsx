/**
 * Panel de Control de Usos - Administración
 * Permite gestionar límites de uso y generar informes
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  LinearProgress,
  Checkbox,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Badge,
  Divider,
  InputAdornment
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Assessment as ReportIcon,
  History as HistoryIcon,
  Person as PersonIcon,
  PersonAdd as PersonAddIcon,
  Psychology as PsychologyIcon,
  Apps as AppsIcon,
  TrendingUp as TrendingUpIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  SelectAll as SelectAllIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { appUsageService, supabase, AppUsageLimit, UserUsageInfo, UsageStatistics } from '../../services/appUsageService';
import { userManagementService, type UserProfile } from '../../services/userManagement';
import UsageReportsPanel from './UsageReportsPanel';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { ModuleWrapper } from '../Admin/ModuleFallback';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`usage-tabpanel-${index}`}
      aria-labelledby={`usage-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const UsageControlPanel: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Estados para datos
  const [appLimits, setAppLimits] = useState<AppUsageLimit[]>([]);
  const [usersUsage, setUsersUsage] = useState<UserUsageInfo[]>([]);
  const [statistics, setStatistics] = useState<UsageStatistics[]>([]);
  
  // Estados para diálogos
  const [assignUsageDialog, setAssignUsageDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserUsageInfo | null>(null);
  const [selectedApp, setSelectedApp] = useState('');
  const [usesToAssign, setUsesToAssign] = useState(10);
  const [assigningUsage, setAssigningUsage] = useState(false);
  const [currentUserUsage, setCurrentUserUsage] = useState<{[key: string]: number}>({});
  
  // Estados para edición de límites
  const [editLimitDialog, setEditLimitDialog] = useState(false);
  const [selectedLimit, setSelectedLimit] = useState<AppUsageLimit | null>(null);
  const [newDefaultLimit, setNewDefaultLimit] = useState(0);

  // Estados para historial de usuario
  const [userHistoryDialog, setUserHistoryDialog] = useState(false);
  const [selectedUserForHistory, setSelectedUserForHistory] = useState<UserUsageInfo | null>(null);
  const [userHistory, setUserHistory] = useState<any[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(false);

  // Estados para búsqueda y filtrado
  const [searchTerm, setSearchTerm] = useState('');

  // Estados para añadir psicólogos al sistema de usos
  const [allPsychologists, setAllPsychologists] = useState<UserProfile[]>([]);
  const [addPsychologistDialog, setAddPsychologistDialog] = useState(false);
  const [selectedPsychologistsToAdd, setSelectedPsychologistsToAdd] = useState<string[]>([]);
  const [isAddingPsychologists, setIsAddingPsychologists] = useState(false);

  // Estados para acciones en lote (bulk actions)
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [bulkActionDialog, setBulkActionDialog] = useState(false);
  const [bulkUsesToAssign, setBulkUsesToAssign] = useState(10);
  const [bulkSelectedApp, setBulkSelectedApp] = useState('');
  const [isBulkAssigning, setIsBulkAssigning] = useState(false);

  // Estados para alertas y notificaciones
  const [lowUsageAlerts, setLowUsageAlerts] = useState<UserUsageInfo[]>([]);
  const [showAlertsPanel, setShowAlertsPanel] = useState(false);

  // Estados para indicadores de carga granulares
  const [initializingUsers, setInitializingUsers] = useState<Set<string>>(new Set());
  const [updatingLimit, setUpdatingLimit] = useState(false);

  useEffect(() => {
    // Cargar datos iniciales
    loadInitialData();

    // Configurar suscripciones en tiempo real
    const subscriptions = setupRealtimeSubscriptions();

    // Cleanup: cancelar suscripciones al desmontar el componente
    return () => {
      subscriptions.forEach(subscription => {
        appUsageService.unsubscribeFromChannel(subscription);
      });
    };
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    setError(null);

    try {
      const [limitsResult, usersResult, statsResult, allUsersResult] = await Promise.all([
        appUsageService.getAppLimits(),
        appUsageService.getAllUsersUsage(),
        appUsageService.getUsageStatistics(),
        userManagementService.getAllUsers()
      ]);

      if (!limitsResult.success) {
        throw new Error(limitsResult.error || 'Error al cargar límites');
      }

      if (!usersResult.success) {
        throw new Error(usersResult.error || 'Error al cargar usuarios');
      }

      if (!statsResult.success) {
        throw new Error(statsResult.message || 'Error al cargar estadísticas');
      }

      setAppLimits(limitsResult.limits);

      // Cargar todos los psicólogos para la funcionalidad de añadir
      if (allUsersResult.data) {
        setAllPsychologists(
          allUsersResult.data.filter(u => u.role === 'psicologo')
        );
      }

      // Detectar alertas de usuarios con pocos usos
      const alerts = usersResult.users.filter(user => {
        const totalRemaining = user.usages.reduce((sum, usage) => sum + usage.remaining_uses, 0);
        return user.user_role === 'psicologo' && totalRemaining <= 5 && totalRemaining > 0;
      });
      setLowUsageAlerts(alerts);
      setUsersUsage(usersResult.users);
      setStatistics(statsResult.statistics);
    } catch (err) {
      console.error('Error loading data:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  const setupRealtimeSubscriptions = () => {
    const subscriptions: any[] = [];

    // Suscripción a cambios en app_usage_limits
    const limitsSubscription = appUsageService.subscribeToAppLimits(async (payload) => {
      console.log('App limits changed:', payload);
      // Recargar límites cuando hay cambios
      const limitsResult = await appUsageService.getAppLimits();
      if (limitsResult.success) {
        setAppLimits(limitsResult.limits);
      }
    });
    subscriptions.push(limitsSubscription);

    // Suscripción a cambios en app_usage (usos de usuarios)
    const usageSubscription = appUsageService.subscribeToUsersUsage(async (payload) => {
      console.log('User usage changed:', payload);
      // Recargar datos de usuarios cuando hay cambios
      const usersResult = await appUsageService.getAllUsersUsage();
      if (usersResult.success) {
        setUsersUsage(usersResult.users);
      }

      // También actualizar estadísticas
      const statsResult = await appUsageService.getUsageStatistics();
      if (statsResult.success) {
        setStatistics(statsResult.statistics);
      }
    });
    subscriptions.push(usageSubscription);

    // Suscripción a cambios en profiles (información de usuarios)
    const profilesSubscription = appUsageService.subscribeToProfiles(async (payload) => {
      console.log('Profiles changed:', payload);
      // Recargar datos de usuarios cuando cambian los perfiles
      const usersResult = await appUsageService.getAllUsersUsage();
      if (usersResult.success) {
        setUsersUsage(usersResult.users);
      }
    });
    subscriptions.push(profilesSubscription);

    // Suscripción a cambios en app_usage_history
    const historySubscription = appUsageService.subscribeToUsageHistory(async (payload) => {
      console.log('Usage history changed:', payload);
      // Actualizar estadísticas cuando hay cambios en el historial
      const statsResult = await appUsageService.getUsageStatistics();
      if (statsResult.success) {
        setStatistics(statsResult.statistics);
      }
    });
    subscriptions.push(historySubscription);

    return subscriptions;
  };

  const handleAssignUsage = async () => {
    if (!selectedUser || !selectedApp || usesToAssign <= 0) return;

    try {
      setAssigningUsage(true);
      const result = await appUsageService.assignUsage(
        selectedUser.user_id,
        selectedApp,
        usesToAssign
      );

      if (result.success) {
        setAssignUsageDialog(false);
        setSelectedUser(null);
        setSelectedApp('');
        setUsesToAssign(10);
        setCurrentUserUsage({});
        toast.success(`✅ Se asignaron ${usesToAssign} usos exitosamente a ${selectedUser.user_name}`);
        // Los datos se actualizarán automáticamente via suscripciones
      } else {
        toast.error(`❌ Error: ${result.message}`);
      }
    } catch (err: any) {
      console.error('Error assigning usage:', err);

      // Manejo específico de errores de base de datos
      if (err?.message?.includes('column') && err?.message?.includes('does not exist')) {
        toast.error('❌ Error de configuración de base de datos. La función ha sido corregida, intenta nuevamente.');
      } else if (err?.message?.includes('permission') || err?.message?.includes('administrador')) {
        toast.error('❌ No tienes permisos para realizar esta acción.');
      } else if (err?.message?.includes('network') || err?.code === 'NETWORK_ERROR') {
        toast.error('❌ Error de conexión. Verifica tu conexión a internet.');
      } else if (err?.message?.includes('constraint') || err?.message?.includes('check')) {
        toast.error('❌ Error de validación en base de datos. Contacta al administrador.');
      } else {
        toast.error(`❌ Error al asignar usos: ${err?.message || 'Error desconocido'}`);
      }
    } finally {
      setAssigningUsage(false);
    }
  };

  // Función para asignación rápida de usos
  const handleQuickAssign = async (user: UserUsageInfo, amount: number) => {
    if (!user.usages || user.usages.length === 0) {
      toast.error('❌ El usuario no tiene aplicaciones inicializadas');
      return;
    }

    // Usar la primera aplicación disponible (normalmente MACI-II)
    const firstApp = user.usages[0];

    try {
      const result = await appUsageService.assignUsage(
        user.user_id,
        firstApp.app_id,
        amount
      );

      if (result.success) {
        const action = amount > 0 ? 'asignaron' : 'removieron';
        const absAmount = Math.abs(amount);
        toast.success(`✅ Se ${action} ${absAmount} usos a ${user.user_name}`);
        // Los datos se actualizarán automáticamente via suscripciones
      } else {
        toast.error(`❌ Error: ${result.message}`);
      }
    } catch (err: any) {
      console.error('Error in quick assign:', err);
      toast.error(`❌ Error al ${amount > 0 ? 'asignar' : 'remover'} usos: ${err?.message || 'Error desconocido'}`);
    }
  };

  // Función para obtener usos actuales del usuario para una app específica
  const getCurrentUsageForApp = (userId: string, appId: string): number => {
    // Programación defensiva: verificar que usersUsage existe y es un array
    if (!usersUsage || !Array.isArray(usersUsage)) {
      console.warn('⚠️ usersUsage no está disponible o no es un array:', usersUsage);
      return 0;
    }

    const userUsage = usersUsage.find(u => u?.user_id === userId);
    if (!userUsage) {
      console.log('🔍 No se encontró usage para usuario:', userId);
      return 0;
    }

    // Verificar que userUsage.usages existe y es un array
    if (!userUsage.usages || !Array.isArray(userUsage.usages)) {
      console.warn('⚠️ userUsage.usages no está disponible o no es un array:', userUsage.usages);
      return 0;
    }

    const appUsage = userUsage.usages.find(app => app?.app_id === appId);
    return appUsage?.remaining_uses || 0;
  };

  // Función para cargar usos actuales cuando se selecciona usuario/app
  const loadCurrentUsageForUser = async (userId: string) => {
    try {
      // Programación defensiva
      if (!usersUsage || !Array.isArray(usersUsage)) {
        console.warn('⚠️ usersUsage no disponible en loadCurrentUsageForUser');
        setCurrentUserUsage({});
        return;
      }

      const userUsage = usersUsage.find(u => u?.user_id === userId);
      if (userUsage && userUsage.usages && Array.isArray(userUsage.usages)) {
        const usageMap: {[key: string]: number} = {};
        userUsage.usages.forEach(app => {
          if (app?.app_id) {
            usageMap[app.app_id] = app.remaining_uses || 0;
          }
        });
        setCurrentUserUsage(usageMap);
      } else {
        console.log('🔍 No se encontraron usages para usuario:', userId);
        setCurrentUserUsage({});
      }
    } catch (error) {
      console.error('Error loading current usage:', error);
    }
  };

  // Función para cargar historial de un usuario específico
  const loadUserHistory = async (user: UserUsageInfo) => {
    setSelectedUserForHistory(user);
    setUserHistoryDialog(true);
    setLoadingHistory(true);

    try {
      const result = await appUsageService.getUserUsageHistory(user.user_id);
      if (result.success) {
        setUserHistory(result.history);
        if (result.history.length === 0) {
          toast.info(`ℹ️ No hay historial de uso para ${user.user_name}`);
        }
      } else {
        toast.error(`❌ Error: ${result.error || 'Error al cargar historial'}`);
        setUserHistory([]);
      }
    } catch (error) {
      console.error('Error loading user history:', error);
      toast.error('❌ Error de conexión al cargar historial del usuario');
      setUserHistory([]);
    } finally {
      setLoadingHistory(false);
    }
  };

  // Psicólogos que no están en el sistema de control de usos
  const uninitializedPsychologists = React.useMemo(() => {
    if (!allPsychologists.length) return [];
    const initializedUserIds = new Set(usersUsage.map(u => u.user_id));
    return allPsychologists.filter(p => !initializedUserIds.has(p.id));
  }, [usersUsage, allPsychologists]);

  // Función para filtrar usuarios por búsqueda
  const filteredUsers = usersUsage.filter(user => {
    // Mostrar solo psicólogos y administradores (los administradores pueden necesitar usos para pruebas)
    const isRelevantRole = user.user_role === 'psicologo' || user.user_role === 'administrador';

    if (!searchTerm) return isRelevantRole;

    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = (
      user.user_name.toLowerCase().includes(searchLower) ||
      user.user_email.toLowerCase().includes(searchLower) ||
      user.user_role.toLowerCase().includes(searchLower)
    );

    return isRelevantRole && matchesSearch;
  });

  // Función para manejar selección de usuarios (bulk actions)
  const handleUserSelection = (userId: string, checked: boolean) => {
    setSelectedUsers(prev =>
      checked
        ? [...prev, userId]
        : prev.filter(id => id !== userId)
    );
  };

  // Función para seleccionar/deseleccionar todos los usuarios
  const handleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredUsers.map(u => u.user_id));
    }
  };

  // Función para añadir psicólogos al sistema de usos
  const handleConfirmAddPsychologists = async () => {
    setIsAddingPsychologists(true);
    const promises = selectedPsychologistsToAdd.map(userId =>
      appUsageService.initializeUserUsage(userId)
    );

    try {
      await Promise.all(promises);
      toast.success(`✅ ${selectedPsychologistsToAdd.length} psicólogo(s) añadido(s) al sistema.`);
      setAddPsychologistDialog(false);
      setSelectedPsychologistsToAdd([]);
      // Los datos se recargarán automáticamente por las suscripciones
    } catch (error) {
      console.error('Error adding psychologists:', error);
      toast.error('❌ Error al añadir psicólogos.');
    } finally {
      setIsAddingPsychologists(false);
    }
  };

  // Función para asignación en lote
  const handleBulkAssignUsage = async () => {
    if (!bulkSelectedApp || bulkUsesToAssign === 0 || selectedUsers.length === 0) return;

    setIsBulkAssigning(true);
    const promises = selectedUsers.map(userId =>
      appUsageService.assignUsage(userId, bulkSelectedApp, bulkUsesToAssign)
    );

    try {
      const results = await Promise.all(promises);
      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      if (successCount > 0) {
        toast.success(`✅ Se asignaron ${bulkUsesToAssign} usos a ${successCount} usuario(s).`);
      }
      if (failCount > 0) {
        toast.warning(`⚠️ ${failCount} asignación(es) fallaron.`);
      }

      setBulkActionDialog(false);
      setSelectedUsers([]);
      setBulkSelectedApp('');
      setBulkUsesToAssign(10);
    } catch (error) {
      console.error('Error in bulk assign:', error);
      toast.error('❌ Error en asignación en lote.');
    } finally {
      setIsBulkAssigning(false);
    }
  };

  const handleInitializeUser = async (userId: string) => {
    try {
      setInitializingUsers(prev => new Set(prev).add(userId));
      const result = await appUsageService.initializeUserUsage(userId);

      if (result.success) {
        toast.success(`✅ Usuario inicializado correctamente`);
        // Los datos se actualizarán automáticamente via suscripciones
      } else {
        toast.error(`❌ Error: ${result.message}`);
      }
    } catch (err) {
      console.error('Error initializing user:', err);
      toast.error('❌ Error de conexión al inicializar usuario');
    } finally {
      setInitializingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    }
  };

  const handleUpdateLimit = async () => {
    if (!selectedLimit || newDefaultLimit <= 0) return;

    try {
      setUpdatingLimit(true);
      const result = await appUsageService.updateAppLimit(selectedLimit.app_id, {
        default_uses_limit: newDefaultLimit
      });

      if (result.success) {
        setEditLimitDialog(false);
        setSelectedLimit(null);
        setNewDefaultLimit(0);
        toast.success(`✅ Límite actualizado correctamente para ${selectedLimit.app_name}`);
        // Los datos se actualizarán automáticamente via suscripciones
      } else {
        toast.error(`❌ Error: ${result.message}`);
      }
    } catch (err) {
      console.error('Error updating limit:', err);
      toast.error('❌ Error de conexión al actualizar límite');
    } finally {
      setUpdatingLimit(false);
    }
  };

  const exportToCSV = (data: any[], filename: string) => {
    if (data.length === 0) return;

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          return typeof value === 'string' && value.includes(',') 
            ? `"${value}"` 
            : value;
        }).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <ModuleWrapper
      loading={loading}
      error={error}
      onRetry={loadInitialData}
    >
    <Box>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab icon={<TrendingUpIcon />} label="Estadísticas" />
          <Tab icon={<PersonIcon />} label="Usuarios" />
          <Tab icon={<AppsIcon />} label="Aplicaciones" />
          <Tab icon={<ReportIcon />} label="Informes" />
        </Tabs>
      </Box>

      {/* Tab 1: Estadísticas */}
      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          {statistics.map((stat) => (
            <Grid item xs={12} md={6} lg={4} key={stat.app_id}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {stat.app_name}
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Progreso de uso
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={stat.usage_percentage} 
                      sx={{ mt: 1 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      {stat.usage_percentage.toFixed(1)}% utilizado
                    </Typography>
                  </Box>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Usuarios totales
                      </Typography>
                      <Typography variant="h6">
                        {stat.total_users}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Usos restantes
                      </Typography>
                      <Typography variant="h6">
                        {stat.total_remaining_uses}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Total asignado
                      </Typography>
                      <Typography variant="h6">
                        {stat.total_assigned_uses}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Total usado
                      </Typography>
                      <Typography variant="h6">
                        {stat.total_used}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      {/* Tab 2: Usuarios */}
      <TabPanel value={tabValue} index={1}>
        {/* Panel de Alertas */}
        {lowUsageAlerts.length > 0 && (
          <Alert
            severity="warning"
            sx={{ mb: 3 }}
            action={
              <Button
                color="inherit"
                size="small"
                onClick={() => setShowAlertsPanel(!showAlertsPanel)}
              >
                {showAlertsPanel ? 'Ocultar' : 'Ver Detalles'}
              </Button>
            }
          >
            <Typography variant="body2">
              <strong>⚠️ {lowUsageAlerts.length} usuario(s) con pocos usos disponibles</strong>
            </Typography>
            {showAlertsPanel && (
              <Box sx={{ mt: 2 }}>
                {lowUsageAlerts.map(user => {
                  const totalRemaining = user.usages.reduce((sum, usage) => sum + usage.remaining_uses, 0);
                  return (
                    <Typography key={user.user_id} variant="caption" display="block">
                      • {user.user_name}: {totalRemaining} usos restantes
                    </Typography>
                  );
                })}
              </Box>
            )}
          </Alert>
        )}

        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Gestión de Usuarios</Typography>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <TextField
              size="small"
              placeholder="Buscar por nombre, email o rol..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{ minWidth: 250 }}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  )
                }
              }}
            />
            <Button
              variant="contained"
              startIcon={<PersonAddIcon />}
              onClick={() => setAddPsychologistDialog(true)}
              disabled={uninitializedPsychologists.length === 0}
            >
              Añadir Psicólogo ({uninitializedPsychologists.length})
            </Button>
            {selectedUsers.length > 0 && (
              <Button
                variant="contained"
                color="secondary"
                startIcon={<SelectAllIcon />}
                onClick={() => setBulkActionDialog(true)}
              >
                Acciones en Lote ({selectedUsers.length})
              </Button>
            )}
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadInitialData}
            >
              Actualizar
            </Button>
          </Box>
        </Box>

        {/* Mostrar contador de resultados */}
        {searchTerm && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Mostrando {filteredUsers.length} de {usersUsage.length} usuarios
              {searchTerm && ` para "${searchTerm}"`}
            </Typography>
          </Box>
        )}

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selectedUsers.length > 0 && selectedUsers.length < filteredUsers.length}
                    checked={filteredUsers.length > 0 && selectedUsers.length === filteredUsers.length}
                    onChange={handleSelectAll}
                    slotProps={{
                      input: { 'aria-label': 'Seleccionar todos los usuarios' }
                    }}
                  />
                </TableCell>
                <TableCell>Usuario</TableCell>
                <TableCell>Rol</TableCell>
                <TableCell>Estado de Usos por Aplicación</TableCell>
                <TableCell>Acciones</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow
                  key={user.user_id}
                  selected={selectedUsers.includes(user.user_id)}
                  hover
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedUsers.includes(user.user_id)}
                      onChange={(e) => handleUserSelection(user.user_id, e.target.checked)}
                      slotProps={{
                        input: { 'aria-label': `Seleccionar ${user.user_name}` }
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        {user.user_name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {user.user_email}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={user.user_role} 
                      size="small"
                      color={user.user_role === 'administrador' ? 'primary' : 'default'}
                    />
                  </TableCell>
                  <TableCell>
                    {user.usages.length === 0 ? (
                      <Typography variant="caption" color="text.secondary">
                        Sin usos asignados
                      </Typography>
                    ) : (
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, minWidth: 300 }}>
                        {user.usages.map((usage) => {
                          const usedCount = usage.total_assigned - usage.remaining_uses;
                          const percentage = usage.total_assigned > 0 ? (usage.remaining_uses / usage.total_assigned) * 100 : 0;

                          return (
                            <Box key={usage.app_id} sx={{ p: 1.5, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                              <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                                {usage.app_name}
                              </Typography>

                              {/* Resumen de usos */}
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Box sx={{ textAlign: 'center' }}>
                                  <Typography variant="h6" color="success.main" sx={{ fontWeight: 'bold', lineHeight: 1 }}>
                                    {usage.remaining_uses}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Disponibles
                                  </Typography>
                                </Box>
                                <Box sx={{ textAlign: 'center' }}>
                                  <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 'bold', lineHeight: 1 }}>
                                    {usedCount}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Usados
                                  </Typography>
                                </Box>
                                <Box sx={{ textAlign: 'center' }}>
                                  <Typography variant="h6" color="primary.main" sx={{ fontWeight: 'bold', lineHeight: 1 }}>
                                    {usage.total_assigned}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Total Asignado
                                  </Typography>
                                </Box>
                              </Box>

                              {/* Barra de progreso */}
                              <LinearProgress
                                variant="determinate"
                                value={percentage}
                                color={percentage < 25 ? 'error' : percentage < 50 ? 'warning' : 'success'}
                                sx={{
                                  height: 8,
                                  borderRadius: 4,
                                  backgroundColor: 'rgba(0,0,0,0.1)',
                                  '& .MuiLinearProgress-bar': {
                                    borderRadius: 4,
                                  }
                                }}
                              />

                              {/* Porcentaje */}
                              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textAlign: 'center', mt: 0.5 }}>
                                {percentage.toFixed(1)}% disponible
                              </Typography>
                            </Box>
                          );
                        })}
                      </Box>
                    )}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      {/* Botones principales */}
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        <Button
                          size="small"
                          variant="contained"
                          startIcon={<AddIcon />}
                          onClick={() => {
                            setSelectedUser(user);
                            loadCurrentUsageForUser(user.user_id);
                            setAssignUsageDialog(true);
                          }}
                          sx={{ minWidth: 120 }}
                        >
                          Asignar Usos
                        </Button>

                        <Tooltip title="Ver historial de uso">
                          <IconButton
                            size="small"
                            onClick={() => loadUserHistory(user)}
                            color="info"
                          >
                            <HistoryIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>

                      {/* Acciones rápidas para psicólogos con usos */}
                      {user.user_role === 'psicologo' && user.usages.length > 0 && (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          <Button
                            size="small"
                            variant="outlined"
                            color="success"
                            onClick={() => handleQuickAssign(user, 5)}
                            sx={{ fontSize: '0.7rem', minWidth: 'auto', px: 1 }}
                          >
                            +5
                          </Button>
                          <Button
                            size="small"
                            variant="outlined"
                            color="success"
                            onClick={() => handleQuickAssign(user, 10)}
                            sx={{ fontSize: '0.7rem', minWidth: 'auto', px: 1 }}
                          >
                            +10
                          </Button>
                          <Button
                            size="small"
                            variant="outlined"
                            color="warning"
                            onClick={() => handleQuickAssign(user, -5)}
                            sx={{ fontSize: '0.7rem', minWidth: 'auto', px: 1 }}
                          >
                            -5
                          </Button>
                        </Box>
                      )}

                      {/* Botón de inicializar para usuarios sin usos */}
                      {user.usages.length === 0 && (
                        <Button
                          size="small"
                          variant="outlined"
                          startIcon={initializingUsers.has(user.user_id) ? <CircularProgress size={16} /> : <PersonIcon />}
                          onClick={() => handleInitializeUser(user.user_id)}
                          disabled={initializingUsers.has(user.user_id)}
                          sx={{ minWidth: 120 }}
                        >
                          {initializingUsers.has(user.user_id) ? 'Inicializando...' : 'Inicializar'}
                        </Button>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Tab 3: Aplicaciones */}
      <TabPanel value={tabValue} index={2}>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="h6">Configuración de Aplicaciones</Typography>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Aplicación</TableCell>
                <TableCell>Descripción</TableCell>
                <TableCell>Límite por Defecto</TableCell>
                <TableCell>Estado</TableCell>
                <TableCell>Acciones</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {appLimits.map((limit) => (
                <TableRow key={limit.app_id}>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {limit.app_name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      ID: {limit.app_id}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {limit.app_description || 'Sin descripción'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="h6">
                      {limit.default_uses_limit}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={limit.is_active ? 'Activa' : 'Inactiva'}
                      color={limit.is_active ? 'success' : 'error'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => {
                        setSelectedLimit(limit);
                        setNewDefaultLimit(limit.default_uses_limit);
                        setEditLimitDialog(true);
                      }}
                    >
                      <EditIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Tab 4: Informes */}
      <TabPanel value={tabValue} index={3}>
        <UsageReportsPanel />
      </TabPanel>

      {/* Diálogo para asignar usos */}
      <Dialog
        open={assignUsageDialog}
        onClose={() => setAssignUsageDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <PersonIcon color="primary" />
            Asignar Usos a Usuario
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {/* Información del usuario */}
            <Card sx={{ mb: 3, bgcolor: 'background.default' }}>
              <CardContent sx={{ pb: '16px !important' }}>
                <Typography variant="h6" gutterBottom>
                  👤 {selectedUser?.user_name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  ID: {selectedUser?.user_id}
                </Typography>
              </CardContent>
            </Card>

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Aplicación</InputLabel>
              <Select
                value={selectedApp}
                onChange={(e) => {
                  setSelectedApp(e.target.value);
                  if (selectedUser) {
                    loadCurrentUsageForUser(selectedUser.user_id);
                  }
                }}
                label="Aplicación"
              >
                {appLimits.map((limit) => (
                  <MenuItem key={limit.app_id} value={limit.app_id}>
                    <Box display="flex" justifyContent="space-between" width="100%">
                      <span>{limit.app_name}</span>
                      <Chip
                        size="small"
                        label={`Límite: ${limit.default_uses_limit}`}
                        color="info"
                        variant="outlined"
                      />
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Mostrar usos actuales si hay app seleccionada */}
            {selectedApp && selectedUser && (
              <Card sx={{ mb: 3, bgcolor: 'info.light', color: 'info.contrastText' }}>
                <CardContent sx={{ pb: '16px !important' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    📊 Estado Actual
                  </Typography>
                  <Typography variant="body2">
                    Usos restantes: <strong>{getCurrentUsageForApp(selectedUser.user_id, selectedApp)}</strong>
                  </Typography>
                  <Typography variant="body2">
                    Después de asignar: <strong>{getCurrentUsageForApp(selectedUser.user_id, selectedApp) + usesToAssign}</strong>
                  </Typography>
                </CardContent>
              </Card>
            )}

            {/* Botones de acción rápida */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Acciones Rápidas
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                <Button
                  variant="outlined"
                  color="success"
                  size="small"
                  onClick={() => setUsesToAssign(5)}
                >
                  +5 Usos
                </Button>
                <Button
                  variant="outlined"
                  color="success"
                  size="small"
                  onClick={() => setUsesToAssign(10)}
                >
                  +10 Usos
                </Button>
                <Button
                  variant="outlined"
                  color="success"
                  size="small"
                  onClick={() => setUsesToAssign(25)}
                >
                  +25 Usos
                </Button>
                <Button
                  variant="outlined"
                  color="warning"
                  size="small"
                  onClick={() => setUsesToAssign(-5)}
                >
                  -5 Usos
                </Button>
                <Button
                  variant="outlined"
                  color="warning"
                  size="small"
                  onClick={() => setUsesToAssign(-10)}
                >
                  -10 Usos
                </Button>
              </Box>
            </Box>

            <TextField
              fullWidth
              label="Cantidad de usos (positivo para sumar, negativo para restar)"
              type="number"
              value={usesToAssign}
              onChange={(e) => setUsesToAssign(parseInt(e.target.value) || 0)}
              slotProps={{
                htmlInput: { min: -1000, max: 1000 }
              }}
              helperText={
                usesToAssign > 0
                  ? `Se sumarán ${usesToAssign} usos a los que ya tiene el usuario`
                  : usesToAssign < 0
                  ? `Se restarán ${Math.abs(usesToAssign)} usos de los que tiene el usuario`
                  : "Ingresa un número positivo para sumar usos o negativo para restar"
              }
              color={usesToAssign > 0 ? 'success' : usesToAssign < 0 ? 'warning' : 'primary'}
            />

            {/* Advertencia para números negativos */}
            {usesToAssign < 0 && selectedApp && selectedUser && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Atención:</strong> El usuario tiene {getCurrentUsageForApp(selectedUser.user_id, selectedApp)} usos.
                  {getCurrentUsageForApp(selectedUser.user_id, selectedApp) + usesToAssign < 0 && (
                    <span style={{ color: 'red' }}>
                      {' '}Esta operación resultaría en {getCurrentUsageForApp(selectedUser.user_id, selectedApp) + usesToAssign} usos (negativo).
                    </span>
                  )}
                </Typography>
              </Alert>
            )}
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button
            onClick={() => {
              setAssignUsageDialog(false);
              setSelectedUser(null);
              setSelectedApp('');
              setUsesToAssign(10);
              setCurrentUserUsage({});
            }}
            disabled={assigningUsage}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleAssignUsage}
            variant="contained"
            disabled={!selectedApp || usesToAssign === 0 || assigningUsage}
            startIcon={assigningUsage ? <CircularProgress size={20} /> : <AddIcon />}
            color={usesToAssign > 0 ? 'success' : 'warning'}
          >
            {assigningUsage
              ? 'Procesando...'
              : usesToAssign > 0
              ? `Sumar ${usesToAssign} Usos`
              : usesToAssign < 0
              ? `Restar ${Math.abs(usesToAssign)} Usos`
              : 'Asignar Usos'
            }
          </Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo para editar límites */}
      <Dialog open={editLimitDialog} onClose={() => setEditLimitDialog(false)}>
        <DialogTitle>Editar Límite de Aplicación</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2, minWidth: 300 }}>
            <Typography variant="body2" gutterBottom>
              Aplicación: {selectedLimit?.app_name}
            </Typography>
            <Typography variant="caption" color="text.secondary" gutterBottom>
              ID: {selectedLimit?.app_id}
            </Typography>

            <TextField
              fullWidth
              label="Nuevo límite por defecto"
              type="number"
              value={newDefaultLimit}
              onChange={(e) => setNewDefaultLimit(parseInt(e.target.value) || 0)}
              slotProps={{
                htmlInput: { min: 1 }
              }}
              sx={{ mt: 2 }}
              helperText="Este será el límite asignado a nuevos usuarios"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditLimitDialog(false)}>
            Cancelar
          </Button>
          <Button
            onClick={handleUpdateLimit}
            variant="contained"
            disabled={newDefaultLimit <= 0 || updatingLimit}
            startIcon={updatingLimit ? <CircularProgress size={20} /> : <EditIcon />}
          >
            {updatingLimit ? 'Actualizando...' : 'Actualizar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo para historial de usuario */}
      <Dialog
        open={userHistoryDialog}
        onClose={() => setUserHistoryDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <HistoryIcon color="primary" />
            Historial de Uso - {selectedUserForHistory?.user_name}
          </Box>
        </DialogTitle>
        <DialogContent>
          {loadingHistory ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          ) : userHistory.length === 0 ? (
            <Box textAlign="center" p={3}>
              <Typography variant="body1" color="text.secondary">
                No hay historial de uso para este usuario
              </Typography>
            </Box>
          ) : (
            <TableContainer component={Paper} sx={{ mt: 2 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Fecha</TableCell>
                    <TableCell>Aplicación</TableCell>
                    <TableCell>Acción</TableCell>
                    <TableCell>Detalles</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {userHistory.map((record, index) => (
                    <TableRow key={record.id || index}>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(record.created_at).toLocaleString('es-ES')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={record.app_id}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={record.action_type}
                          size="small"
                          color={
                            record.action_type === 'usage_decremented' ? 'error' :
                            record.action_type === 'usage_assigned' ? 'success' : 'default'
                          }
                        />
                      </TableCell>
                      <TableCell>
                        {record.metadata && (
                          <Typography variant="caption" color="text.secondary">
                            {typeof record.metadata === 'object'
                              ? JSON.stringify(record.metadata, null, 2)
                              : record.metadata
                            }
                          </Typography>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUserHistoryDialog(false)}>
            Cerrar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo para añadir psicólogos al sistema de usos */}
      <Dialog
        open={addPsychologistDialog}
        onClose={() => setAddPsychologistDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Añadir Psicólogos al Control de Usos</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            Selecciona los psicólogos que deseas añadir al sistema para poder asignarles usos.
          </Alert>
          <Paper sx={{ maxHeight: 400, overflow: 'auto' }}>
            <List>
              {uninitializedPsychologists.map(psy => (
                <ListItem
                  key={psy.id}
                  secondaryAction={
                    <Checkbox
                      edge="end"
                      onChange={(e) => {
                        setSelectedPsychologistsToAdd(prev =>
                          e.target.checked
                            ? [...prev, psy.id]
                            : prev.filter(id => id !== psy.id)
                        );
                      }}
                      checked={selectedPsychologistsToAdd.includes(psy.id)}
                    />
                  }
                  disablePadding
                >
                  <ListItemButton>
                    <ListItemIcon>
                      <PsychologyIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={psy.full_name || 'Nombre no disponible'}
                      secondary={psy.email}
                    />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </Paper>
          <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
            {selectedPsychologistsToAdd.length} seleccionado(s)
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddPsychologistDialog(false)}>Cancelar</Button>
          <Button
            variant="contained"
            onClick={handleConfirmAddPsychologists}
            disabled={selectedPsychologistsToAdd.length === 0 || isAddingPsychologists}
            startIcon={isAddingPsychologists ? <CircularProgress size={20} /> : <PersonAddIcon />}
          >
            {isAddingPsychologists
              ? 'Añadiendo...'
              : `Añadir ${selectedPsychologistsToAdd.length} Psicólogo(s)`}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo para acciones en lote */}
      <Dialog
        open={bulkActionDialog}
        onClose={() => setBulkActionDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <SelectAllIcon />
            Acciones en Lote - {selectedUsers.length} Usuario(s)
          </Box>
        </DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            Esta acción se aplicará a todos los usuarios seleccionados.
          </Alert>

          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel>Aplicación</InputLabel>
            <Select
              value={bulkSelectedApp}
              onChange={(e) => setBulkSelectedApp(e.target.value)}
              label="Aplicación"
            >
              {appLimits.map((app) => (
                <MenuItem key={app.app_id} value={app.app_id}>
                  {app.app_name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <TextField
            fullWidth
            label="Cantidad de usos (positivo para sumar, negativo para restar)"
            type="number"
            value={bulkUsesToAssign}
            onChange={(e) => setBulkUsesToAssign(parseInt(e.target.value) || 0)}
            slotProps={{
              htmlInput: { min: -1000, max: 1000 }
            }}
            helperText={
              bulkUsesToAssign > 0
                ? `Se sumarán ${bulkUsesToAssign} usos a cada usuario seleccionado`
                : bulkUsesToAssign < 0
                ? `Se restarán ${Math.abs(bulkUsesToAssign)} usos de cada usuario seleccionado`
                : "Ingresa un número positivo para sumar usos o negativo para restar"
            }
            color={bulkUsesToAssign > 0 ? 'success' : bulkUsesToAssign < 0 ? 'warning' : 'primary'}
          />

          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Usuarios seleccionados:
            </Typography>
            <Box sx={{ maxHeight: 150, overflow: 'auto' }}>
              {selectedUsers.map(userId => {
                const user = filteredUsers.find(u => u.user_id === userId);
                return user ? (
                  <Chip
                    key={userId}
                    label={user.user_name}
                    size="small"
                    sx={{ mr: 1, mb: 1 }}
                  />
                ) : null;
              })}
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBulkActionDialog(false)}>Cancelar</Button>
          <Button
            variant="contained"
            onClick={handleBulkAssignUsage}
            disabled={!bulkSelectedApp || bulkUsesToAssign === 0 || isBulkAssigning}
            startIcon={isBulkAssigning ? <CircularProgress size={20} /> : <SelectAllIcon />}
            color={bulkUsesToAssign > 0 ? 'success' : 'warning'}
          >
            {isBulkAssigning
              ? 'Procesando...'
              : bulkUsesToAssign > 0
              ? `Sumar ${bulkUsesToAssign} Usos a ${selectedUsers.length} Usuario(s)`
              : `Restar ${Math.abs(bulkUsesToAssign)} Usos de ${selectedUsers.length} Usuario(s)`
            }
          </Button>
        </DialogActions>
      </Dialog>

      {/* Toast Container para notificaciones */}
      <ToastContainer
        position="top-right"
        autoClose={4000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </Box>
    </ModuleWrapper>
  );
};

export default UsageControlPanel;
