import React from 'react';
import {
  Box,
  Typography,
  Chip,
  Paper,
  Divider,
} from '@mui/material';
import {
  People as PeopleIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';

interface PaginationInfoProps {
  totalUsers: number;
  filteredUsers: number;
  currentPage: number;
  pageSize: number;
  hasActiveFilters: boolean;
  totalPages?: number;
}

export const PaginationInfo: React.FC<PaginationInfoProps> = ({
  totalUsers,
  filteredUsers,
  currentPage,
  pageSize,
  hasActiveFilters,
  totalPages,
}) => {
  const startIndex = currentPage * pageSize + 1;
  const endIndex = Math.min((currentPage + 1) * pageSize, filteredUsers);
  const calculatedTotalPages = totalPages || Math.ceil(filteredUsers / pageSize);
  
  return (
    <Paper 
      elevation={0} 
      sx={{ 
        p: 2, 
        mt: 1, 
        backgroundColor: 'grey.50',
        border: '1px solid',
        borderColor: 'grey.200',
      }}
    >
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: 2,
      }}>
        {/* Información de paginación */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <VisibilityIcon color="primary" fontSize="small" />
            <Typography variant="body2" fontWeight={500}>
              Mostrando {startIndex}-{endIndex} de {filteredUsers}
            </Typography>
          </Box>
          
          {hasActiveFilters && (
            <>
              <Divider orientation="vertical" flexItem />
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <FilterIcon color="action" fontSize="small" />
                <Typography variant="body2" color="text.secondary">
                  Filtrado de {totalUsers} total
                </Typography>
              </Box>
            </>
          )}
        </Box>

        {/* Estadísticas rápidas */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Chip
            icon={<PeopleIcon />}
            label={`${totalUsers} usuarios`}
            size="small"
            variant="outlined"
            color="primary"
          />

          {hasActiveFilters && (
            <Chip
              icon={<FilterIcon />}
              label={`${filteredUsers} filtrados`}
              size="small"
              variant="filled"
              color="secondary"
            />
          )}

          {calculatedTotalPages > 1 && (
            <Chip
              label={`Página ${currentPage + 1} de ${calculatedTotalPages}`}
              size="small"
              variant="outlined"
              color="default"
            />
          )}
        </Box>
      </Box>

      {/* Información adicional cuando hay filtros activos */}
      {hasActiveFilters && filteredUsers === 0 && (
        <Box sx={{ mt: 1, pt: 1, borderTop: '1px solid', borderColor: 'grey.300' }}>
          <Typography variant="body2" color="warning.main" fontWeight={500}>
            ⚠️ No se encontraron usuarios que coincidan con los filtros aplicados
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Intenta ajustar los criterios de búsqueda o limpiar los filtros
          </Typography>
        </Box>
      )}
    </Paper>
  );
};
