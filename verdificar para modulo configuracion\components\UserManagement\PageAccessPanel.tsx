import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Card,
  CardContent,
  FormControlLabel,
  Divider,
  Button,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Info as InfoIcon,
  AdminPanelSettings as AdminIcon,
  Psychology as PsychologyIcon,
  Person as PersonIcon,
  PersonAdd as PersonAddIcon,
  Group as GroupIcon,
} from '@mui/icons-material';
import {
  routePermissionsService,
  type RoutePermission,
  type UserRole,
  permissionUtils,
} from '../../services/routePermissions';
import { useNotifier } from '../../hooks/useNotifier';
import UserSelector from './UserSelector';
import { ModuleWrapper } from '../Admin/ModuleFallback';
import { RouteSearchAndFilters, useRouteFilters } from './RouteSearchAndFilters';

const PageAccessPanel: React.FC = () => {
  const [permissions, setPermissions] = useState<RoutePermission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState<number | null>(null);
  const [userSelectorOpen, setUserSelectorOpen] = useState(false);
  const [selectedPermission, setSelectedPermission] = useState<RoutePermission | null>(null);
  const { showSuccess, showError } = useNotifier();

  // Hook para filtros y búsqueda
  const {
    searchTerm,
    setSearchTerm,
    roleFilter,
    setRoleFilter,
    statusFilter,
    setStatusFilter,
    categoryFilter,
    setCategoryFilter,
    filteredPermissions,
    clearFilters,
  } = useRouteFilters(permissions);

  const roles: UserRole[] = ['administrador', 'psicologo', 'paciente'];

  // Iconos para cada rol
  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'administrador':
        return <AdminIcon fontSize="small" />;
      case 'psicologo':
        return <PsychologyIcon fontSize="small" />;
      case 'paciente':
        return <PersonIcon fontSize="small" />;
      default:
        return <PersonIcon fontSize="small" />;
    }
  };

  // Cargar permisos al montar el componente
  useEffect(() => {
    loadPermissions();
  }, []);

  const loadPermissions = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('🔍 Cargando permisos de rutas...');
      const { data, error: serviceError } =
        await routePermissionsService.getAllRoutePermissions();

      if (serviceError) {
        console.error('❌ Error del servicio:', serviceError);
        setError(serviceError);
        showError(`Error al cargar permisos: ${serviceError}`);
        return;
      }

      console.log('✅ Permisos cargados:', data?.length || 0, 'rutas');
      setPermissions(data || []);
    } catch (err) {
      console.error('❌ Excepción en loadPermissions:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error inesperado al cargar permisos';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleRole = async (
    permissionId: number,
    role: UserRole,
    isChecked: boolean,
  ) => {
    setUpdating(permissionId);

    try {
      const permission = permissions.find((p) => p.id === permissionId);
      if (!permission) return;

      let newRoles = [...permission.allowed_roles];

      if (isChecked) {
        // Agregar rol si no está presente
        if (!newRoles.includes(role)) {
          newRoles.push(role);
        }
      } else {
        // Remover rol
        newRoles = newRoles.filter((r) => r !== role);
      }

      // Asegurar que administrador siempre tenga acceso a rutas administrativas
      newRoles = permissionUtils.ensureAdminAccess(
        permission.route_path,
        newRoles,
      );

      const { success, error } =
        await routePermissionsService.updateRoutePermission(
          permissionId,
          newRoles,
          permission.is_enabled, // <-- AÑADIDO: Pasar siempre el estado actual
        );

      if (error) {
        showError(`Error al actualizar permisos: ${error}`);
        return;
      }

      if (success) {
        // Actualizar estado local
        setPermissions((prev) =>
          prev.map((p) =>
            p.id === permissionId
              ? {
                  ...p,
                  allowed_roles: newRoles,
                  updated_at: new Date().toISOString(),
                }
              : p,
          ),
        );

        showSuccess(`Permisos actualizados para ${permission.route_name}`);
      }
    } catch (error) {
      showError('Error inesperado al actualizar permisos');
      console.error('Error updating permission:', error);
    } finally {
      setUpdating(null);
    }
  };

  const handleToggleEnabled = async (
    permissionId: number,
    isEnabled: boolean,
  ) => {
    setUpdating(permissionId);

    try {
      const permission = permissions.find((p) => p.id === permissionId);
      if (!permission) return;

      const { success, error } =
        await routePermissionsService.updateRoutePermission(
          permissionId,
          permission.allowed_roles,
          isEnabled,
        );

      if (error) {
        showError(`Error al actualizar estado: ${error}`);
        return;
      }

      if (success) {
        // Actualizar estado local
        setPermissions((prev) =>
          prev.map((p) =>
            p.id === permissionId
              ? {
                  ...p,
                  is_enabled: isEnabled,
                  updated_at: new Date().toISOString(),
                }
              : p,
          ),
        );

        showSuccess(
          `${permission.route_name} ${isEnabled ? 'habilitada' : 'deshabilitada'}`,
        );
      }
    } catch (error) {
      showError('Error inesperado al actualizar estado');
      console.error('Error updating enabled state:', error);
    } finally {
      setUpdating(null);
    }
  };

  const handleOpenUserSelector = (permission: RoutePermission) => {
    setSelectedPermission(permission);
    setUserSelectorOpen(true);
  };

  const handleCloseUserSelector = () => {
    setUserSelectorOpen(false);
    setSelectedPermission(null);
  };

  const handleSaveAllowedUsers = async (userIds: string[]) => {
    if (!selectedPermission) return;

    setUpdating(selectedPermission.id);

    try {
      const { success, error } = await routePermissionsService.updateRouteAllowedUsers(
        selectedPermission.id,
        userIds
      );

      if (error) {
        showError(`Error al actualizar usuarios permitidos: ${error}`);
        return;
      }

      if (success) {
        // Actualizar estado local
        setPermissions(prev =>
          prev.map(p =>
            p.id === selectedPermission.id
              ? {
                  ...p,
                  allowed_users: userIds,
                  updated_at: new Date().toISOString(),
                }
              : p
          )
        );

        showSuccess(`Usuarios específicos actualizados para ${selectedPermission.route_name}`);
      }
    } catch (error) {
      showError('Error inesperado al actualizar usuarios permitidos');
      console.error('Error updating allowed users:', error);
    } finally {
      setUpdating(null);
    }
  };

  return (
    <ModuleWrapper
      loading={loading}
      error={error}
      onRetry={loadPermissions}
    >
    <Box>
      {/* Header */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" mb={2}>
            <SecurityIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="h5" component="h2">
              Control de Acceso a Páginas
            </Typography>
          </Box>
          <Typography variant="body2" color="text.secondary">
            Gestiona qué roles pueden acceder a cada página del sistema. Los
            cambios se aplican inmediatamente.
          </Typography>
        </CardContent>
      </Card>

      {/* Búsqueda y Filtros */}
      <RouteSearchAndFilters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        roleFilter={roleFilter}
        onRoleFilterChange={setRoleFilter}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
        categoryFilter={categoryFilter}
        onCategoryFilterChange={setCategoryFilter}
        permissions={permissions}
        onClearFilters={clearFilters}
      />

      {/* Tabla de permisos */}
      <TableContainer component={Paper} elevation={2}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'primary.main' }}>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>
                Página
              </TableCell>
              <TableCell
                align="center"
                sx={{ color: 'white', fontWeight: 'bold' }}
              >
                Estado
              </TableCell>
              {roles.map((role) => (
                <TableCell
                  key={role}
                  align="center"
                  sx={{ color: 'white', fontWeight: 'bold' }}
                >
                  <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    {getRoleIcon(role)}
                    <Typography variant="body2" sx={{ ml: 1 }}>
                      {permissionUtils.formatRole(role)}
                    </Typography>
                  </Box>
                </TableCell>
              ))}
              <TableCell
                align="center"
                sx={{ color: 'white', fontWeight: 'bold' }}
              >
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <GroupIcon sx={{ mr: 1 }} />
                  <Typography variant="body2">
                    Usuarios Específicos
                  </Typography>
                </Box>
              </TableCell>
              <TableCell
                align="center"
                sx={{ color: 'white', fontWeight: 'bold' }}
              >
                Info
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredPermissions.map((permission) => (
              <TableRow
                key={permission.id}
                sx={{
                  '&:hover': { backgroundColor: 'action.hover' },
                  opacity: permission.is_enabled ? 1 : 0.6,
                }}
              >
                {/* Información de la página */}
                <TableCell>
                  <Box>
                    <Typography variant="body1" fontWeight="medium">
                      {permission.route_name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {permission.route_path}
                    </Typography>
                    {permission.route_description && (
                      <Typography
                        variant="caption"
                        display="block"
                        color="text.secondary"
                      >
                        {permission.route_description}
                      </Typography>
                    )}
                  </Box>
                </TableCell>

                {/* Estado habilitado/deshabilitado */}
                <TableCell align="center">
                  <FormControlLabel
                    control={
                      <Switch
                        checked={permission.is_enabled}
                        onChange={(e) =>
                          handleToggleEnabled(permission.id, e.target.checked)
                        }
                        disabled={updating === permission.id}
                        color="success"
                      />
                    }
                    label={permission.is_enabled ? 'Activa' : 'Inactiva'}
                    labelPlacement="bottom"
                  />
                </TableCell>

                {/* Switches para cada rol */}
                {roles.map((role) => (
                  <TableCell key={role} align="center">
                    <Switch
                      checked={permission.allowed_roles.includes(role)}
                      onChange={(e) =>
                        handleToggleRole(permission.id, role, e.target.checked)
                      }
                      disabled={
                        updating === permission.id ||
                        !permission.is_enabled ||
                        (role === 'administrador' &&
                          ['/administracion', '/configuracion'].includes(
                            permission.route_path,
                          ))
                      }
                      color="primary"
                      size="medium"
                    />
                    {updating === permission.id && (
                      <CircularProgress size={16} sx={{ ml: 1 }} />
                    )}
                  </TableCell>
                ))}

                {/* Usuarios específicos */}
                <TableCell align="center">
                  <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<PersonAddIcon />}
                      onClick={() => handleOpenUserSelector(permission)}
                      disabled={updating === permission.id || !permission.is_enabled}
                      sx={{ minWidth: '140px' }}
                    >
                      {permission.allowed_users?.length || 0} usuario(s)
                    </Button>

                    {permission.allowed_users && permission.allowed_users.length > 0 && (
                      <Box display="flex" flexWrap="wrap" gap={0.5} justifyContent="center">
                        {permission.allowed_users.slice(0, 3).map((userId, index) => (
                          <Chip
                            key={userId}
                            label={`U${index + 1}`}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        ))}
                        {permission.allowed_users.length > 3 && (
                          <Chip
                            label={`+${permission.allowed_users.length - 3}`}
                            size="small"
                            color="default"
                            variant="outlined"
                          />
                        )}
                      </Box>
                    )}
                  </Box>
                </TableCell>

                {/* Información adicional */}
                <TableCell align="center">
                  <Tooltip
                    title={`Última actualización: ${new Date(permission.updated_at).toLocaleString()}`}
                  >
                    <IconButton size="small">
                      <InfoIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Alertas informativas */}
      <Box mt={3} sx={{ '& > *': { mb: 2 } }}>
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>Nota importante:</strong> El rol de 'Administrador' siempre
            tiene acceso a las páginas de administración y configuración. Estos
            permisos no pueden ser modificados por seguridad.
          </Typography>
        </Alert>

        <Alert severity="warning">
          <Typography variant="body2">
            <strong>Precaución:</strong> Deshabilitar páginas puede afectar la
            funcionalidad del sistema. Los cambios se aplican inmediatamente a
            todos los usuarios.
          </Typography>
        </Alert>
      </Box>

      {/* Estadísticas rápidas */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Resumen de Permisos
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <Box display="flex" gap={2} flexWrap="wrap">
            <Chip
              label={`${permissions.filter((p) => p.is_enabled).length} páginas activas`}
              color="success"
              variant="outlined"
            />
            <Chip
              label={`${permissions.filter((p) => !p.is_enabled).length} páginas inactivas`}
              color="error"
              variant="outlined"
            />
            <Chip
              label={`${permissions.filter((p) => p.allowed_roles.includes('administrador')).length} acceso admin`}
              color="primary"
              variant="outlined"
            />
            <Chip
              label={`${permissions.filter((p) => p.allowed_roles.includes('psicologo')).length} acceso psicólogo`}
              color="info"
              variant="outlined"
            />
            <Chip
              label={`${permissions.filter((p) => p.allowed_roles.includes('paciente')).length} acceso paciente`}
              color="secondary"
              variant="outlined"
            />
          </Box>
        </CardContent>
      </Card>

      {/* Selector de usuarios específicos */}
      {selectedPermission && (
        <UserSelector
          open={userSelectorOpen}
          onClose={handleCloseUserSelector}
          onSave={handleSaveAllowedUsers}
          currentUserIds={selectedPermission.allowed_users || []}
          routeName={selectedPermission.route_name}
        />
      )}
    </Box>
    </ModuleWrapper>
  );
};

export default PageAccessPanel;
