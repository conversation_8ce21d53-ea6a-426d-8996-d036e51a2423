import React, { useState, useEffect } from 'react';
import { Grid, Paper, Typography, Box, Alert } from '@mui/material';
import {
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  PersonAdd as PersonAddIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { StatCard } from './StatCard';
import { UserActivityChart } from './UserActivityChart';
import { ActivityLogTable } from './ActivityLogTable';
import { supabase } from '../../config/supabase';

interface DashboardStats {
  totalUsers: number;
  activeToday: number;
  newUsersWeek: number;
  completedTests: number;
}

interface ActivityData {
  date: string;
  newUsers: number;
  activeUsers: number;
  completedTests: number;
}

interface ActivityLog {
  id: string;
  type:
    | 'user_created'
    | 'user_updated'
    | 'test_completed'
    | 'user_login'
    | 'system_config';
  description: string;
  user: string;
  timestamp: string;
  details?: string;
}

export const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    activeToday: 0,
    newUsersWeek: 0,
    completedTests: 0,
  });
  const [chartData, setChartData] = useState<ActivityData[]>([]);
  const [recentLogs, setRecentLogs] = useState<ActivityLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Cargar estadísticas básicas
      await loadStats();

      // Cargar datos del gráfico (simulados por ahora)
      loadChartData();

      // Cargar logs de actividad (simulados por ahora)
      loadActivityLogs();
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setError('Error al cargar los datos del dashboard');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      // Obtener total de usuarios
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      // Obtener usuarios creados en la última semana
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);

      const { count: newUsersWeek } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', weekAgo.toISOString());

      // Por ahora, simular otros datos
      setStats({
        totalUsers: totalUsers || 0,
        activeToday: Math.floor((totalUsers || 0) * 0.3), // 30% activos hoy
        newUsersWeek: newUsersWeek || 0,
        completedTests: Math.floor((totalUsers || 0) * 1.5), // 1.5 tests por usuario promedio
      });
    } catch (error) {
      console.error('Error loading stats:', error);
      // Usar datos simulados en caso de error
      setStats({
        totalUsers: 25,
        activeToday: 8,
        newUsersWeek: 5,
        completedTests: 38,
      });
    }
  };

  const loadChartData = () => {
    // Generar datos simulados para los últimos 7 días
    const data: ActivityData[] = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      data.push({
        date: date.toLocaleDateString('es-ES', {
          month: 'short',
          day: 'numeric',
        }),
        newUsers: Math.floor(Math.random() * 5) + 1,
        activeUsers: Math.floor(Math.random() * 15) + 5,
        completedTests: Math.floor(Math.random() * 8) + 2,
      });
    }
    setChartData(data);
  };

  const loadActivityLogs = () => {
    // Generar logs simulados
    const logs: ActivityLog[] = [
      {
        id: '1',
        type: 'user_created',
        description: 'Nuevo usuario registrado',
        user: '<EMAIL>',
        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 min ago
        details: 'Rol: Psicólogo',
      },
      {
        id: '2',
        type: 'test_completed',
        description: 'Test MACI-II completado',
        user: '<EMAIL>',
        timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45 min ago
      },
      {
        id: '3',
        type: 'user_updated',
        description: 'Rol de usuario actualizado',
        user: '<EMAIL>',
        timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago
        details: 'Cambio: Paciente → Psicólogo',
      },
      {
        id: '4',
        type: 'user_login',
        description: 'Inicio de sesión',
        user: '<EMAIL>',
        timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(), // 3 hours ago
      },
      {
        id: '5',
        type: 'system_config',
        description: 'Configuración actualizada',
        user: '<EMAIL>',
        timestamp: new Date(Date.now() - 1000 * 60 * 300).toISOString(), // 5 hours ago
        details: 'Permisos de rutas',
      },
    ];
    setRecentLogs(logs);
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Box sx={{ p: 3 }}>
        <Typography
          variant="h4"
          sx={{ mb: 3, fontWeight: 700, color: '#011129', textAlign: 'center' }}
        >
          Dashboard de Administración
        </Typography>

        <Grid container spacing={3}>
          {/* Tarjetas de Métricas */}
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Usuarios Totales"
              value={stats.totalUsers}
              icon={<PeopleIcon />}
              color="primary"
              loading={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Activos Hoy"
              value={stats.activeToday}
              icon={<TrendingUpIcon />}
              color="success"
              change={{ value: 12, type: 'increase', period: 'vs ayer' }}
              loading={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Nuevos (7 días)"
              value={stats.newUsersWeek}
              icon={<PersonAddIcon />}
              color="info"
              change={{
                value: 8,
                type: 'increase',
                period: 'vs sem. anterior',
              }}
              loading={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Tests Completados"
              value={stats.completedTests}
              icon={<AssessmentIcon />}
              color="warning"
              change={{ value: 5, type: 'increase', period: 'este mes' }}
              loading={loading}
            />
          </Grid>

          {/* Gráfico Principal */}
          <Grid item xs={12} lg={8}>
            <Paper sx={{ p: 3, height: 350 }}>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, textAlign: 'center' }}>
                Actividad de Usuarios (Últimos 7 días)
              </Typography>
              <UserActivityChart data={chartData} loading={loading} />
            </Paper>
          </Grid>

          {/* Actividad Reciente */}
          <Grid item xs={12} lg={4}>
            <Paper sx={{ p: 3, height: 350, overflow: 'hidden' }}>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, textAlign: 'center' }}>
                Actividad Reciente
              </Typography>
              <Box sx={{ height: 'calc(100% - 60px)', overflow: 'auto' }}>
                <ActivityLogTable logs={recentLogs} loading={loading} />
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </motion.div>
  );
};
