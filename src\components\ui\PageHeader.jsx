import React from 'react';

/**
 * Componente de encabezado de página estandarizado para BAT-7
 * Aplica el estilo consistente con fondo azul oscuro #121940
 */
const PageHeader = ({ 
  title, 
  subtitle, 
  icon: IconComponent, 
  className = "",
  showTransitions = true 
}) => {
  return (
    <div className={`bg-[#121940] text-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <div className={`flex items-center justify-center mb-4 ${
            showTransitions ? 'transition-all duration-300 ease-in-out hover:scale-105' : ''
          }`}>
            {IconComponent && (
              <div className={`w-12 h-12 bg-[#f59e0b] rounded-full flex items-center justify-center mr-4 ${
                showTransitions ? 'transition-all duration-300 ease-in-out hover:bg-yellow-400 hover:shadow-lg' : ''
              }`}>
                <IconComponent className="text-white text-xl" />
              </div>
            )}
            <h1 className={`text-3xl font-bold ${
              showTransitions ? 'transition-all duration-300 ease-in-out hover:text-yellow-200' : ''
            }`}>
              {title}
            </h1>
          </div>
          {subtitle && (
            <p className={`text-blue-100 text-lg ${
              showTransitions ? 'transition-all duration-300 ease-in-out hover:text-white' : ''
            }`}>
              {subtitle}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default PageHeader;
