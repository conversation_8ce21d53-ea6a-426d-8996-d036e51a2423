import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabaseClient';

interface DiagnosticResult {
  isConnected: boolean;
  latency: number | null;
  error: string | null;
  lastCheck: Date | null;
  authStatus: 'authenticated' | 'unauthenticated' | 'checking' | 'error';
  dbStatus: 'connected' | 'disconnected' | 'checking' | 'error';
}

export const useSupabaseDiagnostic = () => {
  const [diagnostic, setDiagnostic] = useState<DiagnosticResult>({
    isConnected: false,
    latency: null,
    error: null,
    lastCheck: null,
    authStatus: 'checking',
    dbStatus: 'checking',
  });

  const [isRunning, setIsRunning] = useState(false);

  const runDiagnostic = async (): Promise<DiagnosticResult> => {
    setIsRunning(true);
    const startTime = Date.now();
    
    const result: DiagnosticResult = {
      isConnected: false,
      latency: null,
      error: null,
      lastCheck: new Date(),
      authStatus: 'checking',
      dbStatus: 'checking',
    };

    try {
      // 1. Verificar conexión de autenticación
      console.log('🔍 Verificando estado de autenticación...');
      const { data: authData, error: authError } = await supabase.auth.getSession();
      
      if (authError) {
        result.authStatus = 'error';
        result.error = `Auth Error: ${authError.message}`;
      } else if (authData.session) {
        result.authStatus = 'authenticated';
      } else {
        result.authStatus = 'unauthenticated';
      }

      // 2. Verificar conexión a la base de datos
      console.log('🔍 Verificando conexión a la base de datos...');
      const { data: dbData, error: dbError } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);

      if (dbError) {
        result.dbStatus = 'error';
        result.error = result.error ? `${result.error} | DB Error: ${dbError.message}` : `DB Error: ${dbError.message}`;
      } else {
        result.dbStatus = 'connected';
      }

      // 3. Calcular latencia
      const endTime = Date.now();
      result.latency = endTime - startTime;

      // 4. Determinar estado general
      result.isConnected = result.authStatus !== 'error' && result.dbStatus === 'connected';

      console.log('✅ Diagnóstico completado:', result);

    } catch (error) {
      console.error('❌ Error en diagnóstico:', error);
      result.error = error instanceof Error ? error.message : 'Error desconocido';
      result.authStatus = 'error';
      result.dbStatus = 'error';
    }

    setDiagnostic(result);
    setIsRunning(false);
    return result;
  };

  // Ejecutar diagnóstico inicial
  useEffect(() => {
    runDiagnostic();
  }, []);

  // Diagnóstico automático cada 30 segundos si hay errores
  useEffect(() => {
    if (!diagnostic.isConnected && diagnostic.lastCheck) {
      const interval = setInterval(() => {
        console.log('🔄 Ejecutando diagnóstico automático...');
        runDiagnostic();
      }, 30000);

      return () => clearInterval(interval);
    }
  }, [diagnostic.isConnected, diagnostic.lastCheck]);

  const getStatusColor = () => {
    if (diagnostic.isConnected) return 'success';
    if (diagnostic.error) return 'error';
    return 'warning';
  };

  const getStatusText = () => {
    if (isRunning) return 'Verificando conexión...';
    if (diagnostic.isConnected) return 'Conectado';
    if (diagnostic.error) return 'Error de conexión';
    return 'Desconectado';
  };

  const getDetailedStatus = () => {
    return {
      auth: {
        status: diagnostic.authStatus,
        text: diagnostic.authStatus === 'authenticated' ? 'Autenticado' :
              diagnostic.authStatus === 'unauthenticated' ? 'No autenticado' :
              diagnostic.authStatus === 'checking' ? 'Verificando...' : 'Error',
      },
      database: {
        status: diagnostic.dbStatus,
        text: diagnostic.dbStatus === 'connected' ? 'Conectado' :
              diagnostic.dbStatus === 'disconnected' ? 'Desconectado' :
              diagnostic.dbStatus === 'checking' ? 'Verificando...' : 'Error',
      },
      latency: diagnostic.latency ? `${diagnostic.latency}ms` : 'N/A',
      lastCheck: diagnostic.lastCheck?.toLocaleTimeString() || 'Nunca',
    };
  };

  return {
    diagnostic,
    isRunning,
    runDiagnostic,
    getStatusColor,
    getStatusText,
    getDetailedStatus,
  };
};
