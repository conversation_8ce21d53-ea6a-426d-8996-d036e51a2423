import React from 'react';
import {
  <PERSON>,
  Chip,
  Typography,
  Icon<PERSON>utton,
  Tooltip,
} from '@mui/material';
import {
  ArrowUpward as ArrowUpIcon,
  ArrowDownward as ArrowDownIcon,
  Clear as ClearIcon,
  Sort as SortIcon,
} from '@mui/icons-material';
import { GridSortModel } from '@mui/x-data-grid';

interface SortingInfoProps {
  sortModel: GridSortModel;
  onClearSort: () => void;
}

const fieldLabels: Record<string, string> = {
  full_name: 'Nombre',
  email: 'Email',
  role: 'Rol',
  is_active: 'Estado',
  created_at: '<PERSON><PERSON> de Registro',
};

export const SortingInfo: React.FC<SortingInfoProps> = ({
  sortModel,
  onClearSort,
}) => {
  if (!sortModel || sortModel.length === 0) {
    return (
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 1,
        py: 1,
        px: 2,
        backgroundColor: 'grey.50',
        borderRadius: 1,
        border: '1px solid',
        borderColor: 'grey.200',
      }}>
        <SortIcon color="action" fontSize="small" />
        <Typography variant="body2" color="text.secondary">
          Sin ordenamiento aplicado
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: 1,
      py: 1,
      px: 2,
      backgroundColor: 'primary.50',
      borderRadius: 1,
      border: '1px solid',
      borderColor: 'primary.200',
    }}>
      <SortIcon color="primary" fontSize="small" />
      <Typography variant="body2" color="primary.main" fontWeight={500}>
        Ordenado por:
      </Typography>
      
      {sortModel.map((sort, index) => (
        <Chip
          key={`${sort.field}-${index}`}
          icon={sort.sort === 'asc' ? <ArrowUpIcon /> : <ArrowDownIcon />}
          label={`${fieldLabels[sort.field] || sort.field} ${sort.sort === 'asc' ? '↑' : '↓'}`}
          size="small"
          color="primary"
          variant="filled"
          sx={{ 
            fontWeight: 600,
            '& .MuiChip-icon': {
              fontSize: '0.875rem',
            },
          }}
        />
      ))}

      <Tooltip title="Limpiar ordenamiento">
        <IconButton
          size="small"
          onClick={onClearSort}
          sx={{ 
            ml: 1,
            color: 'primary.main',
            '&:hover': {
              backgroundColor: 'primary.100',
            },
          }}
        >
          <ClearIcon fontSize="small" />
        </IconButton>
      </Tooltip>
    </Box>
  );
};
