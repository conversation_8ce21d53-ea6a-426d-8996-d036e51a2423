import React from 'react';
import { Chip, ChipProps } from '@mui/material';
import { styled } from '@mui/material/styles';

// Tipos de estado según la guía
export type StatusType = 
  | 'asignada' 
  | 'en_progreso' 
  | 'completada' 
  | 'revisada' 
  | 'caducada'
  | 'activo'
  | 'inactivo'
  | 'pendiente'
  | 'aprobado'
  | 'rechazado';

interface StatusBadgeProps extends Omit<ChipProps, 'color'> {
  status: StatusType;
  variant?: 'filled' | 'outlined' | 'soft';
}

// Configuración de colores según la guía de diseño
const statusConfig = {
  asignada: {
    color: '#DDA15E', // Ocre
    backgroundColor: 'rgba(221, 161, 94, 0.1)',
    borderColor: 'rgba(221, 161, 94, 0.3)',
    label: 'Asignada'
  },
  en_progreso: {
    color: '#5A92C8', // Azul sereno
    backgroundColor: 'rgba(90, 146, 200, 0.1)',
    borderColor: 'rgba(90, 146, 200, 0.3)',
    label: 'En Progreso'
  },
  completada: {
    color: '#63B4A9', // Verde menta
    backgroundColor: 'rgba(99, 180, 169, 0.1)',
    borderColor: 'rgba(99, 180, 169, 0.3)',
    label: 'Completada'
  },
  revisada: {
    color: '#52A398', // Verde menta oscuro
    backgroundColor: 'rgba(82, 163, 152, 0.1)',
    borderColor: 'rgba(82, 163, 152, 0.3)',
    label: 'Revisada'
  },
  caducada: {
    color: '#F28A7C', // Coral suave
    backgroundColor: 'rgba(242, 138, 124, 0.1)',
    borderColor: 'rgba(242, 138, 124, 0.3)',
    label: 'Caducada'
  },
  activo: {
    color: '#63B4A9',
    backgroundColor: 'rgba(99, 180, 169, 0.1)',
    borderColor: 'rgba(99, 180, 169, 0.3)',
    label: 'Activo'
  },
  inactivo: {
    color: '#6C757D',
    backgroundColor: 'rgba(108, 117, 125, 0.1)',
    borderColor: 'rgba(108, 117, 125, 0.3)',
    label: 'Inactivo'
  },
  pendiente: {
    color: '#DDA15E',
    backgroundColor: 'rgba(221, 161, 94, 0.1)',
    borderColor: 'rgba(221, 161, 94, 0.3)',
    label: 'Pendiente'
  },
  aprobado: {
    color: '#63B4A9',
    backgroundColor: 'rgba(99, 180, 169, 0.1)',
    borderColor: 'rgba(99, 180, 169, 0.3)',
    label: 'Aprobado'
  },
  rechazado: {
    color: '#F28A7C',
    backgroundColor: 'rgba(242, 138, 124, 0.1)',
    borderColor: 'rgba(242, 138, 124, 0.3)',
    label: 'Rechazado'
  }
};

const StyledChip = styled(Chip)<{ statustype: StatusType; chipvariant: string }>(
  ({ theme, statustype, chipvariant }) => {
    const config = statusConfig[statustype];
    
    const baseStyles = {
      fontWeight: 500,
      fontSize: '0.75rem',
      height: 24,
      borderRadius: 12,
      '& .MuiChip-label': {
        padding: '0 8px',
      },
    };

    switch (chipvariant) {
      case 'filled':
        return {
          ...baseStyles,
          backgroundColor: config.color,
          color: '#ffffff',
          border: 'none',
        };
      case 'outlined':
        return {
          ...baseStyles,
          backgroundColor: 'transparent',
          color: config.color,
          border: `1px solid ${config.borderColor}`,
        };
      case 'soft':
      default:
        return {
          ...baseStyles,
          backgroundColor: config.backgroundColor,
          color: config.color,
          border: 'none',
        };
    }
  }
);

export const StatusBadge: React.FC<StatusBadgeProps> = ({ 
  status, 
  variant = 'soft',
  ...props 
}) => {
  const config = statusConfig[status];
  
  return (
    <StyledChip
      label={config.label}
      statustype={status}
      chipvariant={variant}
      size="small"
      {...props}
    />
  );
};

export default StatusBadge;
