import { useState, useCallback, useRef } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  loading: boolean;
}

interface UseCacheOptions {
  ttl?: number; // Time to live in milliseconds (default: 5 minutes)
  staleWhileRevalidate?: boolean; // Return stale data while fetching new data
}

/**
 * Hook para cache de datos de módulos con optimizaciones de rendimiento
 * Evita recargas innecesarias al cambiar entre módulos
 */
export function useModuleCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: UseCacheOptions = {}
) {
  const { ttl = 5 * 60 * 1000, staleWhileRevalidate = true } = options;
  
  // Cache global usando useRef para persistir entre renders
  const cache = useRef<Map<string, CacheEntry<T>>>(new Map());
  
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isStale = useCallback((entry: CacheEntry<T>): boolean => {
    return Date.now() - entry.timestamp > ttl;
  }, [ttl]);

  const fetchData = useCallback(async (forceRefresh = false) => {
    const cacheEntry = cache.current.get(key);
    
    // Si hay datos en cache y no están obsoletos, usarlos
    if (cacheEntry && !isStale(cacheEntry) && !forceRefresh) {
      setData(cacheEntry.data);
      setLoading(false);
      setError(null);
      return cacheEntry.data;
    }

    // Si hay datos obsoletos pero staleWhileRevalidate está habilitado
    if (cacheEntry && isStale(cacheEntry) && staleWhileRevalidate && !forceRefresh) {
      setData(cacheEntry.data); // Mostrar datos obsoletos inmediatamente
      setLoading(true); // Pero indicar que se está cargando
    } else {
      setLoading(true);
    }

    setError(null);

    try {
      const newData = await fetcher();
      
      // Actualizar cache
      cache.current.set(key, {
        data: newData,
        timestamp: Date.now(),
        loading: false,
      });

      setData(newData);
      setLoading(false);
      return newData;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      setLoading(false);
      
      // Si hay datos en cache, mantenerlos en caso de error
      if (cacheEntry) {
        setData(cacheEntry.data);
      }
      
      throw err;
    }
  }, [key, fetcher, isStale, staleWhileRevalidate]);

  const invalidateCache = useCallback(() => {
    cache.current.delete(key);
  }, [key]);

  const clearAllCache = useCallback(() => {
    cache.current.clear();
  }, []);

  const getCacheInfo = useCallback(() => {
    const entry = cache.current.get(key);
    if (!entry) return null;

    return {
      hasData: !!entry.data,
      isStale: isStale(entry),
      age: Date.now() - entry.timestamp,
      timestamp: entry.timestamp,
    };
  }, [key, isStale]);

  return {
    data,
    loading,
    error,
    fetchData,
    invalidateCache,
    clearAllCache,
    getCacheInfo,
    // Helpers
    isDataAvailable: !!data,
    isDataStale: data ? isStale(cache.current.get(key) || { data, timestamp: 0, loading: false }) : false,
  };
}

/**
 * Hook específico para cache de datos de usuario
 */
export function useUserDataCache() {
  return useModuleCache('userData', async () => {
    // Esta función será reemplazada por el fetcher real
    return null;
  }, {
    ttl: 10 * 60 * 1000, // 10 minutos para datos de usuario
    staleWhileRevalidate: true,
  });
}

/**
 * Hook específico para cache de estadísticas
 */
export function useStatsCache() {
  return useModuleCache('stats', async () => {
    return null;
  }, {
    ttl: 2 * 60 * 1000, // 2 minutos para estadísticas (más frecuente)
    staleWhileRevalidate: true,
  });
}

export default useModuleCache;
