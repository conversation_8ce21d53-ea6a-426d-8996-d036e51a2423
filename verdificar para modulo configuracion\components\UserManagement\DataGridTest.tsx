import React, { useState } from 'react';
import { DataGrid, GridColDef, GridPaginationModel } from '@mui/x-data-grid';
import { Box, Typography, Paper } from '@mui/material';

// Datos de prueba simples
const testRows = [
  { id: '1', name: '<PERSON>', email: '<EMAIL>', role: 'administrador' },
  { id: '2', name: '<PERSON>', email: '<EMAIL>', role: 'psicologo' },
  { id: '3', name: '<PERSON>', email: '<EMAIL>', role: 'paciente' },
  { id: '4', name: '<PERSON>', email: '<EMAIL>', role: 'psicologo' },
  { id: '5', name: '<PERSON>', email: '<EMAIL>', role: 'paciente' },
];

const testColumns: GridColDef[] = [
  { field: 'id', headerName: 'ID', width: 70 },
  { field: 'name', headerName: 'Nombre', width: 200 },
  { field: 'email', headerName: 'Email', width: 250 },
  { field: 'role', headerName: 'Rol', width: 150 },
];

export const DataGridTest: React.FC = () => {
  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
    page: 0,
    pageSize: 5,
  });

  const [selectedRows, setSelectedRows] = useState<string[]>([]);

  console.log('🧪 DataGridTest render:', {
    rowsLength: testRows.length,
    paginationModel,
    selectedRows,
  });

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        🧪 Prueba de DataGrid
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Esta es una prueba simple del DataGrid para verificar que funciona correctamente.
      </Typography>

      <Paper sx={{ height: 400, width: '100%' }}>
        <DataGrid
          rows={testRows}
          columns={testColumns}
          rowCount={testRows.length}
          paginationModel={paginationModel}
          onPaginationModelChange={(newModel) => {
            console.log('📄 Test pagination changed:', newModel);
            setPaginationModel(newModel);
          }}
          pageSizeOptions={[5, 10, 25]}
          paginationMode="client"
          checkboxSelection
          disableRowSelectionOnClick
          rowSelectionModel={selectedRows}
          onRowSelectionModelChange={(newSelection) => {
            console.log('✅ Test selection changed:', newSelection);
            const selectionArray = Array.isArray(newSelection) ? newSelection : [];
            setSelectedRows(selectionArray as string[]);
          }}
          sx={{
            border: 'none',
            '& .MuiDataGrid-cell': {
              borderBottom: '1px solid #f0f0f0',
            },
            '& .MuiDataGrid-columnHeaders': {
              backgroundColor: '#f8fafc',
              borderBottom: '2px solid #e2e8f0',
            },
            '& .MuiDataGrid-row:hover': {
              backgroundColor: '#f8fafc',
            },
          }}
        />
      </Paper>

      <Box sx={{ mt: 2 }}>
        <Typography variant="body2">
          <strong>Filas seleccionadas:</strong> {selectedRows.length > 0 ? selectedRows.join(', ') : 'Ninguna'}
        </Typography>
        <Typography variant="body2">
          <strong>Página actual:</strong> {paginationModel.page + 1}
        </Typography>
        <Typography variant="body2">
          <strong>Tamaño de página:</strong> {paginationModel.pageSize}
        </Typography>
      </Box>
    </Box>
  );
};
