import { useCallback, useEffect } from 'react';
import {
  useAuthStore,
  useAuthUser,
  useIsAuthenticated,
  useAuthStatus,
  useAuthError,
  useAuthInitialized,
  useAuthSession,
} from '../stores/useAuthStore';
import type { AuthCredentials } from '../services/supabaseAuth';

/**
 * Custom hook that provides authentication functionality using Zustand store
 * Now unified with Supabase authentication
 */
export const useAuth = () => {
  const user = useAuthUser();
  const session = useAuthSession();
  const isAuthenticated = useIsAuthenticated();
  const status = useAuthStatus();
  const error = useAuthError();
  const initialized = useAuthInitialized();
  const {
    signIn: signInAction,
    signUp: signUpAction,
    signOut: signOutAction,
    clearError,
    initialize,
  } = useAuthStore();

  // Initialize auth on mount
  useEffect(() => {
    if (!initialized) {
      initialize();
    }
  }, [initialized]);

  const signIn = useCallback(
    async (email: string, password: string) => {
      try {
        await signInAction({ email, password });
        return { user: useAuthStore.getState().user, error: null };
      } catch (error) {
        return {
          user: null,
          error:
            error instanceof Error ? error.message : 'Error de autenticación',
        };
      }
    },
    [signInAction],
  );

  const signUp = useCallback(
    async (credentials: AuthCredentials & { name?: string }) => {
      await signUpAction(credentials);
    },
    [signUpAction],
  );

  const signOut = useCallback(async () => {
    await signOutAction();
  }, [signOutAction]);

  const isLoading = status === 'loading';
  const hasError = !!error;

  return {
    user,
    session,
    isAuthenticated,
    isLoading,
    error,
    hasError,
    initialized,
    signIn,
    signUp,
    signOut,
    clearError,
    // Mantener compatibilidad con código existente
    login: signIn,
    logout: signOut,
  };
};
