import React from 'react';
import {
  Box,
  Paper,
  Grid,
  Card,
  CardHeader,
  CardContent,
  Skeleton,
  Divider,
  Container,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';

const ProfileSkeleton: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header con Avatar y Info Principal */}
      <Paper
        elevation={3}
        sx={{
          p: 4,
          mb: 4,
          display: 'flex',
          alignItems: 'center',
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 3,
          borderRadius: 2,
        }}
      >
        <Skeleton variant="circular" width={100} height={100} />
        <Box sx={{ textAlign: { xs: 'center', sm: 'left' }, flex: 1 }}>
          <Skeleton variant="text" width="60%" height={48} sx={{ mb: 1 }} />
          <Skeleton variant="text" width="40%" height={24} sx={{ mb: 1 }} />
          <Skeleton variant="rectangular" width={120} height={24} sx={{ borderRadius: 12 }} />
        </Box>
      </Paper>

      <Grid container spacing={4}>
        {/* Formulario de Información */}
        <Grid item xs={12} md={7}>
          <Card elevation={3} sx={{ borderRadius: 2 }}>
            <CardHeader
              title={<Skeleton variant="text" width="60%" height={32} />}
              subheader={<Skeleton variant="text" width="80%" height={20} />}
              avatar={<Skeleton variant="circular" width={24} height={24} />}
            />
            <Divider />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Skeleton variant="rectangular" height={56} sx={{ borderRadius: 1 }} />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Skeleton variant="rectangular" height={56} sx={{ borderRadius: 1 }} />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Skeleton variant="rectangular" height={56} sx={{ borderRadius: 1 }} />
                </Grid>
                <Grid item xs={12}>
                  <Skeleton variant="rectangular" height={100} sx={{ borderRadius: 1 }} />
                </Grid>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <Skeleton variant="rectangular" width={150} height={36} sx={{ borderRadius: 1 }} />
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Detalles del Sistema */}
        <Grid item xs={12} md={5}>
          <Card elevation={3} sx={{ borderRadius: 2, mb: 3 }}>
            <CardHeader
              title={<Skeleton variant="text" width="70%" height={32} />}
              subheader={<Skeleton variant="text" width="90%" height={20} />}
              avatar={<Skeleton variant="circular" width={24} height={24} />}
            />
            <Divider />
            <CardContent>
              <List>
                {Array.from({ length: 3 }).map((_, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <Skeleton variant="circular" width={24} height={24} />
                    </ListItemIcon>
                    <ListItemText
                      primary={<Skeleton variant="text" width="60%" height={20} />}
                      secondary={<Skeleton variant="text" width="80%" height={16} />}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>

          {/* Estadísticas */}
          <Card elevation={3} sx={{ borderRadius: 2 }}>
            <CardHeader
              title={<Skeleton variant="text" width="60%" height={32} />}
              avatar={<Skeleton variant="circular" width={24} height={24} />}
            />
            <Divider />
            <CardContent>
              <Grid container spacing={2}>
                {Array.from({ length: 4 }).map((_, index) => (
                  <Grid item xs={6} key={index}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                      <Skeleton variant="text" width="80%" height={32} sx={{ mx: 'auto' }} />
                      <Skeleton variant="text" width="60%" height={16} sx={{ mx: 'auto' }} />
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ProfileSkeleton;
