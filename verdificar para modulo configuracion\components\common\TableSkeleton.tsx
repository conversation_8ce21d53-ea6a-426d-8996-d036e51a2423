import React from 'react';
import {
  Box,
  Skeleton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import { motion } from 'framer-motion';

interface TableSkeletonProps {
  rows?: number;
  columns?: number;
  showHeader?: boolean;
  height?: number;
}

export const TableSkeleton: React.FC<TableSkeletonProps> = ({
  rows = 5,
  columns = 6,
  showHeader = true,
  height = 400,
}) => {
  const skeletonVariants = {
    initial: { opacity: 0.3 },
    animate: { opacity: 1 },
    transition: {
      duration: 0.8,
      repeat: Infinity,
      repeatType: 'reverse' as const,
    },
  };

  return (
    <Paper sx={{ height, width: '100%' }}>
      <TableContainer>
        <Table>
          {showHeader && (
            <TableHead>
              <TableRow>
                {Array.from({ length: columns }).map((_, index) => (
                  <TableCell key={`header-${index}`}>
                    <motion.div {...skeletonVariants}>
                      <Skeleton variant="text" width="80%" height={24} />
                    </motion.div>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
          )}
          <TableBody>
            {Array.from({ length: rows }).map((_, rowIndex) => (
              <TableRow key={`row-${rowIndex}`}>
                {Array.from({ length: columns }).map((_, colIndex) => (
                  <TableCell key={`cell-${rowIndex}-${colIndex}`}>
                    <motion.div
                      {...skeletonVariants}
                      transition={{
                        ...skeletonVariants.transition,
                        delay: rowIndex * 0.1 + colIndex * 0.05,
                      }}
                    >
                      {colIndex === 0 ? (
                        <Box
                          sx={{ display: 'flex', alignItems: 'center', gap: 2 }}
                        >
                          <Skeleton variant="circular" width={32} height={32} />
                          <Box sx={{ flex: 1 }}>
                            <Skeleton variant="text" width="70%" height={20} />
                            <Skeleton variant="text" width="50%" height={16} />
                          </Box>
                        </Box>
                      ) : colIndex === columns - 1 ? (
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Skeleton variant="circular" width={24} height={24} />
                          <Skeleton variant="circular" width={24} height={24} />
                        </Box>
                      ) : (
                        <Skeleton
                          variant={colIndex === 2 ? 'rounded' : 'text'}
                          width={colIndex === 2 ? 80 : '60%'}
                          height={colIndex === 2 ? 24 : 20}
                        />
                      )}
                    </motion.div>
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
};

// Skeleton específico para DataGrid
export const DataGridSkeleton: React.FC<{ rows?: number }> = ({
  rows = 10,
}) => {
  return (
    <Paper sx={{ height: 600, width: '100%', p: 2 }}>
      {/* Toolbar skeleton */}
      <Box
        sx={{ display: 'flex', justifyContent: 'space-between', mb: 2, p: 2 }}
      >
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Skeleton variant="rectangular" width={80} height={32} />
          <Skeleton variant="rectangular" width={80} height={32} />
          <Skeleton variant="rectangular" width={80} height={32} />
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Skeleton variant="rectangular" width={100} height={32} />
          <Skeleton variant="rectangular" width={120} height={32} />
        </Box>
      </Box>

      {/* Header skeleton */}
      <Box
        sx={{
          display: 'flex',
          borderBottom: '1px solid #e0e0e0',
          pb: 1,
          mb: 1,
        }}
      >
        <Skeleton variant="rectangular" width={40} height={24} sx={{ mr: 2 }} />
        <Skeleton variant="text" width={60} height={24} sx={{ mr: 4 }} />
        <Skeleton variant="text" width={150} height={24} sx={{ mr: 4 }} />
        <Skeleton variant="text" width={100} height={24} sx={{ mr: 4 }} />
        <Skeleton variant="text" width={80} height={24} sx={{ mr: 4 }} />
        <Skeleton variant="text" width={120} height={24} sx={{ mr: 4 }} />
        <Skeleton variant="text" width={80} height={24} />
      </Box>

      {/* Rows skeleton */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        {Array.from({ length: rows }).map((_, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', py: 1 }}>
              <Skeleton
                variant="rectangular"
                width={24}
                height={24}
                sx={{ mr: 2 }}
              />
              <Skeleton
                variant="circular"
                width={32}
                height={32}
                sx={{ mr: 2 }}
              />
              <Box sx={{ flex: 1, mr: 4 }}>
                <Skeleton variant="text" width="70%" height={20} />
                <Skeleton variant="text" width="50%" height={16} />
              </Box>
              <Skeleton
                variant="rounded"
                width={80}
                height={24}
                sx={{ mr: 4 }}
              />
              <Skeleton
                variant="rounded"
                width={60}
                height={24}
                sx={{ mr: 4 }}
              />
              <Skeleton variant="text" width={100} height={20} sx={{ mr: 4 }} />
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Skeleton variant="circular" width={24} height={24} />
                <Skeleton variant="circular" width={24} height={24} />
              </Box>
            </Box>
          </motion.div>
        ))}
      </Box>

      {/* Pagination skeleton */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mt: 2,
          pt: 2,
          borderTop: '1px solid #e0e0e0',
        }}
      >
        <Skeleton variant="text" width={150} height={20} />
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Skeleton variant="circular" width={32} height={32} />
          <Skeleton variant="circular" width={32} height={32} />
          <Skeleton variant="circular" width={32} height={32} />
          <Skeleton variant="circular" width={32} height={32} />
        </Box>
      </Box>
    </Paper>
  );
};
