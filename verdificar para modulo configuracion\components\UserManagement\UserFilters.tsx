import React from 'react';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  InputAdornment,
  Paper,
  Typography,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { UserProfile } from '../../services/userManagement';

export interface UserFilters {
  searchTerm: string;
  roleFilter: string;
  statusFilter: string;
}

interface UserFiltersProps {
  filters: UserFilters;
  onFiltersChange: (filters: UserFilters) => void;
  totalUsers: number;
  filteredUsers: number;
}

export const UserFiltersComponent: React.FC<UserFiltersProps> = ({
  filters,
  onFiltersChange,
  totalUsers,
  filteredUsers,
}) => {
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onFiltersChange({
      ...filters,
      searchTerm: event.target.value,
    });
  };

  const handleRoleFilterChange = (event: any) => {
    onFiltersChange({
      ...filters,
      roleFilter: event.target.value,
    });
  };

  const handleStatusFilterChange = (event: any) => {
    onFiltersChange({
      ...filters,
      statusFilter: event.target.value,
    });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      searchTerm: '',
      roleFilter: 'all',
      statusFilter: 'all',
    });
  };

  const hasActiveFilters = 
    filters.searchTerm !== '' || 
    filters.roleFilter !== 'all' || 
    filters.statusFilter !== 'all';

  const activeFiltersCount = [
    filters.searchTerm !== '',
    filters.roleFilter !== 'all',
    filters.statusFilter !== 'all',
  ].filter(Boolean).length;

  return (
    <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
        <FilterIcon color="primary" />
        <Typography variant="h6" component="h3">
          Filtros de Búsqueda
        </Typography>
        {hasActiveFilters && (
          <Chip
            label={`${activeFiltersCount} filtro${activeFiltersCount > 1 ? 's' : ''} activo${activeFiltersCount > 1 ? 's' : ''}`}
            color="primary"
            size="small"
            variant="outlined"
          />
        )}
        {hasActiveFilters && (
          <Tooltip title="Limpiar todos los filtros">
            <IconButton size="small" onClick={clearAllFilters}>
              <ClearIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      <Box sx={{ 
        display: 'flex', 
        gap: 2, 
        flexWrap: 'wrap',
        alignItems: 'center',
      }}>
        {/* Barra de búsqueda */}
        <TextField
          label="Buscar usuarios"
          placeholder="Buscar por nombre, email..."
          value={filters.searchTerm}
          onChange={handleSearchChange}
          variant="outlined"
          size="small"
          sx={{ minWidth: 300, flexGrow: 1 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
            endAdornment: filters.searchTerm && (
              <InputAdornment position="end">
                <IconButton
                  size="small"
                  onClick={() => onFiltersChange({ ...filters, searchTerm: '' })}
                >
                  <ClearIcon />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />

        {/* Filtro por rol */}
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel>Rol</InputLabel>
          <Select
            value={filters.roleFilter}
            onChange={handleRoleFilterChange}
            label="Rol"
          >
            <MenuItem value="all">Todos los roles</MenuItem>
            <MenuItem value="administrador">Administrador</MenuItem>
            <MenuItem value="psicologo">Psicólogo</MenuItem>
            <MenuItem value="paciente">Paciente</MenuItem>
          </Select>
        </FormControl>

        {/* Filtro por estado */}
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel>Estado</InputLabel>
          <Select
            value={filters.statusFilter}
            onChange={handleStatusFilterChange}
            label="Estado"
          >
            <MenuItem value="all">Todos los estados</MenuItem>
            <MenuItem value="active">Activos</MenuItem>
            <MenuItem value="inactive">Inactivos</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Contador de resultados */}
      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          {hasActiveFilters ? (
            <>
              Mostrando <strong>{filteredUsers}</strong> de <strong>{totalUsers}</strong> usuarios
            </>
          ) : (
            <>
              Total: <strong>{totalUsers}</strong> usuarios
            </>
          )}
        </Typography>
        
        {hasActiveFilters && (
          <Typography variant="body2" color="primary">
            Filtros aplicados
          </Typography>
        )}
      </Box>
    </Paper>
  );
};

// Hook personalizado para manejar el filtrado
export const useUserFilters = (users: UserProfile[]) => {
  const [filters, setFilters] = React.useState<UserFilters>({
    searchTerm: '',
    roleFilter: 'all',
    statusFilter: 'all',
  });

  const filteredUsers = React.useMemo(() => {
    return users.filter((user) => {
      // Filtro de búsqueda por texto
      const searchMatch =
        filters.searchTerm === '' ||
        user.full_name?.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        user.email?.toLowerCase().includes(filters.searchTerm.toLowerCase());

      // Filtro por rol
      const roleMatch =
        filters.roleFilter === 'all' ||
        user.role === filters.roleFilter;

      // Filtro por estado
      const statusMatch =
        filters.statusFilter === 'all' ||
        (filters.statusFilter === 'active' && user.is_active) ||
        (filters.statusFilter === 'inactive' && !user.is_active);

      return searchMatch && roleMatch && statusMatch;
    });
  }, [users, filters]);

  return {
    filters,
    setFilters,
    filteredUsers,
    totalUsers: users.length,
    filteredCount: filteredUsers.length,
  };
};
