import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Divider,
  Paper,
  Grid,
  Chip,
} from '@mui/material';
import { Report } from '../../services/reports';
import { formatCompleteReport } from '../../utils/reportFormatting';
import ScoreTable from './ScoreTable';
import ReportActions from './ReportActions';

interface ReportDialogProps {
  open: boolean;
  report: Report | null;
  onClose: () => void;
  onGeneratePdf: (report: Report) => void;
  isGeneratingPdf?: boolean;
}

/**
 * Componente de diálogo para visualizar reportes completos
 * Extraído de Informes.tsx para mejorar la modularidad
 */
const ReportDialog: React.FC<ReportDialogProps> = ({
  open,
  report,
  onClose,
  onGeneratePdf,
  isGeneratingPdf = false,
}) => {
  if (!report) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const parseScores = (scoresString: string | null): Record<string, number> => {
    if (!scoresString) return {};
    try {
      return JSON.parse(scoresString);
    } catch {
      return {};
    }
  };

  const directScores = parseScores(report.direct_scores);
  const centileScores = parseScores(report.centile_scores);

  // Separar scores por categorías
  const personalityScores = Object.fromEntries(
    Object.entries(directScores).filter(([key]) =>
      ['1', '2', '3', '4', '5', '6A', '6B', '7', '8A', '8B', '9'].includes(key),
    ),
  );

  const concernScores = Object.fromEntries(
    Object.entries(directScores).filter(([key]) =>
      ['A', 'B', 'C', 'D'].includes(key),
    ),
  );

  const syndromeScores = Object.fromEntries(
    Object.entries(directScores).filter(([key]) =>
      ['AA', 'BB', 'CC', 'DD', 'EE', 'FF', 'GG', 'HH', 'II'].includes(key),
    ),
  );

  const grossmanScores = Object.fromEntries(
    Object.entries(directScores).filter(
      ([key]) => key.includes('.'), // Facetas de Grossman tienen formato X.X
    ),
  );

  const validityScores = Object.fromEntries(
    Object.entries(directScores).filter(([key]) =>
      ['X', 'Y', 'Z', 'V', 'W'].includes(key),
    ),
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="xl"
      fullWidth
      PaperProps={{
        sx: {
          height: '90vh',
          maxWidth: '1400px', // Ampliar el ancho del contenedor
          width: '95vw',
        },
      }}
    >
      <DialogTitle>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Typography variant="h5" component="h2">
            Reporte MACI-II
          </Typography>
          <Chip label={`ID: ${report.id}`} color="primary" variant="outlined" />
        </Box>
      </DialogTitle>

      <DialogContent dividers sx={{ p: 0 }}>
        {/* Información del paciente */}
        <Paper sx={{ m: 2, p: 2, bgcolor: 'grey.50' }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Información del Paciente
              </Typography>
              <Typography>
                <strong>Nombre:</strong>{' '}
                {report.patient_name || 'No especificado'}
              </Typography>
              <Typography>
                <strong>Edad:</strong> {report.patient_age || 'No especificada'}
              </Typography>
              <Typography>
                <strong>Género:</strong>{' '}
                {report.patient_gender || 'No especificado'}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Información del Reporte
              </Typography>
              <Typography>
                <strong>Fecha:</strong> {formatDate(report.created_at)}
              </Typography>
              <Typography>
                <strong>Estado:</strong> {report.status || 'Completado'}
              </Typography>
            </Grid>
          </Grid>
        </Paper>

        <Box sx={{ px: 2 }}>
          {/* Tablas de puntuaciones */}
          {Object.keys(validityScores).length > 0 && (
            <ScoreTable
              title="Escalas de Validez"
              scores={validityScores}
              centileScores={centileScores}
              category="validity"
              showCentiles={true}
            />
          )}

          {Object.keys(personalityScores).length > 0 && (
            <ScoreTable
              title="Patrones de Personalidad"
              scores={personalityScores}
              centileScores={centileScores}
              category="personalityPatterns"
              showCentiles={true}
            />
          )}

          {Object.keys(concernScores).length > 0 && (
            <ScoreTable
              title="Preocupaciones Expresadas"
              scores={concernScores}
              centileScores={centileScores}
              category="expressedConcerns"
              showCentiles={true}
            />
          )}

          {Object.keys(syndromeScores).length > 0 && (
            <ScoreTable
              title="Síndromes Clínicos"
              scores={syndromeScores}
              centileScores={centileScores}
              category="clinicalSyndromes"
              showCentiles={true}
            />
          )}

          {Object.keys(grossmanScores).length > 0 && (
            <ScoreTable
              title="Facetas de Grossman"
              scores={grossmanScores}
              category="grossmanFacets"
              showCentiles={false}
            />
          )}

          {/* Interpretación cualitativa */}
          {report.qualitative_interpretation && (
            <Paper sx={{ mt: 3, p: 2 }}>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ color: 'primary.main' }}
              >
                Interpretación Cualitativa
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Box
                sx={{
                  '& .report-header': {
                    backgroundColor: '#1976d2',
                    color: 'white',
                    padding: '20px',
                    borderRadius: '12px',
                    fontWeight: 'bold',
                    fontSize: '1.4em',
                    margin: '0 0 24px 0',
                    textAlign: 'center',
                    boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                  },
                  '& .patient-info-item': {
                    display: 'flex',
                    alignItems: 'center',
                    padding: '12px 16px',
                    margin: '8px 0',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    borderLeft: '4px solid #1976d2',
                  },
                  '& .patient-label': {
                    fontWeight: 'bold',
                    color: '#1976d2',
                    minWidth: '180px',
                    fontSize: '1em',
                  },
                  '& .patient-value': {
                    color: '#333',
                    fontSize: '1em',
                    marginLeft: '8px',
                  },
                  '& .section-title-main': {
                    backgroundColor: '#00BCD4',
                    color: 'white',
                    padding: '12px 16px',
                    margin: '20px 0 16px 0',
                    borderRadius: '8px',
                    fontWeight: 'bold',
                    fontSize: '1.1em',
                    textAlign: 'center',
                  },
                  '& .grossman-pattern-title': {
                    color: '#00796B',
                    fontWeight: 'bold',
                    fontSize: '1.1em',
                    margin: '20px 0 16px 0',
                    padding: '0 0 8px 0',
                    borderBottom: '2px solid #00796B',
                    backgroundColor: 'transparent',
                  },
                  '& .grossman-scale-item': {
                    margin: '12px 0',
                    padding: '0',
                    backgroundColor: 'transparent',
                    border: 'none',
                    borderRadius: '0',
                  },

                  '& .grossman-interpretation': {
                    margin: '8px 0 16px 0',
                    padding: '0',
                    fontSize: '0.95em',
                    lineHeight: 1.6,
                    color: '#333',
                    textAlign: 'justify',
                    backgroundColor: 'transparent',
                    border: 'none',
                  },
                  '& .qualitative-scale-id': {
                    color: '#1565C0',
                    fontWeight: 'bold',
                    fontSize: '1.05em',
                  },
                  '& .qualitative-scale-name': {
                    color: '#1565C0',
                    fontWeight: 'bold',
                    fontSize: '1.05em',
                  },
                  '& .qualitative-scale-score': {
                    color: '#FF6F00',
                    fontWeight: 'bold',
                    backgroundColor: '#FFF3E0',
                    padding: '2px 6px',
                    borderRadius: '4px',
                    fontSize: '0.95em',
                  },
                  '& .qualitative-paragraph': {
                    textAlign: 'justify',
                    marginBottom: '12px',
                    lineHeight: 1.6,
                    textIndent: '20px',
                  },
                  '& .critical-alert': {
                    backgroundColor: '#ffebee',
                    border: '1px solid #f44336',
                    borderRadius: '4px',
                    padding: '8px',
                    margin: '8px 0',
                    color: '#d32f2f',
                    fontWeight: 'bold',
                  },
                }}
                dangerouslySetInnerHTML={{
                  __html: formatCompleteReport(
                    report.qualitative_interpretation,
                  ),
                }}
              />
            </Paper>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <ReportActions
          report={report}
          onGeneratePdf={onGeneratePdf}
          onClose={onClose}
          isGeneratingPdf={isGeneratingPdf}
          showCloseButton={true}
          variant="horizontal"
        />
      </DialogActions>
    </Dialog>
  );
};

export default ReportDialog;
