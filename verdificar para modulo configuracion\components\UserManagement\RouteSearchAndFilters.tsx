import React from 'react';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  InputAdornment,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { RoutePermission, UserRole } from '../../services/routePermissions';

interface RouteSearchAndFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  roleFilter: UserRole | 'all';
  onRoleFilterChange: (role: UserRole | 'all') => void;
  statusFilter: 'all' | 'enabled' | 'disabled';
  onStatusFilterChange: (status: 'all' | 'enabled' | 'disabled') => void;
  categoryFilter: string;
  onCategoryFilterChange: (category: string) => void;
  permissions: RoutePermission[];
  onClearFilters: () => void;
}

export const RouteSearchAndFilters: React.FC<RouteSearchAndFiltersProps> = ({
  searchTerm,
  onSearchChange,
  roleFilter,
  onRoleFilterChange,
  statusFilter,
  onStatusFilterChange,
  categoryFilter,
  onCategoryFilterChange,
  permissions,
  onClearFilters,
}) => {
  // Extraer categorías únicas de las rutas
  const categories = React.useMemo(() => {
    const categorySet = new Set<string>();
    permissions.forEach(permission => {
      const pathParts = permission.route_path.split('/');
      if (pathParts.length > 1) {
        categorySet.add(pathParts[1]); // Primera parte después de '/'
      }
    });
    return Array.from(categorySet).sort();
  }, [permissions]);

  const hasActiveFilters = searchTerm || roleFilter !== 'all' || statusFilter !== 'all' || categoryFilter;

  const getFilterCount = () => {
    let count = 0;
    if (searchTerm) count++;
    if (roleFilter !== 'all') count++;
    if (statusFilter !== 'all') count++;
    if (categoryFilter) count++;
    return count;
  };

  return (
    <Box sx={{ mb: 3 }}>
      {/* Búsqueda Principal */}
      <TextField
        fullWidth
        variant="outlined"
        placeholder="Buscar rutas por nombre o path..."
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon color="action" />
            </InputAdornment>
          ),
          endAdornment: searchTerm && (
            <InputAdornment position="end">
              <IconButton
                size="small"
                onClick={() => onSearchChange('')}
                edge="end"
              >
                <ClearIcon />
              </IconButton>
            </InputAdornment>
          ),
        }}
        sx={{ mb: 2 }}
      />

      {/* Filtros Avanzados */}
      <Accordion>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{
            backgroundColor: 'grey.50',
            '&:hover': {
              backgroundColor: 'grey.100',
            },
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FilterIcon />
            <Typography variant="subtitle2">
              Filtros Avanzados
            </Typography>
            {hasActiveFilters && (
              <Chip
                label={`${getFilterCount()} activo${getFilterCount() > 1 ? 's' : ''}`}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* Primera fila de filtros */}
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              {/* Filtro por Rol */}
              <FormControl sx={{ minWidth: 200 }}>
                <InputLabel>Filtrar por Rol</InputLabel>
                <Select
                  value={roleFilter}
                  label="Filtrar por Rol"
                  onChange={(e) => onRoleFilterChange(e.target.value as UserRole | 'all')}
                >
                  <MenuItem value="all">Todos los roles</MenuItem>
                  <MenuItem value="administrador">Administrador</MenuItem>
                  <MenuItem value="psicologo">Psicólogo</MenuItem>
                  <MenuItem value="paciente">Paciente</MenuItem>
                </Select>
              </FormControl>

              {/* Filtro por Estado */}
              <FormControl sx={{ minWidth: 200 }}>
                <InputLabel>Estado de la Ruta</InputLabel>
                <Select
                  value={statusFilter}
                  label="Estado de la Ruta"
                  onChange={(e) => onStatusFilterChange(e.target.value as 'all' | 'enabled' | 'disabled')}
                >
                  <MenuItem value="all">Todas las rutas</MenuItem>
                  <MenuItem value="enabled">Habilitadas</MenuItem>
                  <MenuItem value="disabled">Deshabilitadas</MenuItem>
                </Select>
              </FormControl>

              {/* Filtro por Categoría */}
              <FormControl sx={{ minWidth: 200 }}>
                <InputLabel>Categoría</InputLabel>
                <Select
                  value={categoryFilter}
                  label="Categoría"
                  onChange={(e) => onCategoryFilterChange(e.target.value)}
                >
                  <MenuItem value="">Todas las categorías</MenuItem>
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            {/* Acciones de filtros */}
            {hasActiveFilters && (
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', pt: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  {getFilterCount()} filtro{getFilterCount() > 1 ? 's' : ''} aplicado{getFilterCount() > 1 ? 's' : ''}
                </Typography>
                <IconButton
                  size="small"
                  onClick={onClearFilters}
                  color="primary"
                >
                  <ClearIcon />
                  <Typography variant="caption" sx={{ ml: 0.5 }}>
                    Limpiar filtros
                  </Typography>
                </IconButton>
              </Box>
            )}
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* Chips de filtros activos */}
      {hasActiveFilters && (
        <Box sx={{ display: 'flex', gap: 1, mt: 2, flexWrap: 'wrap' }}>
          {searchTerm && (
            <Chip
              label={`Búsqueda: "${searchTerm}"`}
              onDelete={() => onSearchChange('')}
              size="small"
              variant="outlined"
            />
          )}
          {roleFilter !== 'all' && (
            <Chip
              label={`Rol: ${roleFilter}`}
              onDelete={() => onRoleFilterChange('all')}
              size="small"
              variant="outlined"
            />
          )}
          {statusFilter !== 'all' && (
            <Chip
              label={`Estado: ${statusFilter === 'enabled' ? 'Habilitadas' : 'Deshabilitadas'}`}
              onDelete={() => onStatusFilterChange('all')}
              size="small"
              variant="outlined"
            />
          )}
          {categoryFilter && (
            <Chip
              label={`Categoría: ${categoryFilter}`}
              onDelete={() => onCategoryFilterChange('')}
              size="small"
              variant="outlined"
            />
          )}
        </Box>
      )}
    </Box>
  );
};

// Hook para manejar el estado de filtros
export const useRouteFilters = (permissions: RoutePermission[]) => {
  const [searchTerm, setSearchTerm] = React.useState('');
  const [roleFilter, setRoleFilter] = React.useState<UserRole | 'all'>('all');
  const [statusFilter, setStatusFilter] = React.useState<'all' | 'enabled' | 'disabled'>('all');
  const [categoryFilter, setCategoryFilter] = React.useState('');

  const filteredPermissions = React.useMemo(() => {
    return permissions.filter(permission => {
      // Filtro de búsqueda
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch = 
          permission.route_name.toLowerCase().includes(searchLower) ||
          permission.route_path.toLowerCase().includes(searchLower) ||
          permission.route_description?.toLowerCase().includes(searchLower);
        if (!matchesSearch) return false;
      }

      // Filtro por rol
      if (roleFilter !== 'all') {
        if (!permission.allowed_roles.includes(roleFilter)) return false;
      }

      // Filtro por estado
      if (statusFilter !== 'all') {
        const isEnabled = permission.is_enabled;
        if (statusFilter === 'enabled' && !isEnabled) return false;
        if (statusFilter === 'disabled' && isEnabled) return false;
      }

      // Filtro por categoría
      if (categoryFilter) {
        const pathParts = permission.route_path.split('/');
        const category = pathParts.length > 1 ? pathParts[1] : '';
        if (category !== categoryFilter) return false;
      }

      return true;
    });
  }, [permissions, searchTerm, roleFilter, statusFilter, categoryFilter]);

  const clearFilters = () => {
    setSearchTerm('');
    setRoleFilter('all');
    setStatusFilter('all');
    setCategoryFilter('');
  };

  return {
    searchTerm,
    setSearchTerm,
    roleFilter,
    setRoleFilter,
    statusFilter,
    setStatusFilter,
    categoryFilter,
    setCategoryFilter,
    filteredPermissions,
    clearFilters,
  };
};
