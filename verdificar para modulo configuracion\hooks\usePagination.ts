import { useState, useMemo, useCallback } from 'react';

export interface PaginationState {
  page: number;
  pageSize: number;
}

export interface PaginationInfo {
  totalItems: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  startIndex: number;
  endIndex: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface UsePaginationReturn<T> {
  // Estado de paginación
  paginationModel: PaginationState;
  setPaginationModel: (model: PaginationState) => void;
  
  // Datos paginados
  paginatedData: T[];
  
  // Información de paginación
  paginationInfo: PaginationInfo;
  
  // Funciones de navegación
  goToPage: (page: number) => void;
  goToNextPage: () => void;
  goToPreviousPage: () => void;
  goToFirstPage: () => void;
  goToLastPage: () => void;
  changePageSize: (pageSize: number) => void;
  
  // Utilidades
  resetPagination: () => void;
}

export const usePagination = <T>(
  data: T[],
  initialPageSize: number = 20,
  initialPage: number = 0
): UsePaginationReturn<T> => {
  const [paginationModel, setPaginationModel] = useState<PaginationState>({
    page: initialPage,
    pageSize: initialPageSize,
  });

  // Calcular información de paginación
  const paginationInfo = useMemo((): PaginationInfo => {
    const totalItems = data.length;
    const totalPages = Math.ceil(totalItems / paginationModel.pageSize);
    const startIndex = paginationModel.page * paginationModel.pageSize;
    const endIndex = Math.min(startIndex + paginationModel.pageSize, totalItems);

    return {
      totalItems,
      totalPages,
      currentPage: paginationModel.page,
      pageSize: paginationModel.pageSize,
      startIndex: startIndex + 1, // 1-based para mostrar al usuario
      endIndex,
      hasNextPage: paginationModel.page < totalPages - 1,
      hasPreviousPage: paginationModel.page > 0,
    };
  }, [data.length, paginationModel]);

  // Obtener datos paginados
  const paginatedData = useMemo(() => {
    const startIndex = paginationModel.page * paginationModel.pageSize;
    const endIndex = startIndex + paginationModel.pageSize;
    return data.slice(startIndex, endIndex);
  }, [data, paginationModel]);

  // Funciones de navegación
  const goToPage = useCallback((page: number) => {
    const maxPage = Math.max(0, Math.ceil(data.length / paginationModel.pageSize) - 1);
    const validPage = Math.max(0, Math.min(page, maxPage));
    
    setPaginationModel(prev => ({
      ...prev,
      page: validPage,
    }));
  }, [data.length, paginationModel.pageSize]);

  const goToNextPage = useCallback(() => {
    if (paginationInfo.hasNextPage) {
      goToPage(paginationModel.page + 1);
    }
  }, [paginationInfo.hasNextPage, paginationModel.page, goToPage]);

  const goToPreviousPage = useCallback(() => {
    if (paginationInfo.hasPreviousPage) {
      goToPage(paginationModel.page - 1);
    }
  }, [paginationInfo.hasPreviousPage, paginationModel.page, goToPage]);

  const goToFirstPage = useCallback(() => {
    goToPage(0);
  }, [goToPage]);

  const goToLastPage = useCallback(() => {
    const lastPage = Math.max(0, Math.ceil(data.length / paginationModel.pageSize) - 1);
    goToPage(lastPage);
  }, [data.length, paginationModel.pageSize, goToPage]);

  const changePageSize = useCallback((pageSize: number) => {
    // Calcular la nueva página para mantener aproximadamente los mismos elementos visibles
    const currentFirstItem = paginationModel.page * paginationModel.pageSize;
    const newPage = Math.floor(currentFirstItem / pageSize);
    
    setPaginationModel({
      page: newPage,
      pageSize,
    });
  }, [paginationModel]);

  const resetPagination = useCallback(() => {
    setPaginationModel({
      page: 0,
      pageSize: initialPageSize,
    });
  }, [initialPageSize]);

  return {
    paginationModel,
    setPaginationModel,
    paginatedData,
    paginationInfo,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    goToFirstPage,
    goToLastPage,
    changePageSize,
    resetPagination,
  };
};
