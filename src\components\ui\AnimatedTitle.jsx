import React from 'react';

/**
 * Componente de título animado para BAT-7
 * Incluye efectos visuales y animaciones
 */
const AnimatedTitle = ({ className = "" }) => {
  return (
    <div className={`animated-title-container ${className}`}>
      <div className="relative inline-flex items-center">
        {/* Título principal con efectos */}
        <h1 className="animated-title text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
          <span className="title-part-1">BAT-7</span>
          <span className="title-separator mx-2 text-gray-400">|</span>
          <span className="title-part-2 text-lg">Batería de Aptitudes</span>
        </h1>
        
        {/* Efectos de brillo */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-blue-600/20 rounded-lg blur-sm animate-pulse"></div>
        
        {/* Partículas decorativas */}
        <div className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-400 rounded-full animate-ping"></div>
        <div className="absolute -bottom-1 -left-1 w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce delay-300"></div>
      </div>
      
      <style jsx>{`
        .animated-title-container {
          position: relative;
          display: inline-block;
        }
        
        .animated-title {
          position: relative;
          z-index: 10;
          animation: titleGlow 3s ease-in-out infinite alternate;
          text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
        }
        
        .title-part-1 {
          animation: slideInLeft 1s ease-out;
          font-weight: 800;
          letter-spacing: 1px;
        }
        
        .title-part-2 {
          animation: slideInRight 1s ease-out 0.3s both;
          font-weight: 600;
          opacity: 0.9;
        }
        
        .title-separator {
          animation: fadeIn 1s ease-out 0.6s both;
          transform: scaleY(1.2);
        }
        
        @keyframes titleGlow {
          0% {
            filter: brightness(1) drop-shadow(0 0 5px rgba(59, 130, 246, 0.3));
          }
          100% {
            filter: brightness(1.1) drop-shadow(0 0 15px rgba(59, 130, 246, 0.5));
          }
        }
        
        @keyframes slideInLeft {
          0% {
            transform: translateX(-30px);
            opacity: 0;
          }
          100% {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        @keyframes slideInRight {
          0% {
            transform: translateX(30px);
            opacity: 0;
          }
          100% {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        @keyframes fadeIn {
          0% {
            opacity: 0;
            transform: scale(0.8);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }
        
        /* Efectos hover */
        .animated-title-container:hover .animated-title {
          animation-duration: 1s;
          transform: scale(1.02);
          transition: transform 0.3s ease;
        }
        
        .animated-title-container:hover .title-part-1 {
          color: #3b82f6;
          transition: color 0.3s ease;
        }
        
        .animated-title-container:hover .title-part-2 {
          color: #8b5cf6;
          transition: color 0.3s ease;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
          .animated-title {
            font-size: 1.5rem;
          }
          
          .title-part-2 {
            font-size: 1rem;
          }
        }
        
        @media (max-width: 480px) {
          .animated-title {
            font-size: 1.25rem;
          }
          
          .title-part-2 {
            font-size: 0.875rem;
          }
          
          .title-separator {
            margin: 0 0.5rem;
          }
        }
      `}</style>
    </div>
  );
};

export default AnimatedTitle;
