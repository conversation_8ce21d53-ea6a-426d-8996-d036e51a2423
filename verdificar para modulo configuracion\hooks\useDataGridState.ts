import { useState, useCallback, useMemo } from 'react';
import { GridSortModel, GridPaginationModel } from '@mui/x-data-grid';

interface UseDataGridStateOptions {
  initialPageSize?: number;
  initialSortField?: string;
  initialSortDirection?: 'asc' | 'desc';
}

export const useDataGridState = (options: UseDataGridStateOptions = {}) => {
  const {
    initialPageSize = 20,
    initialSortField = 'created_at',
    initialSortDirection = 'desc'
  } = options;

  // Estado de paginación con inicialización segura
  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
    page: 0,
    pageSize: initialPageSize,
  });

  // Estado de ordenamiento con inicialización segura
  const [sortModel, setSortModel] = useState<GridSortModel>([
    { field: initialSortField, sort: initialSortDirection }
  ]);

  // Manejadores seguros con logging
  const handlePaginationChange = useCallback((newModel: GridPaginationModel) => {
    console.log('📄 Pagination changed:', newModel);
    
    // Validar que el modelo sea válido
    if (typeof newModel.page !== 'number' || typeof newModel.pageSize !== 'number') {
      console.error('❌ Invalid pagination model:', newModel);
      return;
    }

    // Validar rangos
    if (newModel.page < 0 || newModel.pageSize <= 0) {
      console.error('❌ Invalid pagination values:', newModel);
      return;
    }

    setPaginationModel(newModel);
  }, []);

  const handleSortChange = useCallback((newModel: GridSortModel) => {
    console.log('🔄 Sort changed:', newModel);
    
    // Validar que el modelo sea un array
    if (!Array.isArray(newModel)) {
      console.error('❌ Invalid sort model:', newModel);
      return;
    }

    setSortModel(newModel);
  }, []);

  // Reset functions
  const resetPagination = useCallback(() => {
    setPaginationModel({
      page: 0,
      pageSize: initialPageSize,
    });
  }, [initialPageSize]);

  const resetSort = useCallback(() => {
    setSortModel([
      { field: initialSortField, sort: initialSortDirection }
    ]);
  }, [initialSortField, initialSortDirection]);

  const resetAll = useCallback(() => {
    resetPagination();
    resetSort();
  }, [resetPagination, resetSort]);

  // Computed values
  const currentPage = useMemo(() => paginationModel.page, [paginationModel.page]);
  const currentPageSize = useMemo(() => paginationModel.pageSize, [paginationModel.pageSize]);
  const currentSort = useMemo(() => sortModel[0] || null, [sortModel]);

  // Debug info
  const debugInfo = useMemo(() => ({
    paginationModel,
    sortModel,
    currentPage,
    currentPageSize,
    currentSort,
  }), [paginationModel, sortModel, currentPage, currentPageSize, currentSort]);

  return {
    // State
    paginationModel,
    sortModel,
    
    // Handlers
    handlePaginationChange,
    handleSortChange,
    
    // Reset functions
    resetPagination,
    resetSort,
    resetAll,
    
    // Computed values
    currentPage,
    currentPageSize,
    currentSort,
    
    // Debug
    debugInfo,
  };
};

// Hook para manejo seguro de selección
export const useDataGridSelection = (initialSelection: string[] = []) => {
  const [selectedRows, setSelectedRows] = useState<string[]>(initialSelection);

  const handleSelectionChange = useCallback((newSelection: string[]) => {
    console.log('✅ Selection changed:', newSelection);
    
    // Validar que sea un array
    if (!Array.isArray(newSelection)) {
      console.error('❌ Invalid selection:', newSelection);
      return;
    }

    setSelectedRows(newSelection);
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedRows([]);
  }, []);

  const selectAll = useCallback((allIds: string[]) => {
    if (!Array.isArray(allIds)) {
      console.error('❌ Invalid allIds for selectAll:', allIds);
      return;
    }
    setSelectedRows(allIds);
  }, []);

  const isSelected = useCallback((id: string) => {
    return selectedRows.includes(id);
  }, [selectedRows]);

  const toggleSelection = useCallback((id: string) => {
    setSelectedRows(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  }, []);

  return {
    selectedRows,
    handleSelectionChange,
    clearSelection,
    selectAll,
    isSelected,
    toggleSelection,
    selectedCount: selectedRows.length,
    hasSelection: selectedRows.length > 0,
  };
};

// Hook combinado para DataGrid completo
export const useDataGrid = (options: UseDataGridStateOptions & {
  initialSelection?: string[];
} = {}) => {
  const { initialSelection = [], ...gridOptions } = options;
  
  const gridState = useDataGridState(gridOptions);
  const selectionState = useDataGridSelection(initialSelection);

  const resetAll = useCallback(() => {
    gridState.resetAll();
    selectionState.clearSelection();
  }, [gridState, selectionState]);

  return {
    ...gridState,
    ...selectionState,
    resetAll,
  };
};
