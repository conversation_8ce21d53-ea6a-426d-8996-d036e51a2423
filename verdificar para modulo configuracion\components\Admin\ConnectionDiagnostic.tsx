import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material';
import {
  Wifi as WifiIcon,
  WifiOff as WifiOffIcon,
  Security as SecurityIcon,
  Storage as StorageIcon,
  Refresh as RefreshIcon,
  Speed as SpeedIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { useSupabaseDiagnostic } from '../../hooks/useSupabaseDiagnostic';

export const ConnectionDiagnostic: React.FC = () => {
  const {
    diagnostic,
    isRunning,
    runDiagnostic,
    getStatusColor,
    getStatusText,
    getDetailedStatus,
  } = useSupabaseDiagnostic();

  const detailedStatus = getDetailedStatus();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
      case 'authenticated':
        return <WifiIcon color="success" />;
      case 'error':
      case 'disconnected':
        return <WifiOffIcon color="error" />;
      default:
        return <CircularProgress size={20} />;
    }
  };

  return (
    <Card elevation={2} sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {diagnostic.isConnected ? <WifiIcon color="success" /> : <WifiOffIcon color="error" />}
            Estado de Conexión
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip
              label={getStatusText()}
              color={getStatusColor()}
              variant="outlined"
              size="small"
            />
            <Button
              size="small"
              startIcon={isRunning ? <CircularProgress size={16} /> : <RefreshIcon />}
              onClick={runDiagnostic}
              disabled={isRunning}
              variant="outlined"
            >
              {isRunning ? 'Verificando...' : 'Verificar'}
            </Button>
          </Box>
        </Box>

        {diagnostic.error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>Error de Conexión:</strong> {diagnostic.error}
            </Typography>
          </Alert>
        )}

        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, p: 1 }}>
              <SecurityIcon color={diagnostic.authStatus === 'authenticated' ? 'success' : 'error'} />
              <Box>
                <Typography variant="body2" fontWeight="bold">
                  Autenticación
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {detailedStatus.auth.text}
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, p: 1 }}>
              <StorageIcon color={diagnostic.dbStatus === 'connected' ? 'success' : 'error'} />
              <Box>
                <Typography variant="body2" fontWeight="bold">
                  Base de Datos
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {detailedStatus.database.text}
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, p: 1 }}>
              <SpeedIcon color={diagnostic.latency && diagnostic.latency < 1000 ? 'success' : 'warning'} />
              <Box>
                <Typography variant="body2" fontWeight="bold">
                  Latencia
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {detailedStatus.latency}
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ScheduleIcon fontSize="small" color="action" />
            <Typography variant="caption" color="text.secondary">
              Última verificación: {detailedStatus.lastCheck}
            </Typography>
          </Box>

          {!diagnostic.isConnected && (
            <Typography variant="caption" color="warning.main">
              Verificación automática cada 30 segundos
            </Typography>
          )}
        </Box>

        {diagnostic.isConnected && (
          <Alert severity="success" sx={{ mt: 2 }}>
            <Typography variant="body2">
              ✅ Conexión estable. Todos los módulos deberían funcionar correctamente.
            </Typography>
          </Alert>
        )}

        {!diagnostic.isConnected && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            <Typography variant="body2">
              ⚠️ Problemas de conexión detectados. Algunos módulos pueden no funcionar correctamente.
            </Typography>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};
