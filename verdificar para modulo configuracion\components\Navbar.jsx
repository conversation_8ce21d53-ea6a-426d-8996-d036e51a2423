import React from 'react';
import { Link } from 'react-router-dom';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';

function Navbar() {
  return (
    <AppBar position="static">
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          MACI-II
        </Typography>
        <Button color="inherit" component={Link} to="/">
          Inicio
        </Button>
        <Button color="inherit" component={Link} to="/cuestionario">
          Cuestionario
        </Button>
        <Button color="inherit" component={Link} to="/escalas">
          Escalas
        </Button>
        <Button color="inherit" component={Link} to="/pacientes">
          Pacientes
        </Button>
        <Button color="inherit" component={Link} to="/configuracion">
          Configuración
        </Button>
      </Toolbar>
    </AppBar>
  );
}

export default Navbar;
