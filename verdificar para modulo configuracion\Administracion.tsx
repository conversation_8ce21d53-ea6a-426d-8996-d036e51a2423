import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Alert,
  Tabs,
  Tab,
} from '@mui/material';
import {
  People as PeopleIcon,
  Settings as SettingsIcon,
  Dashboard as DashboardIcon,
  Security as SecurityIcon,
  Assessment as AssessmentIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { UserManagementPanel } from '../components/UserManagement/UserManagementPanel';
import PageAccessPanel from '../components/UserManagement/PageAccessPanel';
import AuthConfigPanel from '../components/UserManagement/AuthConfigPanel';
import { AdminDashboard } from '../components/Dashboard/AdminDashboard';
import UsageControlPanel from '../components/UserManagement/UsageControlPanel';
import PatientAssignmentPanel from '../components/UserManagement/PatientAssignmentPanel';
import { usePermissions } from '../hooks/useUserManagement';
import { PageTransition } from '../components/Common/AnimatedDialog';
import Logo from '../components/common/Logo';
import { ConnectionDiagnostic } from '../components/Admin/ConnectionDiagnostic';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
};

const Administracion: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const { permissions, loading, error, canAccessAdminPanel } = usePermissions();

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Mostrar loading mientras se verifican permisos
  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Administración
        </Typography>
        <Typography>Verificando permisos...</Typography>
      </Box>
    );
  }

  // Mostrar error si hay problemas de conexión
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Administración
        </Typography>
        <Alert severity="error" sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            Error de Conexión
          </Typography>
          <Typography variant="body2">
            {error}
          </Typography>
        </Alert>
        <Alert severity="info">
          <Typography variant="body2">
            Verifica tu conexión a internet y que el servicio de Supabase esté disponible.
            La página se actualizará automáticamente cuando se restablezca la conexión.
          </Typography>
        </Alert>
      </Box>
    );
  }

  // Verificar permisos de administrador
  if (!canAccessAdminPanel) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Administración
        </Typography>
        <Alert severity="error">
          Acceso denegado. Se requieren permisos de administrador para acceder a
          esta sección.
        </Alert>
      </Box>
    );
  }

  return (
    <PageTransition>
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2, gap: 2 }}>
          <Logo size="large" showText={false} />
          <Typography variant="h4" component="h1" gutterBottom sx={{ textAlign: 'center' }}>
            Panel de Administración
          </Typography>
        </Box>

        <Typography variant="body1" color="text.secondary" sx={{ mb: 3, textAlign: 'center' }}>
          Gestiona usuarios, configuraciones y estadísticas del sistema
        </Typography>

        {/* Diagnóstico de Conexión */}
        <ConnectionDiagnostic />

        <Paper sx={{ width: '100%' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="admin tabs"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab
              icon={<DashboardIcon />}
              label="Dashboard"
              id="admin-tab-0"
              aria-controls="admin-tabpanel-0"
            />
            <Tab
              icon={<PeopleIcon />}
              label="Gestión de Usuarios"
              id="admin-tab-1"
              aria-controls="admin-tabpanel-1"
            />
            <Tab
              icon={<SecurityIcon />}
              label="Control de Acceso"
              id="admin-tab-2"
              aria-controls="admin-tabpanel-2"
            />
            <Tab
              icon={<AssignmentIcon />}
              label="Asignación de Pacientes"
              id="admin-tab-3"
              aria-controls="admin-tabpanel-3"
            />
            <Tab
              icon={<AssessmentIcon />}
              label="Control de Usos"
              id="admin-tab-4"
              aria-controls="admin-tabpanel-4"
            />
            <Tab
              icon={<SettingsIcon />}
              label="Configuración"
              id="admin-tab-5"
              aria-controls="admin-tabpanel-5"
            />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <AdminDashboard />
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <UserManagementPanel />
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <PageAccessPanel />
          </TabPanel>

          <TabPanel value={tabValue} index={3}>
            <PatientAssignmentPanel />
          </TabPanel>

          <TabPanel value={tabValue} index={4}>
            <UsageControlPanel />
          </TabPanel>

          <TabPanel value={tabValue} index={5}>
            <PageTransition>
              <AuthConfigPanel />
            </PageTransition>
          </TabPanel>
        </Paper>
      </Box>
    </PageTransition>
  );
};

export default Administracion;
