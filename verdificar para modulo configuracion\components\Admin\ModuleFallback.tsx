import React from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CircularProgress,
  Skeleton,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';

interface ModuleFallbackProps {
  type: 'loading' | 'error' | 'empty' | 'no-permission';
  title?: string;
  message?: string;
  onRetry?: () => void;
  showRetry?: boolean;
}

interface ModuleWrapperProps {
  loading: boolean;
  error: string | null;
  onRetry?: () => void;
  children: React.ReactNode;
  showSkeletonOnLoading?: boolean;
  cacheInfo?: {
    hasData: boolean;
    isStale: boolean;
    age: number;
  };
}

export const ModuleFallback: React.FC<ModuleFallbackProps> = ({
  type,
  title,
  message,
  onRetry,
  showRetry = true,
}) => {
  const getContent = () => {
    switch (type) {
      case 'loading':
        return (
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <CircularProgress size={24} />
                <Typography variant="h6">
                  {title || 'Cargando módulo...'}
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {message || 'Por favor espera mientras se cargan los datos.'}
              </Typography>
              
              {/* Skeleton para simular contenido */}
              <Box sx={{ mt: 2 }}>
                <Skeleton variant="rectangular" height={40} sx={{ mb: 1 }} />
                <Skeleton variant="rectangular" height={200} sx={{ mb: 1 }} />
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Skeleton variant="rectangular" width={100} height={36} />
                  <Skeleton variant="rectangular" width={100} height={36} />
                </Box>
              </Box>
            </CardContent>
          </Card>
        );

      case 'error':
        return (
          <Alert 
            severity="error" 
            icon={<ErrorIcon />}
            action={
              showRetry && onRetry ? (
                <Button
                  color="inherit"
                  size="small"
                  startIcon={<RefreshIcon />}
                  onClick={onRetry}
                >
                  Reintentar
                </Button>
              ) : null
            }
          >
            <Typography variant="h6" gutterBottom>
              {title || 'Error al cargar el módulo'}
            </Typography>
            <Typography variant="body2">
              {message || 'Se produjo un error al cargar este módulo. Verifica tu conexión e intenta nuevamente.'}
            </Typography>
          </Alert>
        );

      case 'empty':
        return (
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <WarningIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {title || 'No hay datos disponibles'}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {message || 'Este módulo no tiene datos para mostrar en este momento.'}
              </Typography>
              {showRetry && onRetry && (
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={onRetry}
                >
                  Actualizar
                </Button>
              )}
            </CardContent>
          </Card>
        );

      case 'no-permission':
        return (
          <Alert severity="warning" icon={<WarningIcon />}>
            <Typography variant="h6" gutterBottom>
              {title || 'Acceso restringido'}
            </Typography>
            <Typography variant="body2">
              {message || 'No tienes permisos suficientes para acceder a este módulo.'}
            </Typography>
          </Alert>
        );

      default:
        return (
          <Alert severity="info">
            <Typography variant="body2">
              Módulo no disponible
            </Typography>
          </Alert>
        );
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      {getContent()}
    </Box>
  );
};

// Hook para usar con Suspense
export const useModuleLoader = (loadFunction: () => Promise<any>) => {
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [data, setData] = React.useState<any>(null);

  const load = React.useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await loadFunction();
      setData(result);
    } catch (err) {
      console.error('Error loading module:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  }, [loadFunction]);

  React.useEffect(() => {
    load();
  }, [load]);

  return {
    loading,
    error,
    data,
    retry: load,
  };
};

// Componente wrapper para módulos
export const ModuleWrapper: React.FC<{
  children: React.ReactNode;
  loading?: boolean;
  error?: string | null;
  onRetry?: () => void;
  fallbackType?: 'loading' | 'error' | 'empty';
}> = ({ 
  children, 
  loading = false, 
  error = null, 
  onRetry,
  fallbackType = 'loading'
}) => {
  if (loading) {
    return <ModuleFallback type="loading" onRetry={onRetry} />;
  }

  if (error) {
    return (
      <ModuleFallback 
        type="error" 
        message={error} 
        onRetry={onRetry}
        showRetry={!!onRetry}
      />
    );
  }

  return <>{children}</>;
};

/**
 * Componente optimizado para módulos con cache
 * Evita mostrar loading innecesario cuando hay datos en cache
 */
export const OptimizedModuleWrapper: React.FC<ModuleWrapperProps> = ({
  loading,
  error,
  onRetry,
  children,
  showSkeletonOnLoading = true,
  cacheInfo,
}) => {
  // Si hay datos en cache y están cargando, mostrar los datos con indicador sutil
  if (loading && cacheInfo?.hasData) {
    return (
      <Box sx={{ position: 'relative' }}>
        {/* Indicador sutil de carga */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            zIndex: 1,
            p: 1,
          }}
        >
          <CircularProgress size={20} />
        </Box>

        {/* Contenido con datos de cache */}
        <Box sx={{ opacity: cacheInfo.isStale ? 0.8 : 1 }}>
          {children}
        </Box>

        {/* Indicador de datos obsoletos */}
        {cacheInfo.isStale && (
          <Alert
            severity="info"
            sx={{ mt: 1 }}
            action={
              onRetry && (
                <Button size="small" onClick={onRetry}>
                  Actualizar
                </Button>
              )
            }
          >
            <Typography variant="caption">
              Datos de hace {Math.round(cacheInfo.age / 1000 / 60)} minutos. Actualizando...
            </Typography>
          </Alert>
        )}
      </Box>
    );
  }

  // Si está cargando sin datos en cache, mostrar loading normal
  if (loading) {
    return showSkeletonOnLoading ? (
      <ModuleFallback type="loading" onRetry={onRetry} />
    ) : (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Si hay error, mostrar error
  if (error) {
    return (
      <ModuleFallback
        type="error"
        message={error}
        onRetry={onRetry}
        showRetry={!!onRetry}
      />
    );
  }

  // Mostrar contenido normal
  return <>{children}</>;
};
