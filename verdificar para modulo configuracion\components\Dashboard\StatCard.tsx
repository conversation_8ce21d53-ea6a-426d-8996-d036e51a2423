import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  Chip,
} from '@mui/material';
import { motion, useSpring, useTransform, useMotionValue } from 'framer-motion';

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period: string;
  };
  loading?: boolean;
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  color = 'primary',
  change,
  loading = false,
}) => {
  const motionValue = useMotionValue(0);
  const spring = useSpring(motionValue, { stiffness: 300, damping: 30 });
  const displayValue = useTransform(spring, (latest) => Math.round(latest));

  React.useEffect(() => {
    if (typeof value === 'number' && !loading) {
      motionValue.set(value);
    }
  }, [value, loading, motionValue]);

  const getChangeColor = (type: 'increase' | 'decrease') => {
    return type === 'increase' ? 'success' : 'error';
  };

  const getChangeSymbol = (type: 'increase' | 'decrease') => {
    return type === 'increase' ? '+' : '-';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{
        y: -4,
        transition: { duration: 0.2 },
      }}
      whileTap={{ scale: 0.98 }}
    >
      <Card
        sx={{
          height: '100%',
          background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
          border: '1px solid',
          borderColor: 'divider',
          cursor: 'pointer',
          '&:hover': {
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
            borderColor: `${color}.main`,
          },
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar
              sx={{
                bgcolor: `${color}.main`,
                width: 48,
                height: 48,
                mr: 2,
              }}
            >
              {icon}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ fontWeight: 500, mb: 0.5 }}
              >
                {title}
              </Typography>
              {loading ? (
                <motion.div
                  animate={{ opacity: [0.3, 1, 0.3] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <Box
                    sx={{
                      height: 32,
                      bgcolor: 'grey.200',
                      borderRadius: 1,
                    }}
                  />
                </motion.div>
              ) : (
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 700,
                    color: 'text.primary',
                    lineHeight: 1,
                  }}
                >
                  {typeof value === 'number' ? (
                    <motion.span>{displayValue}</motion.span>
                  ) : (
                    value
                  )}
                </Typography>
              )}
            </Box>
          </Box>

          {change && !loading && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              <Chip
                label={`${getChangeSymbol(change.type)}${Math.abs(change.value)}%`}
                color={getChangeColor(change.type)}
                size="small"
                sx={{
                  fontWeight: 600,
                  fontSize: '0.75rem',
                }}
              />
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{ fontWeight: 500 }}
              >
                {change.period}
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};
