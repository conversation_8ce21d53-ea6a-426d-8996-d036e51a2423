import React, { useEffect, ReactNode } from 'react';
import {
  useAuthStore,
  useAuthInitialized,
  useAuthStatus,
} from '../stores/useAuthStore';
import { Box, CircularProgress, Typography, Button } from '@mui/material';

interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Componente que inicializa la autenticación y maneja el estado de carga
 * Debe envolver toda la aplicación para asegurar que la autenticación esté inicializada
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const initialized = useAuthInitialized();
  const status = useAuthStatus();
  const { initialize } = useAuthStore();

  const isLoading = status === 'loading';

  // Inicializar solo una vez
  useEffect(() => {
    if (!initialized) {
      initialize();
    }
  }, []); // Solo se ejecuta una vez al montar

  // Mostrar pantalla de carga mientras se inicializa la autenticación
  if (!initialized || isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          backgroundColor: '#f5f5f5',
        }}
      >
        <CircularProgress size={60} sx={{ mb: 2, color: '#1976d2' }} />
        <Typography variant="h6" sx={{ color: '#666' }}>
          Inicializando aplicación...
        </Typography>
        <Typography variant="body2" sx={{ color: '#999', mt: 1 }}>
          {status === 'failed' && error?.includes('Timeout')
            ? 'Verificando conexión...'
            : 'Cargando configuración...'}
        </Typography>
        {status === 'failed' && error?.includes('Timeout') && (
          <>
            <Typography variant="caption" color="warning.main" sx={{ mt: 2, textAlign: 'center', maxWidth: 400 }}>
              Si este mensaje persiste, verifique su conexión a internet.
            </Typography>
            <Button
              variant="contained"
              onClick={() => window.location.reload()}
              sx={{ mt: 2 }}
            >
              Reintentar
            </Button>
          </>
        )}
      </Box>
    );
  }

  return <>{children}</>;
};
