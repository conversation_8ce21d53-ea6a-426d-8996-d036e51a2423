import React from 'react';
import {
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Divider,
  Chip,
  Box,
  Typography,
  Alert,
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  Description as ReportsIcon,
  Quiz as QuizIcon,
  AdminPanelSettings as AdminIcon,
  Settings as SettingsIcon,
  Help as HelpIcon,
  Person as PersonIcon,
  Logout as LogoutIcon,
  BarChart as ChartsIcon,
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import { UserRole } from '../../services/supabaseAuth';

interface NavigationItem {
  path: string;
  label: string;
  icon: React.ReactNode;
  allowedRoles: UserRole[];
  dividerAfter?: boolean;
}

const navigationItems: NavigationItem[] = [
  {
    path: '/dashboard',
    label: 'Inicio',
    icon: <DashboardIcon />,
    allowedRoles: ['administrador', 'psicologo', 'paciente'],
  },



  // Sección de Trabajo Clínico (administrador y psicólogo)
  {
    path: '/pacientes-avanzado',
    label: 'Pacientes',
    icon: <PeopleIcon />,
    allowedRoles: ['administrador', 'psicologo'],
  },
  {
    path: '/cuestionario',
    label: 'Cuestionario',
    icon: <QuizIcon />,
    allowedRoles: ['administrador', 'psicologo', 'paciente'],
  },
  {
    path: '/respuestas-cuestionario',
    label: 'Respuestas',
    icon: <AssessmentIcon />,
    allowedRoles: ['administrador', 'psicologo'],
  },
  {
    path: '/resultados',
    label: 'Resultados',
    icon: <AssessmentIcon />,
    allowedRoles: ['administrador', 'psicologo'],
  },
  {
    path: '/informes',
    label: 'Informes',
    icon: <ReportsIcon />,
    allowedRoles: ['administrador', 'psicologo'],
  },
  {
    path: '/graficos',
    label: 'Dashboard (Centro de Análisis Comparativo)',
    icon: <ChartsIcon />,
    allowedRoles: ['administrador', 'psicologo'],
    dividerAfter: true,
  },

  // Sección de Paciente (solo para pacientes)
  {
    path: '/respuestas',
    label: 'Mis Respuestas',
    icon: <AssessmentIcon />,
    allowedRoles: ['paciente'],
    dividerAfter: true,
  },

  // Sección General (todos)
  {
    path: '/perfil',
    label: 'Mi Perfil',
    icon: <PersonIcon />,
    allowedRoles: ['administrador', 'psicologo', 'paciente'],
  },
  {
    path: '/administracion',
    label: 'Administración',
    icon: <AdminIcon />,
    allowedRoles: ['administrador'],
  },
  {
    path: '/configuracion',
    label: 'Configuración',
    icon: <SettingsIcon />,
    allowedRoles: ['administrador'],
  },
  {
    path: '/ayuda',
    label: 'Ayuda',
    icon: <HelpIcon />,
    allowedRoles: ['administrador', 'psicologo', 'paciente'],
  },
];

const roleLabels = {
  administrador: 'Administrador',
  psicologo: 'Psicólogo',
  paciente: 'Paciente',
};

const roleColors = {
  administrador: 'error',
  psicologo: 'primary',
  paciente: 'success',
} as const;

export const RoleBasedNavigation: React.FC = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  if (!user) {
    return null;
  }

  // SEGURIDAD: Si no hay rol, no mostrar navegación
  if (!user.role) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Alert severity="warning">
          <Typography variant="body2">
            Cuenta sin rol asignado. Contacta al administrador.
          </Typography>
        </Alert>
      </Box>
    );
  }

  // Filtrar elementos de navegación según el rol del usuario
  const allowedItems = navigationItems.filter((item) =>
    item.allowedRoles.includes(user.role),
  );

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <Box sx={{ height: '100%', backgroundColor: '#1e3a8a' }}>
      {/* Información del usuario */}
      <Box
        sx={{
          p: 3,
          backgroundColor: '#011129', // Cambiar a azul del menú
          color: 'white',
          textAlign: 'center',
        }}
      >
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 1 }}>
          MACI-II
        </Typography>
        <Typography variant="body1" sx={{ fontWeight: 500, mb: 1 }}>
          {user.name}
        </Typography>
        <Chip
          label={user.role ? roleLabels[user.role] : 'Sin rol'}
          sx={{
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            color: 'white',
            fontWeight: 500,
            mb: 1,
            '& .MuiChip-label': {
              px: 1.5,
            },
          }}
          size="small"
        />
        <Typography variant="body2" sx={{ opacity: 0.8 }}>
          {user.email}
        </Typography>
      </Box>

      {/* Navegación */}
      <Box
        sx={{
          backgroundColor: '#011129',
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <List
          sx={{
            py: 2,
            flex: 1,
          }}
        >
          {allowedItems.map((item) => (
            <React.Fragment key={item.path}>
              <ListItem disablePadding>
                <ListItemButton
                  selected={location.pathname === item.path}
                  onClick={() => handleNavigation(item.path)}
                  sx={{
                    borderRadius: 2,
                    mx: 2,
                    mb: 1,
                    color: 'rgba(255, 255, 255, 0.8)',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                      color: 'white',
                      transform: 'translateX(4px)',
                      '& .MuiListItemIcon-root': {
                        color: 'white',
                      },
                    },
                    '&.Mui-selected': {
                      backgroundColor: '#e6c012',
                      color: '#011129',
                      transform: 'translateX(8px)',
                      boxShadow: '0 2px 8px rgba(230, 192, 18, 0.3)',
                      '&:hover': {
                        backgroundColor: '#d4a610',
                        transform: 'translateX(8px)',
                      },
                      '& .MuiListItemIcon-root': {
                        color: '#011129',
                      },
                      '& .MuiListItemText-primary': {
                        fontWeight: 'bold',
                      },
                    },
                    '& .MuiListItemIcon-root': {
                      transition: 'color 0.3s ease-in-out',
                      color: 'rgba(255, 255, 255, 0.7)',
                      minWidth: 40,
                    },
                    '& .MuiListItemText-primary': {
                      fontWeight: location.pathname === item.path ? 600 : 500,
                      transition: 'font-weight 0.3s ease-in-out',
                      fontSize: '0.95rem',
                    },
                  }}
                >
                  <ListItemIcon>{item.icon}</ListItemIcon>
                  <ListItemText primary={item.label} />
                </ListItemButton>
              </ListItem>
              {item.dividerAfter && <Divider sx={{ my: 1 }} />}
            </React.Fragment>
          ))}
        </List>

        {/* Botón de Cerrar Sesión - Movido más arriba */}
        <Box
          sx={{
            p: 2,
            borderTop: '1px solid rgba(255, 255, 255, 0.1)',
            mt: 'auto', // Esto lo empuja hacia abajo pero no al final absoluto
          }}
        >
          <ListItem disablePadding>
            <ListItemButton
              onClick={async () => {
                try {
                  await signOut();
                  navigate('/login');
                } catch (error) {
                  console.error('Error al cerrar sesión:', error);
                }
              }}
              sx={{
                borderRadius: 2,
                backgroundColor: '#d32f2f',
                color: 'white',
                transition: 'all 0.3s ease',
                '&:hover': {
                  backgroundColor: '#b71c1c',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 8px rgba(211, 47, 47, 0.3)',
                },
                '& .MuiListItemIcon-root': {
                  color: 'white',
                  minWidth: 40,
                },
                '& .MuiListItemText-primary': {
                  fontWeight: 'bold',
                  fontSize: '0.9rem',
                },
              }}
            >
              <ListItemIcon>
                <LogoutIcon />
              </ListItemIcon>
              <ListItemText primary="Cerrar Sesión" />
            </ListItemButton>
          </ListItem>
        </Box>
      </Box>
    </Box>
  );
};

/**
 * Hook para obtener elementos de navegación permitidos para el usuario actual
 */
export const useNavigationItems = () => {
  const { user } = useAuth();

  if (!user || !user.role) {
    return [];
  }

  return navigationItems.filter((item) =>
    item.allowedRoles.includes(user.role!),
  );
};

/**
 * Hook para verificar si una ruta está disponible para el usuario actual
 */
export const useRouteAvailability = (path: string) => {
  const { user } = useAuth();

  if (!user || !user.role) {
    return false;
  }

  const item = navigationItems.find((item) => item.path === path);
  return item ? item.allowedRoles.includes(user.role!) : false;
};
