import React, { useState, useEffect } from 'react';
import { FaLock, FaUnlock, FaShieldAlt, FaRoute, FaUsers, FaEdit, FaSave, FaTimes, FaRoute } from 'react-icons/fa';
import { toast } from 'react-toastify';

const PageAccessPanel = () => {
  const [routes, setRoutes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingRoute, setEditingRoute] = useState(null);
  const [newRoute, setNewRoute] = useState({
    route_path: '',
    required_permission: 'read',
    required_role: 'paciente',
    description: ''
  });
  const [showAddForm, setShowAddForm] = useState(false);

  const roles = [
    { value: 'administrador', label: 'Administrador', color: 'bg-red-100 text-red-800' },
    { value: 'psicologo', label: 'Psicólogo', color: 'bg-blue-100 text-blue-800' },
    { value: 'paciente', label: 'Paciente', color: 'bg-green-100 text-green-800' }
  ];

  const permissions = [
    { value: 'read', label: 'Lectura', icon: FaUnlock },
    { value: 'write', label: 'Escritura', icon: FaEdit },
    { value: 'admin', label: 'Administración', icon: FaShieldAlt },
    { value: 'access', label: 'Acceso', icon: FaLock }
  ];

  useEffect(() => {
    loadRoutes();
  }, []);

  const loadRoutes = async () => {
    try {
      setLoading(true);
      
      // Por ahora usamos rutas por defecto
      console.log('🔍 Cargando configuración de rutas...');
      setRoutes(getDefaultRoutes());
      console.log('✅ Rutas cargadas correctamente');
    } catch (error) {
      console.error('Error loading routes:', error);
      // Usar rutas por defecto en caso de error
      setRoutes(getDefaultRoutes());
      toast.error('Error al cargar rutas, usando configuración por defecto');
    } finally {
      setLoading(false);
    }
  };

  const getDefaultRoutes = () => [
    {
      id: 1,
      route_path: '/home',
      required_permission: 'access',
      required_role: 'paciente',
      description: 'Página principal'
    },
    {
      id: 2,
      route_path: '/cuestionario',
      required_permission: 'read',
      required_role: 'paciente',
      description: 'Realizar evaluaciones'
    },
    {
      id: 3,
      route_path: '/resultados',
      required_permission: 'read',
      required_role: 'psicologo',
      description: 'Ver resultados de evaluaciones'
    },
    {
      id: 4,
      route_path: '/admin/administration',
      required_permission: 'admin',
      required_role: 'administrador',
      description: 'Panel de administración'
    },
    {
      id: 5,
      route_path: '/configuracion',
      required_permission: 'read',
      required_role: 'administrador',
      description: 'Configuración del sistema'
    },
    {
      id: 6,
      route_path: '/admin/patients',
      required_permission: 'read',
      required_role: 'psicologo',
      description: 'Gestión de pacientes'
    }
  ];

  const handleUpdateRoute = async (routeId, updates) => {
    try {
      // Actualizar en el estado local
      setRoutes(prev => prev.map(route => 
        route.id === routeId ? { ...route, ...updates } : route
      ));
      
      // Intentar actualizar en la base de datos
      const { error } = await supabase
        .from('route_permissions')
        .update(updates)
        .eq('id', routeId);

      if (error && error.code !== '42P01') {
        throw error;
      }

      setEditingRoute(null);
      toast.success('Ruta actualizada exitosamente');
    } catch (error) {
      console.error('Error updating route:', error);
      toast.error('Error al actualizar ruta');
    }
  };

  const handleAddRoute = async () => {
    try {
      const routeData = {
        ...newRoute,
        id: Date.now() // ID temporal para el estado local
      };

      // Agregar al estado local
      setRoutes(prev => [...prev, routeData]);

      // Intentar agregar a la base de datos
      const { error } = await supabase
        .from('route_permissions')
        .insert([newRoute]);

      if (error && error.code !== '42P01') {
        throw error;
      }

      setNewRoute({
        route_path: '',
        required_permission: 'read',
        required_role: 'paciente',
        description: ''
      });
      setShowAddForm(false);
      toast.success('Ruta agregada exitosamente');
    } catch (error) {
      console.error('Error adding route:', error);
      toast.error('Error al agregar ruta');
    }
  };

  const handleDeleteRoute = async (routeId) => {
    if (!window.confirm('¿Estás seguro de que quieres eliminar esta ruta?')) {
      return;
    }

    try {
      // Eliminar del estado local
      setRoutes(prev => prev.filter(route => route.id !== routeId));

      // Intentar eliminar de la base de datos
      const { error } = await supabase
        .from('route_permissions')
        .delete()
        .eq('id', routeId);

      if (error && error.code !== '42P01') {
        throw error;
      }

      toast.success('Ruta eliminada exitosamente');
    } catch (error) {
      console.error('Error deleting route:', error);
      toast.error('Error al eliminar ruta');
    }
  };

  const getRoleInfo = (role) => {
    return roles.find(r => r.value === role) || { label: role, color: 'bg-gray-100 text-gray-800' };
  };

  const getPermissionInfo = (permission) => {
    return permissions.find(p => p.value === permission) || { label: permission, icon: FaLock };
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Control de Acceso</h2>
        <p className="text-gray-600 mt-2">Gestiona permisos de acceso a páginas</p>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Rutas</p>
              <p className="text-3xl font-bold text-gray-900">{routes.length}</p>
            </div>
            <FaRoute className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rutas Públicas</p>
              <p className="text-3xl font-bold text-gray-900">
                {routes.filter(r => r.required_role === 'paciente').length}
              </p>
            </div>
            <FaUnlock className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-red-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rutas Admin</p>
              <p className="text-3xl font-bold text-gray-900">
                {routes.filter(r => r.required_role === 'administrador').length}
              </p>
            </div>
            <FaShieldAlt className="w-8 h-8 text-red-500" />
          </div>
        </div>
      </div>

      {/* Controles */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Rutas del Sistema</h3>
          <button
            onClick={() => setShowAddForm(!showAddForm)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <FaRoute />
            <span>Agregar Ruta</span>
          </button>
        </div>

        {/* Formulario para agregar ruta */}
        {showAddForm && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h4 className="text-md font-medium text-gray-900 mb-4">Nueva Ruta</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <input
                type="text"
                placeholder="Ruta (ej: /nueva-pagina)"
                value={newRoute.route_path}
                onChange={(e) => setNewRoute(prev => ({ ...prev, route_path: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <select
                value={newRoute.required_permission}
                onChange={(e) => setNewRoute(prev => ({ ...prev, required_permission: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {permissions.map(perm => (
                  <option key={perm.value} value={perm.value}>{perm.label}</option>
                ))}
              </select>
              <select
                value={newRoute.required_role}
                onChange={(e) => setNewRoute(prev => ({ ...prev, required_role: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {roles.map(role => (
                  <option key={role.value} value={role.value}>{role.label}</option>
                ))}
              </select>
              <input
                type="text"
                placeholder="Descripción"
                value={newRoute.description}
                onChange={(e) => setNewRoute(prev => ({ ...prev, description: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex space-x-2 mt-4">
              <button
                onClick={handleAddRoute}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <FaSave />
                <span>Guardar</span>
              </button>
              <button
                onClick={() => setShowAddForm(false)}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <FaTimes />
                <span>Cancelar</span>
              </button>
            </div>
          </div>
        )}

        {/* Tabla de rutas */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ruta
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Permiso
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rol Requerido
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Descripción
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {routes.map((route) => (
                <tr key={route.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 font-mono">
                      {route.route_path}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      {React.createElement(getPermissionInfo(route.required_permission).icon, {
                        className: "w-4 h-4 text-gray-600"
                      })}
                      <span className="text-sm text-gray-900">
                        {getPermissionInfo(route.required_permission).label}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleInfo(route.required_role).color}`}>
                      {getRoleInfo(route.required_role).label}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">{route.description}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button
                      onClick={() => setEditingRoute(route)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <FaEdit />
                    </button>
                    <button
                      onClick={() => handleDeleteRoute(route.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <FaTimes />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {routes.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500">No hay rutas configuradas</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PageAccessPanel;
