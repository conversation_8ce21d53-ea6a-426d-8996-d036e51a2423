import React, { useState, useEffect, Suspense } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
} from '@mui/material';

// Lazy loading de componentes de gráficos
const CohortAnalysisChart = React.lazy(() => import('../charts/CohortAnalysisChart'));
const GenderDistributionChart = React.lazy(() => import('../charts/GenderDistributionChart'));
const AgeDistributionChart = React.lazy(() => import('../charts/AgeDistributionChart'));
const TemporalTrendsChart = React.lazy(() => import('../charts/TemporalTrendsChart'));
const MaciRadarChart = React.lazy(() => import('../charts/MaciRadarChart'));

// Importar la función de transformación por separado
import { transformEvaluationToRadarData } from '../charts/MaciRadarChart';
const ProfileComparisonChart = React.lazy(() => import('../charts/ProfileComparisonChart'));
const CacheStatsPanel = React.lazy(() => import('../debug/CacheStatsPanel'));

// Importar skeletons
import ChartSkeleton, { CompactChartSkeleton } from '../common/ChartSkeleton';
import {
  getCohortAnalysis,
  getGeneralStatistics,
  getTemporalTrends,
  transformCohortDataForChart,
  transformHistoryDataForChart
} from '../../services/analyticsService';
import type {
  PatientData,
  CohortFilters,
  GeneralStats,
  TemporalTrendData
} from '../../types/analytics';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { supabase } from '../../lib/supabaseClient';
import { useNotifier } from '../../hooks/useNotifier';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// Interfaces movidas a src/types/analytics.ts para evitar duplicación

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div role="tabpanel" hidden={value !== index} {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// Nombres de escalas MACI-II para visualización
const scaleNames: { [key: string]: string } = {
  // Patrones de Personalidad
  '1': 'Introvertido',
  '2A': 'Inhibido',
  '2B': 'Pesimista',
  '3': 'Sumiso',
  '4': 'Histriónico',
  '5': 'Egocéntrico',
  '6A': 'Rebelde',
  '6B': 'Rudo',
  '7': 'Conformista',
  '8A': 'Oposicionista',
  '8B': 'Autopunitivo',
  '9': 'Tendencia Límite',
  
  // Preocupaciones Expresadas
  'A': 'Difusión de la Identidad',
  'B': 'Desvalorización de Sí Mismo',
  'C': 'Desagrado por el Propio Cuerpo',
  'D': 'Incomodidad Sexual',
  'E': 'Inseguridad con los Pares',
  'F': 'Insensibilidad Social',
  'G': 'Discordia Familiar',
  'H': 'Abuso Infantil',
  
  // Síndromes Clínicos
  'AA': 'Trastorno Alimentario',
  'BB': 'Inclinación al Abuso de Sustancias',
  'CC': 'Predisposición a la Delincuencia',
  'DD': 'Propensión a la Impulsividad',
  'EE': 'Sentimientos de Ansiedad',
  'FF': 'Afecto Depresivo',
  'GG': 'Tendencia al Suicidio',
  
  // Escalas de Validez
  'X': 'Transparencia',
  'Y': 'Deseabilidad',
  'Z': 'Devaluación'
};

export const ComparativeAnalysisDashboard: React.FC = () => {
  const [tabIndex, setTabIndex] = useState(0);
  const [patients, setPatients] = useState<PatientData[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<string>('');
  const [selectedPatientData, setSelectedPatientData] = useState<PatientData | null>(null);
  const [historyData, setHistoryData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingPatients, setLoadingPatients] = useState(true);

  // Estados para datos de análisis
  const [cohortData, setCohortData] = useState<Array<{ name: string; Masculino: number; Femenino: number; Otro: number }>>([]);
  const [generalStats, setGeneralStats] = useState<GeneralStats | null>(null);
  const [temporalData, setTemporalData] = useState<TemporalTrendData[]>([]);
  const [cohortFilters, setCohortFilters] = useState<CohortFilters>({});
  const [loadingAnalytics, setLoadingAnalytics] = useState(false);

  const { showNotification } = useNotifier();

  // Función para cargar datos de cohortes
  const loadCohortData = async (filters: CohortFilters = {}) => {
    try {
      setLoadingAnalytics(true);
      const data = await getCohortAnalysis(filters);
      const chartData = transformCohortDataForChart(data);
      setCohortData(chartData);
    } catch (error) {
      console.error('Error loading cohort data:', error);
      showNotification('Error al cargar datos de cohortes', 'error');
    } finally {
      setLoadingAnalytics(false);
    }
  };

  // Función para cargar estadísticas generales
  const loadGeneralStats = async () => {
    try {
      const stats = await getGeneralStatistics();
      setGeneralStats(stats);
    } catch (error) {
      console.error('Error loading general stats:', error);
      showNotification('Error al cargar estadísticas generales', 'error');
    }
  };

  // Función para cargar tendencias temporales
  const loadTemporalData = async () => {
    try {
      const data = await getTemporalTrends();
      setTemporalData(data);
    } catch (error) {
      console.error('Error loading temporal data:', error);
      showNotification('Error al cargar tendencias temporales', 'error');
    }
  };

  // Cargar la lista de pacientes del psicólogo/admin
  useEffect(() => {
    const fetchPatients = async () => {
      setLoadingPatients(true);
      try {
        const { data, error } = await supabase.rpc('get_psychologist_patients');
        
        if (error) throw error;
        
        setPatients(data || []);
      } catch (error: any) {
        showNotification(`Error al cargar pacientes: ${error.message}`, 'error');
        setPatients([]);
      } finally {
        setLoadingPatients(false);
      }
    };
    
    fetchPatients();
  }, [showNotification]);

  // Cargar datos de análisis cuando cambie la pestaña
  useEffect(() => {
    if (tabIndex === 1) {
      // Pestaña de Análisis de Cohortes
      loadCohortData(cohortFilters);
    } else if (tabIndex === 2) {
      // Pestaña de Estadísticas Generales
      loadGeneralStats();
      loadTemporalData();
    }
  }, [tabIndex, cohortFilters]);

  // Cargar el historial cuando se selecciona un paciente
  useEffect(() => {
    if (!selectedPatient) {
      setHistoryData([]);
      setSelectedPatientData(null);
      return;
    }

    const patient = patients.find(p => p.id === selectedPatient);
    setSelectedPatientData(patient || null);

    const fetchHistory = async () => {
      setLoading(true);
      try {
        const { data, error } = await supabase.rpc('get_patient_evaluations_history', {
          p_patient_id: selectedPatient,
        });

        if (error) throw error;

        if (!data || data.length === 0) {
          setHistoryData([]);
          return;
        }

        // Transformar datos usando la función pura extraída
        const formattedData = transformHistoryDataForChart(data);
        setHistoryData(formattedData);
      } catch (error: any) {
        showNotification(`Error al cargar el historial: ${error.message}`, 'error');
        setHistoryData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchHistory();
  }, [selectedPatient, patients, showNotification]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  };

  const lineColors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#d0ed57', '#a4de6c', '#8dd1e1', '#d084d0'];

  return (
    <>
    <Paper sx={{ width: '100%', mt: 2, boxShadow: 3 }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabIndex} onChange={handleTabChange} aria-label="analysis tabs">
          <Tab label="Evolución del Paciente" />
          <Tab label="Análisis de Cohortes" />
          <Tab label="Estadísticas Generales" />
        </Tabs>
      </Box>

      {/* Panel 1: Análisis Longitudinal */}
      <TabPanel value={tabIndex} index={0}>
        <Typography variant="h5" gutterBottom sx={{ color: '#5A92C8', fontWeight: 600 }}>
          📈 Análisis Longitudinal
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Visualiza la evolución de un paciente a lo largo del tiempo comparando sus diferentes evaluaciones.
        </Typography>

        {loadingPatients ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <FormControl fullWidth sx={{ mb: 3, maxWidth: 500 }}>
            <InputLabel>Seleccionar Paciente</InputLabel>
            <Select
              value={selectedPatient}
              label="Seleccionar Paciente"
              onChange={(e) => setSelectedPatient(e.target.value)}
            >
              {patients.map((p) => (
                <MenuItem key={p.id} value={p.id}>
                  <Box>
                    <Typography variant="body1">{p.full_name}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {p.evaluation_count} evaluación{p.evaluation_count !== 1 ? 'es' : ''} • 
                      Última: {new Date(p.last_evaluation_date).toLocaleDateString('es-ES')}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}

        {/* Información del paciente seleccionado */}
        {selectedPatientData && (
          <Card sx={{ mb: 3, bgcolor: '#f8f9fa' }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={8}>
                  <Typography variant="h6" gutterBottom>
                    {selectedPatientData.full_name}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Chip 
                      label={`${selectedPatientData.evaluation_count} evaluación${selectedPatientData.evaluation_count !== 1 ? 'es' : ''}`} 
                      size="small" 
                      color="primary" 
                    />
                    <Chip 
                      label={selectedPatientData.gender === 'M' ? 'Masculino' : 'Femenino'} 
                      size="small" 
                      variant="outlined" 
                    />
                    <Chip 
                      label={`Última evaluación: ${new Date(selectedPatientData.last_evaluation_date).toLocaleDateString('es-ES')}`} 
                      size="small" 
                      variant="outlined" 
                    />
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        )}

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {!loading && !selectedPatient && !loadingPatients && (
          <Alert severity="info" sx={{ my: 3 }}>
            Por favor, selecciona un paciente para ver su evolución temporal.
          </Alert>
        )}

        {!loading && selectedPatient && historyData.length === 0 && (
          <Alert severity="warning" sx={{ my: 3 }}>
            No se encontraron evaluaciones históricas para este paciente.
          </Alert>
        )}

        {!loading && historyData.length > 0 && (
          <Box>
            <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }}>
              Evolución de Escalas Principales
            </Typography>
            <Box sx={{ height: 500, mb: 4 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={historyData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis 
                    domain={[0, 115]} 
                    label={{ value: 'Puntuación Percentil', angle: -90, position: 'insideLeft' }} 
                  />
                  <Tooltip 
                    labelFormatter={(value) => `Fecha: ${value}`}
                    formatter={(value: any, name: string) => [
                      `${value}`, 
                      scaleNames[name] || name
                    ]}
                  />
                  <Legend 
                    wrapperStyle={{ paddingTop: '20px' }}
                    formatter={(value) => scaleNames[value] || value}
                  />
                  {Object.keys(historyData[0] || {})
                    .filter((key) => key !== 'date')
                    .map((key, index) => (
                      <Line
                        key={key}
                        type="monotone"
                        dataKey={key}
                        name={key}
                        stroke={lineColors[index % lineColors.length]}
                        strokeWidth={3}
                        activeDot={{ r: 6 }}
                        connectNulls={false}
                      />
                    ))}
                </LineChart>
              </ResponsiveContainer>
            </Box>

            {/* Gráfico de Radar para Perfil Actual */}
            {historyData.length > 0 && (
              <Grid container spacing={3} sx={{ mt: 2 }}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom sx={{ color: '#5A92C8' }}>
                        🎯 Perfil Actual (Radar)
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Visualización del perfil psicológico más reciente
                      </Typography>
                      <Suspense fallback={<ChartSkeleton variant="radar" height={300} />}>
                        <MaciRadarChart
                          data={transformEvaluationToRadarData(
                            // Simular datos de la evaluación más reciente
                            {
                              personalityPatterns: Object.keys(historyData[historyData.length - 1] || {})
                                .filter(key => key !== 'date')
                                .reduce((acc, key) => ({
                                  ...acc,
                                  [key]: { pc: historyData[historyData.length - 1][key] }
                                }), {})
                            }
                          )}
                          height={300}
                          title="Perfil Actual"
                        />
                      </Suspense>
                    </CardContent>
                  </Card>
                </Grid>

                {historyData.length > 1 && (
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom sx={{ color: '#5A92C8' }}>
                          📈 Comparación Temporal
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          Comparación entre primera y última evaluación
                        </Typography>
                        <Suspense fallback={<ChartSkeleton variant="radar" height={300} />}>
                          <ProfileComparisonChart
                            data={Object.keys(historyData[0] || {})
                              .filter(key => key !== 'date')
                              .map(key => ({
                                scale: key,
                                scaleName: scaleNames[key] || key,
                                evaluation1: historyData[0][key] as number,
                                evaluation2: historyData[historyData.length - 1][key] as number,
                                fullMark: 100
                              }))}
                            height={300}
                            evaluation1Label="Primera Evaluación"
                            evaluation2Label="Última Evaluación"
                          />
                        </Suspense>
                      </CardContent>
                    </Card>
                  </Grid>
                )}
              </Grid>
            )}

            {/* Información adicional sobre las escalas */}
            <Card sx={{ mt: 3, bgcolor: '#f0f7ff' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: '#5A92C8' }}>
                  📊 Interpretación de Escalas
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  <strong>Puntuaciones PC (Percentiles):</strong> 0-74 (Normal), 75-84 (Elevado), 85+ (Muy Elevado)
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {Object.keys(historyData[0] || {})
                    .filter((key) => key !== 'date')
                    .map((key, index) => (
                      <Chip
                        key={key}
                        label={scaleNames[key] || key}
                        size="small"
                        sx={{
                          backgroundColor: lineColors[index % lineColors.length],
                          color: 'white',
                          '&:hover': {
                            backgroundColor: lineColors[index % lineColors.length],
                            opacity: 0.8
                          }
                        }}
                      />
                    ))}
                </Box>
              </CardContent>
            </Card>
          </Box>
        )}
      </TabPanel>

      {/* Panel 2: Análisis de Cohortes */}
      <TabPanel value={tabIndex} index={1}>
        <Typography variant="h5" gutterBottom sx={{ color: '#5A92C8', fontWeight: 600 }}>
          👥 Análisis de Cohortes
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Compara grupos de pacientes por características demográficas y resultados de evaluaciones.
        </Typography>

        <Grid container spacing={3}>
          {/* Filtros de Cohorte */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Filtros de Cohorte
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Rango de Edad</InputLabel>
                    <Select
                      value={cohortFilters.minAge ? `${cohortFilters.minAge}-${cohortFilters.maxAge}` : ""}
                      label="Rango de Edad"
                      onChange={(e) => {
                        const value = e.target.value as string;
                        if (value === "") {
                          setCohortFilters(prev => ({ ...prev, minAge: null, maxAge: null }));
                        } else {
                          const [min, max] = value.split('-').map(Number);
                          setCohortFilters(prev => ({ ...prev, minAge: min, maxAge: max }));
                        }
                      }}
                    >
                      <MenuItem value="">Todas las edades</MenuItem>
                      <MenuItem value="12-15">12-15 años</MenuItem>
                      <MenuItem value="16-18">16-18 años</MenuItem>
                      <MenuItem value="19-25">19-25 años</MenuItem>
                      <MenuItem value="26-100">26+ años</MenuItem>
                    </Select>
                  </FormControl>
                  <FormControl fullWidth size="small">
                    <InputLabel>Género</InputLabel>
                    <Select
                      value={cohortFilters.gender || ""}
                      label="Género"
                      onChange={(e) => {
                        const value = e.target.value as string;
                        setCohortFilters(prev => ({
                          ...prev,
                          gender: value === "" ? null : value
                        }));
                      }}
                    >
                      <MenuItem value="">Todos</MenuItem>
                      <MenuItem value="Masculino">Masculino</MenuItem>
                      <MenuItem value="Femenino">Femenino</MenuItem>
                    </Select>
                  </FormControl>
                  <Button
                    variant="contained"
                    sx={{ mt: 1 }}
                    onClick={() => loadCohortData(cohortFilters)}
                    disabled={loadingAnalytics}
                  >
                    {loadingAnalytics ? <CircularProgress size={20} /> : 'Aplicar Filtros'}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Gráfico de Comparación */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Comparación por Cohortes
                </Typography>
                {loadingAnalytics ? (
                  <ChartSkeleton variant="bar" height={300} />
                ) : (
                  <Suspense fallback={<ChartSkeleton variant="bar" height={300} />}>
                    <CohortAnalysisChart data={cohortData} height={300} />
                  </Suspense>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Estadísticas de Cohorte */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Estadísticas por Cohorte
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                      <Typography variant="h4" color="primary">0</Typography>
                      <Typography variant="body2">Cohorte A</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                      <Typography variant="h4" color="primary">0</Typography>
                      <Typography variant="body2">Cohorte B</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                      <Typography variant="h4" color="primary">0</Typography>
                      <Typography variant="body2">Diferencia Media</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                      <Typography variant="h4" color="primary">0%</Typography>
                      <Typography variant="body2">Significancia</Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
      
      {/* Panel 3: Estadísticas Generales */}
      <TabPanel value={tabIndex} index={2}>
        <Typography variant="h5" gutterBottom sx={{ color: '#5A92C8', fontWeight: 600 }}>
          📊 Estadísticas Generales
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Vista panorámica de tendencias y patrones en toda la población de pacientes evaluados.
        </Typography>

        <Grid container spacing={3}>
          {/* Métricas Generales */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Resumen General
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e3f2fd', borderRadius: 1 }}>
                      <Typography variant="h3" color="primary" sx={{ fontWeight: 'bold' }}>
                        {generalStats?.totalPatients || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Pacientes
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f3e5f5', borderRadius: 1 }}>
                      <Typography variant="h3" color="secondary" sx={{ fontWeight: 'bold' }}>
                        {generalStats?.totalEvaluations || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Evaluaciones Completadas
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>
                      <Typography variant="h3" sx={{ color: '#2e7d32', fontWeight: 'bold' }}>
                        {generalStats?.completionRate || 0}%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Tasa de Finalización
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#fff3e0', borderRadius: 1 }}>
                      <Typography variant="h3" sx={{ color: '#f57c00', fontWeight: 'bold' }}>
                        {generalStats?.avgDays || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Promedio Días
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Distribución por Género */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Distribución por Género
                </Typography>
                <Suspense fallback={<ChartSkeleton height={250} variant="pie" />}>
                  <GenderDistributionChart
                    data={generalStats?.genderDistribution}
                    height={250}
                  />
                </Suspense>
              </CardContent>
            </Card>
          </Grid>

          {/* Distribución por Edad */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Distribución por Edad
                </Typography>
                <Suspense fallback={<ChartSkeleton variant="bar" height={250} />}>
                  <AgeDistributionChart
                    data={generalStats?.ageDistribution}
                    height={250}
                  />
                </Suspense>
              </CardContent>
            </Card>
          </Grid>

          {/* Tendencias Temporales */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Tendencias Temporales
                </Typography>
                <Suspense fallback={<ChartSkeleton variant="line" height={300} />}>
                  <TemporalTrendsChart
                    data={temporalData}
                    height={300}
                    chartType="area"
                  />
                </Suspense>
              </CardContent>
            </Card>
          </Grid>

          {/* Top Escalas */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Escalas con Mayor Puntuación
                </Typography>
                <Box sx={{ p: 2 }}>
                  {['Ansiedad', 'Depresión', 'Autoestima', 'Impulsividad', 'Conformidad'].map((escala) => (
                    <Box key={escala} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">{escala}</Typography>
                      <Typography variant="body2" color="primary">
                        {Math.floor(Math.random() * 100)}%
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Alertas y Recomendaciones */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Alertas y Recomendaciones
                </Typography>
                <Box sx={{ p: 2 }}>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Se recomienda revisar pacientes con puntuaciones altas en ansiedad
                  </Alert>
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    3 pacientes requieren seguimiento prioritario
                  </Alert>
                  <Alert severity="success">
                    Tendencia positiva en evaluaciones completadas este mes
                  </Alert>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Paper>

    {/* Panel de estadísticas del caché (solo en desarrollo) */}
    <Suspense fallback={null}>
      <CacheStatsPanel show={process.env.NODE_ENV === 'development'} />
    </Suspense>
    </>
  );
};
