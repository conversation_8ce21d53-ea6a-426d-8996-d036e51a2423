import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Switch,
  FormControlLabel,
  <PERSON><PERSON>,
  <PERSON>,
  Stack,
} from '@mui/material';
import {
  useTheme,
  useNotify,
  useNotifications,
  useUIStore,
  useQuestionnaireStore,
  useQuestionnaireAnswers,
  useQuestionnaireCompletion,
  usePatientsStore,
  usePatientsItems,
  useSelectedPatient,
} from '../../stores';

/**
 * Componente de ejemplo que demuestra el uso de los stores de Zustand
 * Este componente muestra cómo usar los diferentes stores y sus funcionalidades
 */
const ZustandExample: React.FC = () => {
  const theme = useTheme();
  const notify = useNotify();
  const notifications = useNotifications();
  const { toggleTheme, removeNotification } = useUIStore();

  // Questionnaire store
  const { setAnswer, resetAnswers } = useQuestionnaireStore();
  const answers = useQuestionnaireAnswers();
  const isCompleted = useQuestionnaireCompletion();

  // Patients store
  const { fetchPatients } = usePatientsStore();
  const patients = usePatientsItems();
  const selectedPatient = useSelectedPatient();

  const handleTestNotifications = () => {
    notify.success('¡Operación exitosa!');
    setTimeout(() => notify.warning('Advertencia de prueba'), 1000);
    setTimeout(() => notify.error('Error de prueba'), 2000);
    setTimeout(() => notify.info('Información de prueba'), 3000);
  };

  const handleTestQuestionnaire = () => {
    // Simular respuestas aleatorias
    for (let i = 1; i <= 5; i++) {
      const randomValue = Math.floor(Math.random() * 5) + 1;
      setAnswer(i, randomValue);
    }
    notify.success('Se agregaron 5 respuestas aleatorias');
  };

  const handleResetQuestionnaire = () => {
    resetAnswers();
    notify.info('Cuestionario reiniciado');
  };

  const handleFetchPatients = async () => {
    try {
      await fetchPatients();
      notify.success('Pacientes cargados exitosamente');
    } catch (error) {
      notify.error('Error al cargar pacientes');
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Ejemplo de Stores Zustand
      </Typography>

      <Typography variant="body1" sx={{ mb: 3 }}>
        Este componente demuestra el uso de los diferentes stores de Zustand
        implementados.
      </Typography>

      {/* UI Store Example */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            UI Store - Tema y Notificaciones
          </Typography>

          <Stack spacing={2}>
            <FormControlLabel
              control={
                <Switch checked={theme === 'dark'} onChange={toggleTheme} />
              }
              label={`Tema actual: ${theme}`}
            />

            <Button
              variant="contained"
              onClick={handleTestNotifications}
              sx={{ alignSelf: 'flex-start' }}
            >
              Probar Notificaciones
            </Button>

            {notifications.length > 0 && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Notificaciones activas:
                </Typography>
                <Stack spacing={1}>
                  {notifications.map((notification) => (
                    <Alert
                      key={notification.id}
                      severity={notification.type}
                      onClose={() => removeNotification(notification.id)}
                    >
                      {notification.message}
                    </Alert>
                  ))}
                </Stack>
              </Box>
            )}
          </Stack>
        </CardContent>
      </Card>

      {/* Questionnaire Store Example */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Questionnaire Store
          </Typography>

          <Stack spacing={2}>
            <Box>
              <Typography variant="body2">
                Respuestas completadas:{' '}
                {answers.filter((a) => a !== null).length} / {answers.length}
              </Typography>
              <Typography variant="body2">
                Estado: {isCompleted ? 'Completado' : 'En progreso'}
              </Typography>
            </Box>

            <Stack direction="row" spacing={2}>
              <Button variant="contained" onClick={handleTestQuestionnaire}>
                Agregar Respuestas de Prueba
              </Button>

              <Button variant="outlined" onClick={handleResetQuestionnaire}>
                Reiniciar Cuestionario
              </Button>
            </Stack>

            {answers.slice(0, 10).some((a) => a !== null) && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Primeras 10 respuestas:
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap">
                  {answers.slice(0, 10).map((answer, index) => (
                    <Chip
                      key={index}
                      label={`${index + 1}: ${answer || 'N/A'}`}
                      color={answer ? 'primary' : 'default'}
                      size="small"
                    />
                  ))}
                </Stack>
              </Box>
            )}
          </Stack>
        </CardContent>
      </Card>

      {/* Patients Store Example */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Patients Store
          </Typography>

          <Stack spacing={2}>
            <Box>
              <Typography variant="body2">
                Pacientes cargados: {patients.length}
              </Typography>
              {selectedPatient && (
                <Typography variant="body2">
                  Paciente seleccionado: {selectedPatient.name}
                </Typography>
              )}
            </Box>

            <Button
              variant="contained"
              onClick={handleFetchPatients}
              sx={{ alignSelf: 'flex-start' }}
            >
              Cargar Pacientes
            </Button>

            {patients.length > 0 && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Pacientes:
                </Typography>
                <Stack spacing={1}>
                  {patients.slice(0, 5).map((patient) => (
                    <Chip
                      key={patient.id}
                      label={`${patient.name} (${patient.age} años)`}
                      variant="outlined"
                    />
                  ))}
                  {patients.length > 5 && (
                    <Typography variant="caption">
                      ... y {patients.length - 5} más
                    </Typography>
                  )}
                </Stack>
              </Box>
            )}
          </Stack>
        </CardContent>
      </Card>

      <Alert severity="info">
        <Typography variant="body2">
          <strong>Nota:</strong> Este componente es solo para demostración. Los
          stores de Zustand proporcionan una API más simple y mejor rendimiento
          comparado con Redux.
        </Typography>
      </Alert>
    </Box>
  );
};

export default ZustandExample;
