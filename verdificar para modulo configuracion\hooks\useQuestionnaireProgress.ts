import { useState, useCallback, useEffect } from 'react';
import { Question } from '../types/questions';
import { useLocalStorage } from './useLocalStorage';

interface QuestionnaireProgress {
  answers: Question[];
  currentQuestion: number;
  totalQuestions: number;
  progress: number;
}

export const useQuestionnaireProgress = (initialQuestions: Question[]) => {
  const [savedProgress, setSavedProgress] = useLocalStorage<Question[]>(
    'questionnaire_progress',
    initialQuestions,
  );

  const [progress, setProgress] = useState<QuestionnaireProgress>({
    answers: savedProgress,
    currentQuestion: savedProgress.findIndex((q) => q.answer === null),
    totalQuestions: initialQuestions.length,
    progress:
      (savedProgress.filter((q) => q.answer !== null).length /
        initialQuestions.length) *
      100,
  });

  const updateAnswer = useCallback((questionId: number, answer: boolean) => {
    setProgress((prev) => {
      const newAnswers = prev.answers.map((q) =>
        q.id === questionId ? { ...q, answer } : q,
      );

      return {
        answers: newAnswers,
        currentQuestion: newAnswers.findIndex((q) => q.answer === null),
        totalQuestions: prev.totalQuestions,
        progress:
          (newAnswers.filter((q) => q.answer !== null).length /
            prev.totalQuestions) *
          100,
      };
    });
  }, []);

  useEffect(() => {
    setSavedProgress(progress.answers);
  }, [progress.answers, setSavedProgress]);

  const resetProgress = useCallback(() => {
    const resetAnswers = initialQuestions.map((q) => ({ ...q, answer: null }));
    setProgress({
      answers: resetAnswers,
      currentQuestion: 0,
      totalQuestions: initialQuestions.length,
      progress: 0,
    });
  }, [initialQuestions]);

  return {
    ...progress,
    updateAnswer,
    resetProgress,
    isComplete: progress.progress === 100,
  };
};
