import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Box,
  Chip,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  allScaleNames,
  scaleGroups,
  getGrossmanFacets,
  percentileInterpretation
} from '../../data/maciScalesShared';

interface ScoreData {
  raw_scores: { [key: string]: number };
  percentile_scores: { [key: string]: number };
}

interface DetailedScoresTableProps {
  score: ScoreData;
}



const ScoreRow: React.FC<{ scaleKey: string; score: ScoreData }> = ({ scaleKey, score }) => (
  <TableRow>
    <TableCell>{`${scaleKey} - ${allScaleNames[scaleKey]}`}</TableCell>
    <TableCell align="right">{score.raw_scores[scaleKey] ?? 'N/A'}</TableCell>
    <TableCell align="right">
      <Chip
        label={score.percentile_scores[scaleKey] ?? 'N/A'}
        color={
          percentileInterpretation.getClinicalLevel(score.percentile_scores[scaleKey] ?? 0) === 'muy_elevado' ? 'error' :
          percentileInterpretation.getClinicalLevel(score.percentile_scores[scaleKey] ?? 0) === 'elevado' ? 'warning' :
          'default'
        }
      />
    </TableCell>
  </TableRow>
);

const DetailedScoresTable: React.FC<DetailedScoresTableProps> = ({ score }) => {
  if (!score) return null;

  return (
    <TableContainer component={Paper}>
      <Table aria-label="detailed scores table">
        <TableHead>
          <TableRow>
            <TableCell><Typography fontWeight="bold">Escala</Typography></TableCell>
            <TableCell align="right"><Typography fontWeight="bold">Puntaje Bruto</Typography></TableCell>
            <TableCell align="right"><Typography fontWeight="bold">Puntaje Centil (PC)</Typography></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {/* Patrones de Personalidad con Facetas de Grossman */}
          <TableRow>
            <TableCell colSpan={3} sx={{ backgroundColor: 'grey.100' }}>
              <Typography variant="subtitle1" fontWeight="bold">Patrones de Personalidad</Typography>
            </TableCell>
          </TableRow>
          {scaleGroups.personality.map(key => (
            <React.Fragment key={key}>
              <TableRow>
                <TableCell colSpan={3} sx={{ p: 0, border: 'none' }}>
                  <Accordion elevation={0} square sx={{ '&.Mui-expanded': { margin: 0 } }}>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Box display="flex" justifyContent="space-between" width="100%" alignItems="center">
                        <Typography>{`${key} - ${allScaleNames[key]}`}</Typography>
                        <Box>
                          <Chip
                            label={`PC: ${score.percentile_scores[key] ?? 'N/A'}`}
                            size="small"
                            color={
                              percentileInterpretation.getClinicalLevel(score.percentile_scores[key] ?? 0) === 'muy_elevado' ? 'error' :
                              percentileInterpretation.getClinicalLevel(score.percentile_scores[key] ?? 0) === 'elevado' ? 'warning' :
                              'default'
                            }
                          />
                        </Box>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails sx={{ p: 0 }}>
                      <Table size="small">
                        <TableBody>
                          {getGrossmanFacets(key).map(facetKey => (
                            <TableRow key={facetKey}>
                              <TableCell sx={{ pl: 4 }}>{`${facetKey} - ${allScaleNames[facetKey]}`}</TableCell>
                              <TableCell align="right">{score.raw_scores[facetKey] ?? 'N/A'}</TableCell>
                              <TableCell align="right">{score.percentile_scores[facetKey] ?? 'N/A'}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </AccordionDetails>
                  </Accordion>
                </TableCell>
              </TableRow>
            </React.Fragment>
          ))}

          {/* Preocupaciones Expresadas */}
          <TableRow>
            <TableCell colSpan={3} sx={{ backgroundColor: 'grey.100' }}>
              <Typography variant="subtitle1" fontWeight="bold">Preocupaciones Expresadas</Typography>
            </TableCell>
          </TableRow>
          {scaleGroups.concerns.map(key => <ScoreRow key={key} scaleKey={key} score={score} />)}

          {/* Síndromes Clínicos */}
          <TableRow>
            <TableCell colSpan={3} sx={{ backgroundColor: 'grey.100' }}>
              <Typography variant="subtitle1" fontWeight="bold">Síndromes Clínicos</Typography>
            </TableCell>
          </TableRow>
          {scaleGroups.syndromes.map(key => <ScoreRow key={key} scaleKey={key} score={score} />)}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default DetailedScoresTable;
