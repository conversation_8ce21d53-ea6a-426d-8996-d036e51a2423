import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Alert,
  AlertT<PERSON>le,
  Paper,
  Stack,
  Divider,
  Chip,
} from '@mui/material';
import {
  ErrorOutline as ErrorIcon,
  Refresh as RefreshIcon,
  BugReport as BugIcon,
  Home as HomeIcon,
} from '@mui/icons-material';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('<PERSON><PERSON>rBounda<PERSON> caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Llamar callback personalizado si existe
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Enviar error a servicio de monitoreo (opcional)
    this.logErrorToService(error, errorInfo);
  }

  logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // Aquí podrías enviar el error a un servicio como Sentry, LogRocket, etc.
    console.log('Error logged:', {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    });
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      // Si hay un fallback personalizado, usarlo
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Renderizar UI de error por defecto
      return (
        <Box
          sx={{
            minHeight: '400px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            p: 3,
          }}
        >
          <Paper
            elevation={3}
            sx={{
              p: 4,
              maxWidth: 600,
              width: '100%',
              textAlign: 'center',
            }}
          >
            <Stack spacing={3} alignItems="center">
              <ErrorIcon
                sx={{
                  fontSize: 64,
                  color: 'error.main',
                }}
              />
              
              <Typography variant="h4" color="error" gutterBottom>
                ¡Oops! Algo salió mal
              </Typography>
              
              <Typography variant="body1" color="text.secondary">
                Se ha producido un error inesperado en esta parte de la aplicación.
                Nuestro equipo ha sido notificado automáticamente.
              </Typography>

              <Alert severity="error" sx={{ width: '100%', textAlign: 'left' }}>
                <AlertTitle>Detalles del Error</AlertTitle>
                <Typography variant="body2" component="div">
                  <strong>Mensaje:</strong> {this.state.error?.message}
                </Typography>
                {process.env.NODE_ENV === 'development' && (
                  <Box sx={{ mt: 2 }}>
                    <Chip
                      icon={<BugIcon />}
                      label="Modo Desarrollo"
                      color="warning"
                      size="small"
                      sx={{ mb: 1 }}
                    />
                    <Typography
                      variant="caption"
                      component="pre"
                      sx={{
                        display: 'block',
                        whiteSpace: 'pre-wrap',
                        backgroundColor: 'grey.100',
                        p: 1,
                        borderRadius: 1,
                        fontSize: '0.75rem',
                        maxHeight: 200,
                        overflow: 'auto',
                      }}
                    >
                      {this.state.error?.stack}
                    </Typography>
                  </Box>
                )}
              </Alert>

              <Divider sx={{ width: '100%' }} />

              <Stack direction="row" spacing={2}>
                <Button
                  variant="contained"
                  startIcon={<RefreshIcon />}
                  onClick={this.handleRetry}
                  color="primary"
                >
                  Intentar de Nuevo
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<HomeIcon />}
                  onClick={this.handleGoHome}
                >
                  Ir al Inicio
                </Button>
              </Stack>

              <Typography variant="caption" color="text.secondary">
                Si el problema persiste, contacta al soporte técnico.
              </Typography>
            </Stack>
          </Paper>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;

// Hook para usar en componentes funcionales
export const useErrorHandler = () => {
  return (error: Error, errorInfo?: ErrorInfo) => {
    console.error('Error handled by useErrorHandler:', error, errorInfo);
    
    // Aquí podrías disparar un estado global de error
    // o mostrar una notificación
  };
};

// Componente wrapper para casos específicos
export const DataGridErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={
        <Alert severity="error" sx={{ m: 2 }}>
          <AlertTitle>Error en la Tabla de Datos</AlertTitle>
          <Typography variant="body2">
            No se pudo cargar la tabla de datos. Esto puede deberse a un problema de compatibilidad.
          </Typography>
          <Button
            size="small"
            onClick={() => window.location.reload()}
            sx={{ mt: 1 }}
          >
            Recargar Página
          </Button>
        </Alert>
      }
      onError={(error, errorInfo) => {
        console.error('DataGrid Error:', error, errorInfo);
      }}
    >
      {children}
    </ErrorBoundary>
  );
};
