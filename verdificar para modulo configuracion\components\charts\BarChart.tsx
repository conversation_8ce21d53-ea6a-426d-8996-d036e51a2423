import React from 'react';
import {
  <PERSON><PERSON>hart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { Box, Typography, Paper } from '@mui/material';
import { useTheme } from '@mui/material/styles';

interface BarChartData {
  name: string;
  value: number;
  category?: string;
  color?: string;
}

interface BarChartProps {
  data: BarChartData[];
  title?: string;
  subtitle?: string;
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  orientation?: 'horizontal' | 'vertical';
  colorScheme?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'custom';
  customColors?: string[];
}

export const BarChart: React.FC<BarChartProps> = ({
  data,
  title,
  subtitle,
  height = 300,
  showGrid = true,
  showLegend = false,
  orientation = 'vertical',
  colorScheme = 'primary',
  customColors,
}) => {
  const theme = useTheme();

  // Colores según el esquema de la guía de diseño
  const getColors = () => {
    if (customColors) return customColors;
    
    switch (colorScheme) {
      case 'primary':
        return ['#5A92C8', '#7BA7D1', '#4A7FB5', '#9BBCDA'];
      case 'secondary':
        return ['#63B4A9', '#7FC4BA', '#52A398', '#8BCCC3'];
      case 'success':
        return ['#63B4A9', '#52A398', '#7FC4BA', '#8BCCC3'];
      case 'warning':
        return ['#DDA15E', '#E6B373', '#D6944B', '#EBBF88'];
      case 'error':
        return ['#F28A7C', '#F5A299', '#EF7169', '#F7B5AD'];
      default:
        return ['#5A92C8', '#63B4A9', '#DDA15E', '#F28A7C'];
    }
  };

  const colors = getColors();

  // Configuración del tooltip personalizado
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Paper
          sx={{
            p: 2,
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(0, 0, 0, 0.06)',
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
          }}
        >
          <Typography variant="subtitle2" gutterBottom>
            {label}
          </Typography>
          {payload.map((entry: any, index: number) => (
            <Typography
              key={index}
              variant="body2"
              sx={{ color: entry.color, fontWeight: 500 }}
            >
              Valor: {entry.value}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  return (
    <Box>
      {(title || subtitle) && (
        <Box mb={2}>
          {title && (
            <Typography variant="h6" component="h3" gutterBottom>
              {title}
            </Typography>
          )}
          {subtitle && (
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
      )}
      
      <ResponsiveContainer width="100%" height={height}>
        <RechartsBarChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
          layout={orientation === 'horizontal' ? 'horizontal' : 'vertical'}
        >
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke="rgba(0, 0, 0, 0.06)"
            />
          )}
          
          {orientation === 'horizontal' ? (
            <>
              <XAxis 
                type="number" 
                tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
              />
              <YAxis 
                type="category" 
                dataKey="name"
                tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
                width={80}
              />
            </>
          ) : (
            <>
              <XAxis 
                dataKey="name"
                tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis 
                tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
              />
            </>
          )}
          
          <Tooltip content={<CustomTooltip />} />
          
          {showLegend && <Legend />}
          
          <Bar 
            dataKey="value" 
            fill={colors[0]}
            radius={[4, 4, 0, 0]}
            stroke="rgba(255, 255, 255, 0.8)"
            strokeWidth={1}
          >
            {data.map((entry, index) => (
              <Bar 
                key={`cell-${index}`} 
                fill={entry.color || colors[index % colors.length]} 
              />
            ))}
          </Bar>
        </RechartsBarChart>
      </ResponsiveContainer>
    </Box>
  );
};

export default BarChart;
