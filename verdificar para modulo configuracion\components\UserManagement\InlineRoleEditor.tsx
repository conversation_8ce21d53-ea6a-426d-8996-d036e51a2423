import React, { useState } from 'react';
import {
  Select,
  MenuItem,
  FormControl,
  Chip,
  Box,
  IconButton,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import {
  Edit as EditIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  AdminPanelSettings as AdminIcon,
  Psychology as PsychologyIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { UserRole } from '../../types/user';

interface InlineRoleEditorProps {
  currentRole: UserRole;
  userId: string;
  userName: string;
  onRoleChange: (userId: string, newRole: UserRole) => Promise<void>;
  disabled?: boolean;
}

const roleConfig = {
  administrador: {
    label: 'Administrador',
    color: 'error' as const,
    icon: <AdminIcon />,
    bgColor: '#ffebee',
  },
  psicologo: {
    label: 'Psicólogo',
    color: 'primary' as const,
    icon: <PsychologyIcon />,
    bgColor: '#e3f2fd',
  },
  paciente: {
    label: 'Paciente',
    color: 'secondary' as const,
    icon: <PersonIcon />,
    bgColor: '#f3e5f5',
  },
};

export const InlineRoleEditor: React.FC<InlineRoleEditorProps> = ({
  currentRole,
  userId,
  userName,
  onRoleChange,
  disabled = false,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole>(currentRole);
  const [isLoading, setIsLoading] = useState(false);

  const handleStartEdit = () => {
    setIsEditing(true);
    setSelectedRole(currentRole);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setSelectedRole(currentRole);
  };

  const handleSaveEdit = async () => {
    if (selectedRole === currentRole) {
      setIsEditing(false);
      return;
    }

    setIsLoading(true);
    try {
      await onRoleChange(userId, selectedRole);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating role:', error);
      setSelectedRole(currentRole); // Revertir cambio
    } finally {
      setIsLoading(false);
    }
  };

  const handleRoleChange = (newRole: UserRole) => {
    setSelectedRole(newRole);
  };

  if (isEditing) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 200 }}>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <Select
            value={selectedRole}
            onChange={(e) => handleRoleChange(e.target.value as UserRole)}
            disabled={isLoading}
            autoFocus
          >
            <MenuItem value="administrador">
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AdminIcon fontSize="small" />
                Administrador
              </Box>
            </MenuItem>
            <MenuItem value="psicologo">
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PsychologyIcon fontSize="small" />
                Psicólogo
              </Box>
            </MenuItem>
            <MenuItem value="paciente">
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PersonIcon fontSize="small" />
                Paciente
              </Box>
            </MenuItem>
          </Select>
        </FormControl>

        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <Tooltip title="Guardar cambios">
            <IconButton
              size="small"
              onClick={handleSaveEdit}
              disabled={isLoading}
              color="success"
            >
              {isLoading ? (
                <CircularProgress size={16} />
              ) : (
                <CheckIcon fontSize="small" />
              )}
            </IconButton>
          </Tooltip>

          <Tooltip title="Cancelar">
            <IconButton
              size="small"
              onClick={handleCancelEdit}
              disabled={isLoading}
              color="error"
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
    );
  }

  const config = roleConfig[currentRole];

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <Chip
        icon={config?.icon}
        label={config?.label || currentRole}
        color={config?.color}
        size="small"
        sx={{ fontWeight: 600 }}
      />
      
      {!disabled && (
        <Tooltip title={`Editar rol de ${userName}`}>
          <IconButton
            size="small"
            onClick={handleStartEdit}
            sx={{ 
              opacity: 0.7,
              '&:hover': { 
                opacity: 1,
                backgroundColor: 'action.hover',
              },
            }}
          >
            <EditIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      )}
    </Box>
  );
};
