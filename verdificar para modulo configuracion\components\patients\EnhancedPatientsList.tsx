import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  CircularProgress,
  Typography,
  Pagination,
  Button,
  Avatar,
  Paper,
  Divider,
  Alert,
  Chip,
  IconButton,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  Search as SearchIcon,
  Person as PersonIcon,
  PersonAdd as PersonAddIcon,
  FileDownload as FileDownloadIcon,
  MoreVert as MoreVertIcon,
  Assessment as AssessmentIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { useDebounce } from '../../hooks/useDebounce';
import { patientsService } from '../../services/patients';
import { PatientStatusChip } from './PatientStatusChip';
import { PatientFormDialog } from './PatientFormDialog';
import { ExportDialog } from './ExportDialog';
import { AdvancedFilters, FilterOptions } from './AdvancedFilters';
import { Patient, PatientStatus, PatientStatistics } from '../../types/patient';
import { useNotification } from '../../hooks/useNotification';

interface EnhancedPatientsListProps {
  onPatientSelect?: (patient: Patient) => void;
  onNewEvaluation?: (patientId: string) => void;
  onViewResults?: (patientId: string) => void;
  onEditPatient?: (patient: Patient) => void;
  onCreatePatient?: () => void;
}

export const EnhancedPatientsList: React.FC<EnhancedPatientsListProps> = ({
  onPatientSelect,
  onNewEvaluation,
  onViewResults,
  onEditPatient,
  onCreatePatient,
}) => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [editingPatient, setEditingPatient] = useState<Patient | null>(null);
  const [statusUpdateLoading, setStatusUpdateLoading] = useState<string | null>(null);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [statistics, setStatistics] = useState<PatientStatistics | undefined>();
  const [filters, setFilters] = useState<FilterOptions>({});

  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const { showNotification } = useNotification();

  // Cargar pacientes cuando cambie la página, el término de búsqueda o los filtros
  useEffect(() => {
    fetchPatients();
  }, [page, debouncedSearchTerm, filters]);

  const fetchPatients = async () => {
    setLoading(true);
    try {
      const response = await patientsService.getPaginated({
        page,
        searchTerm: debouncedSearchTerm,
        filters,
      });

      if (response.success) {
        setPatients(response.data.data);
        setTotalPages(response.data.totalPages);
        setTotalCount(response.data.count);
      } else {
        showNotification('Error al cargar pacientes: ' + response.message, 'error');
      }
    } catch (error) {
      console.error('Error fetching patients:', error);
      showNotification('Error inesperado al cargar pacientes', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Cargar estadísticas
  const fetchStatistics = async () => {
    try {
      const response = await patientsService.getStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  };

  // Cargar estadísticas al montar el componente
  useEffect(() => {
    fetchStatistics();
  }, []);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(1); // Resetear a la primera página cuando se busca
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, patient: Patient) => {
    setAnchorEl(event.currentTarget);
    setSelectedPatient(patient);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedPatient(null);
  };

  const handleStatusUpdate = async (patientId: string, newStatus: PatientStatus) => {
    try {
      const response = await patientsService.updateStatus(
        patientId, 
        newStatus, 
        newStatus === 'activo' ? new Date().toISOString().split('T')[0] : undefined
      );

      if (response.success) {
        showNotification('Estado del paciente actualizado correctamente', 'success');
        fetchPatients(); // Recargar la lista
      } else {
        showNotification('Error al actualizar estado: ' + response.message, 'error');
      }
    } catch (error) {
      console.error('Error updating patient status:', error);
      showNotification('Error inesperado al actualizar estado', 'error');
    }
    handleMenuClose();
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('es-ES');
  };

  const handleCreatePatient = () => {
    setEditingPatient(null);
    setFormDialogOpen(true);
  };

  const handleEditPatientClick = (patient: Patient) => {
    setEditingPatient(patient);
    setFormDialogOpen(true);
    handleMenuClose();
  };

  const handleFormSuccess = (patient: Patient) => {
    fetchPatients(); // Recargar la lista
    setFormDialogOpen(false);
    setEditingPatient(null);
  };

  const handleFormClose = () => {
    setFormDialogOpen(false);
    setEditingPatient(null);
  };

  // Funciones para exportación
  const handleExportClick = () => {
    setExportDialogOpen(true);
  };

  const handleExportClose = () => {
    setExportDialogOpen(false);
  };

  if (loading && patients.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper elevation={2} sx={{ p: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="h5" gutterBottom>
              Lista de Pacientes
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total: {totalCount} pacientes
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<FileDownloadIcon />}
              onClick={handleExportClick}
              sx={{
                borderColor: '#3498db',
                color: '#3498db',
                borderRadius: '10px',
                px: 3,
                py: 1,
                textTransform: 'none',
                fontWeight: 600,
                '&:hover': {
                  borderColor: '#2980b9',
                  backgroundColor: '#e3f2fd',
                },
              }}
            >
              Exportar
            </Button>
            <Button
              variant="contained"
              startIcon={<PersonAddIcon />}
              onClick={handleCreatePatient}
              sx={{
                backgroundColor: '#27ae60',
                borderRadius: '10px',
                px: 3,
                py: 1,
                textTransform: 'none',
                fontWeight: 600,
                '&:hover': {
                  backgroundColor: '#229954',
                },
              }}
            >
              Nuevo Paciente
            </Button>
          </Box>
        </Box>

        {/* Filtros avanzados */}
        <AdvancedFilters
          filters={filters}
          onFiltersChange={setFilters}
          psychologists={[]} // TODO: Cargar lista de psicólogos
        />

        <TextField
          fullWidth
          label="Buscar paciente..."
          placeholder="Nombre, apellidos, email o documento"
          variant="outlined"
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
          }}
          sx={{ mt: 2 }}
        />
      </Box>

      {patients.length === 0 && !loading ? (
        <Alert severity="info">
          {searchTerm ? 'No se encontraron pacientes con ese criterio de búsqueda.' : 'No hay pacientes registrados.'}
        </Alert>
      ) : (
        <>
          <List>
            {patients.map((patient, index) => (
              <React.Fragment key={patient.id}>
                <ListItem
                  sx={{ 
                    py: 2,
                    '&:hover': { backgroundColor: 'action.hover' },
                    cursor: onPatientSelect ? 'pointer' : 'default',
                  }}
                  onClick={() => onPatientSelect?.(patient)}
                >
                  <ListItemAvatar>
                    <Avatar>
                      <PersonIcon />
                    </Avatar>
                  </ListItemAvatar>
                  
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="subtitle1">
                          {patient.name} {patient.apellidos || ''}
                        </Typography>
                        <PatientStatusChip status={patient.status || 'pendiente_evaluacion'} />
                      </Box>
                    }
                    secondary={
                      <>
                        <Typography component="span" variant="body2" color="text.secondary" display="block">
                          Última evaluación: {formatDate(patient.last_evaluation_date)}
                        </Typography>
                        {patient.email && (
                          <Typography component="span" variant="body2" color="text.secondary" display="block">
                            Email: {patient.email}
                          </Typography>
                        )}
                        {patient.documento_identidad && (
                          <Typography component="span" variant="body2" color="text.secondary" display="block">
                            Documento: {patient.documento_identidad}
                          </Typography>
                        )}
                      </>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    <Box display="flex" gap={1}>
                      {onViewResults && (
                        <Button
                          size="small"
                          startIcon={<VisibilityIcon />}
                          onClick={(e) => {
                            e.stopPropagation();
                            onViewResults(patient.id);
                          }}
                        >
                          Resultados
                        </Button>
                      )}
                      {onNewEvaluation && (
                        <Button
                          size="small"
                          variant="contained"
                          startIcon={<AssessmentIcon />}
                          onClick={(e) => {
                            e.stopPropagation();
                            onNewEvaluation(patient.id);
                          }}
                        >
                          Evaluar
                        </Button>
                      )}
                      <IconButton
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMenuOpen(e, patient);
                        }}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
                {index < patients.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>

          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handlePageChange}
                color="primary"
                showFirstButton
                showLastButton
              />
            </Box>
          )}
        </>
      )}

      {/* Menú contextual */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          if (selectedPatient) handleEditPatientClick(selectedPatient);
        }}>
          <EditIcon sx={{ mr: 1 }} />
          Editar Paciente
        </MenuItem>
        <MenuItem onClick={() => {
          if (selectedPatient) handleStatusUpdate(selectedPatient.id, 'activo');
        }}>
          Marcar como Activo
        </MenuItem>
        <MenuItem onClick={() => {
          if (selectedPatient) handleStatusUpdate(selectedPatient.id, 'inactivo');
        }}>
          Marcar como Inactivo
        </MenuItem>
        <MenuItem onClick={() => {
          if (selectedPatient) handleStatusUpdate(selectedPatient.id, 'pendiente_evaluacion');
        }}>
          Marcar como Pendiente
        </MenuItem>
      </Menu>

      {loading && patients.length > 0 && (
        <Box display="flex" justifyContent="center" mt={2}>
          <CircularProgress size={24} />
        </Box>
      )}

      {/* Formulario de creación/edición */}
      <PatientFormDialog
        open={formDialogOpen}
        onClose={handleFormClose}
        patient={editingPatient}
        onSuccess={handleFormSuccess}
      />

      {/* Diálogo de exportación */}
      <ExportDialog
        open={exportDialogOpen}
        onClose={handleExportClose}
        patients={patients}
        statistics={statistics}
      />
    </Paper>
  );
};
