import React from 'react';
import { Box, Typography } from '@mui/material';
import { Psychology as PsychologyIcon } from '@mui/icons-material';

interface LogoProps {
  size?: 'small' | 'medium' | 'large';
  showText?: boolean;
}

const Logo: React.FC<LogoProps> = ({ size = 'medium', showText = true }) => {
  const sizeMap = {
    small: { icon: 24, text: '1rem' },
    medium: { icon: 32, text: '1.25rem' },
    large: { icon: 40, text: '1.5rem' }
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: sizeMap[size].icon + 8,
          height: sizeMap[size].icon + 8,
          borderRadius: '50%',
          background: 'linear-gradient(45deg, #ffffff 30%, #ffd700 90%)',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        }}
      >
        <PsychologyIcon 
          sx={{ 
            fontSize: sizeMap[size].icon,
            color: '#011129'
          }} 
        />
      </Box>
      {showText && (
        <Typography
          variant="h6"
          sx={{
            fontSize: sizeMap[size].text,
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #011129 30%, #1976d2 90%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          MACI
        </Typography>
      )}
    </Box>
  );
};

export default Logo;
