import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>ltip,
  ResponsiveContainer,
} from 'recharts';

const sampleData = [
  { ageRange: '12-15', count: 21, percentage: 21 },
  { ageRange: '16-18', count: 35, percentage: 35 },
  { ageRange: '19-25', count: 28, percentage: 28 },
  { ageRange: '26-30', count: 12, percentage: 12 },
  { ageRange: '31+', count: 4, percentage: 4 },
];

interface AgeDistributionChartProps {
  data?: typeof sampleData;
  height?: number;
}

const AgeDistributionChart: React.FC<AgeDistributionChartProps> = ({ 
  data = sampleData, 
  height = 250 
}) => {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div style={{
          backgroundColor: '#fff',
          border: '1px solid #5A92C8',
          borderRadius: '8px',
          padding: '10px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        }}>
          <p style={{ margin: 0, fontWeight: 'bold', color: '#333' }}>
            Edad: {label} años
          </p>
          <p style={{ margin: 0, color: '#666' }}>
            Pacientes: {data.count}
          </p>
          <p style={{ margin: 0, color: '#666' }}>
            Porcentaje: {data.percentage}%
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart
        data={data}
        margin={{
          top: 20,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="ageRange" 
          tick={{ fontSize: 12 }}
          stroke="#666"
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          stroke="#666"
          label={{ 
            value: 'Número de Pacientes', 
            angle: -90, 
            position: 'insideLeft',
            style: { textAnchor: 'middle' }
          }}
        />
        <Tooltip content={<CustomTooltip />} />
        <Bar 
          dataKey="count" 
          fill="#5A92C8"
          radius={[4, 4, 0, 0]}
          stroke="#4A82B8"
          strokeWidth={1}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default AgeDistributionChart;
