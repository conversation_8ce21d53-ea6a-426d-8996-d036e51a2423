import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  Switch,
  FormControlLabel,
  TextField,
  Button,
  Divider,
  Alert,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Slider,
  FormGroup,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Password as PasswordIcon,
  Timer as TimerIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Shield as ShieldIcon,
  Settings as SettingsIcon,
  Info as InfoIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useNotifier } from '../../hooks/useNotifier';

interface AuthConfig {
  // Password Policies
  minimumPasswordLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSymbols: boolean;
  passwordExpiration: number; // days, 0 = never

  // Session Management
  sessionTimeout: number; // hours
  inactivityTimeout: number; // hours
  enableRefreshTokenRotation: boolean;
  refreshTokenReuseInterval: number; // seconds

  // Email Configuration
  enableEmailSignup: boolean;
  requireEmailConfirmation: boolean;
  doubleConfirmEmailChanges: boolean;
  securePasswordChange: boolean;
  emailOtpLength: number;

  // Security Features
  enableTwoFactor: boolean;
  maxLoginAttempts: number;
  lockoutDuration: number; // minutes
  enableCaptcha: boolean;

  // Advanced Settings
  enableAnonymousSignIn: boolean;
  enableManualLinking: boolean;
  jwtExpiry: number; // seconds
}

const defaultConfig: AuthConfig = {
  minimumPasswordLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSymbols: false,
  passwordExpiration: 0,
  sessionTimeout: 24,
  inactivityTimeout: 8,
  enableRefreshTokenRotation: true,
  refreshTokenReuseInterval: 10,
  enableEmailSignup: true,
  requireEmailConfirmation: true,
  doubleConfirmEmailChanges: true,
  securePasswordChange: true,
  emailOtpLength: 6,
  enableTwoFactor: false,
  maxLoginAttempts: 5,
  lockoutDuration: 15,
  enableCaptcha: false,
  enableAnonymousSignIn: false,
  enableManualLinking: false,
  jwtExpiry: 3600,
};

const AuthConfigPanel: React.FC = () => {
  const [config, setConfig] = useState<AuthConfig>(defaultConfig);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testDialogOpen, setTestDialogOpen] = useState(false);
  const { showSuccess, showError, showInfo } = useNotifier();

  useEffect(() => {
    loadConfiguration();
  }, []);

  const loadConfiguration = async () => {
    setLoading(true);
    try {
      // Simular carga de configuración desde Supabase
      // En implementación real, esto vendría de la base de datos
      await new Promise((resolve) => setTimeout(resolve, 1000));
      showInfo('Configuración cargada desde la base de datos');
    } catch (error) {
      showError('Error al cargar la configuración');
    } finally {
      setLoading(false);
    }
  };

  const saveConfiguration = async () => {
    setSaving(true);
    try {
      // Validaciones
      if (config.minimumPasswordLength < 6) {
        throw new Error(
          'La longitud mínima de contraseña debe ser al menos 6 caracteres',
        );
      }
      if (config.sessionTimeout < 1) {
        throw new Error('El tiempo de sesión debe ser al menos 1 hora');
      }

      // Simular guardado en Supabase
      await new Promise((resolve) => setTimeout(resolve, 1500));

      showSuccess('Configuración de autenticación guardada correctamente');
    } catch (error) {
      showError(
        error instanceof Error
          ? error.message
          : 'Error al guardar la configuración',
      );
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = () => {
    setConfig(defaultConfig);
    showInfo('Configuración restablecida a valores por defecto');
  };

  const testPasswordPolicy = () => {
    setTestDialogOpen(true);
  };

  const getPasswordStrengthText = () => {
    const requirements = [];
    if (config.requireUppercase) requirements.push('mayúsculas');
    if (config.requireLowercase) requirements.push('minúsculas');
    if (config.requireNumbers) requirements.push('números');
    if (config.requireSymbols) requirements.push('símbolos');

    return `Mínimo ${config.minimumPasswordLength} caracteres${requirements.length > 0 ? `, incluyendo: ${requirements.join(', ')}` : ''}`;
  };

  const handleConfigChange = (field: keyof AuthConfig, value: any) => {
    setConfig((prev) => ({ ...prev, [field]: value }));
  };

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
      >
        <Typography>Cargando configuración...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
          >
            <Box display="flex" alignItems="center">
              <SecurityIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h5" component="h2">
                Configuración de Autenticación
              </Typography>
            </Box>
            <Box>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={resetToDefaults}
                sx={{ mr: 1 }}
              >
                Restablecer
              </Button>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={saveConfiguration}
                disabled={saving}
              >
                {saving ? 'Guardando...' : 'Guardar Cambios'}
              </Button>
            </Box>
          </Box>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Configure las políticas de seguridad y autenticación del sistema
          </Typography>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        {/* Password Policies */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <PasswordIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Políticas de Contraseñas</Typography>
              </Box>

              <TextField
                fullWidth
                label="Longitud mínima"
                type="number"
                value={config.minimumPasswordLength}
                onChange={(e) =>
                  handleConfigChange(
                    'minimumPasswordLength',
                    parseInt(e.target.value),
                  )
                }
                inputProps={{ min: 6, max: 50 }}
                sx={{ mb: 2 }}
              />

              <FormGroup>
                <FormControlLabel
                  control={
                    <Switch
                      checked={config.requireUppercase}
                      onChange={(e) =>
                        handleConfigChange('requireUppercase', e.target.checked)
                      }
                    />
                  }
                  label="Requerir mayúsculas"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={config.requireLowercase}
                      onChange={(e) =>
                        handleConfigChange('requireLowercase', e.target.checked)
                      }
                    />
                  }
                  label="Requerir minúsculas"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={config.requireNumbers}
                      onChange={(e) =>
                        handleConfigChange('requireNumbers', e.target.checked)
                      }
                    />
                  }
                  label="Requerir números"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={config.requireSymbols}
                      onChange={(e) =>
                        handleConfigChange('requireSymbols', e.target.checked)
                      }
                    />
                  }
                  label="Requerir símbolos"
                />
              </FormGroup>

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Política actual:</strong> {getPasswordStrengthText()}
                </Typography>
              </Alert>

              <Button
                variant="outlined"
                size="small"
                onClick={testPasswordPolicy}
                sx={{ mt: 1 }}
              >
                Probar Política
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Session Management */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <TimerIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Gestión de Sesiones</Typography>
              </Box>

              <TextField
                fullWidth
                label="Tiempo de sesión (horas)"
                type="number"
                value={config.sessionTimeout}
                onChange={(e) =>
                  handleConfigChange('sessionTimeout', parseInt(e.target.value))
                }
                inputProps={{ min: 1, max: 168 }}
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Tiempo de inactividad (horas)"
                type="number"
                value={config.inactivityTimeout}
                onChange={(e) =>
                  handleConfigChange(
                    'inactivityTimeout',
                    parseInt(e.target.value),
                  )
                }
                inputProps={{ min: 1, max: 24 }}
                sx={{ mb: 2 }}
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={config.enableRefreshTokenRotation}
                    onChange={(e) =>
                      handleConfigChange(
                        'enableRefreshTokenRotation',
                        e.target.checked,
                      )
                    }
                  />
                }
                label="Rotación de tokens de actualización"
              />

              <TextField
                fullWidth
                label="Intervalo de reutilización de token (segundos)"
                type="number"
                value={config.refreshTokenReuseInterval}
                onChange={(e) =>
                  handleConfigChange(
                    'refreshTokenReuseInterval',
                    parseInt(e.target.value),
                  )
                }
                disabled={!config.enableRefreshTokenRotation}
                sx={{ mt: 2 }}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Test Password Dialog */}
      <Dialog
        open={testDialogOpen}
        onClose={() => setTestDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Probar Política de Contraseñas</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Ingrese una contraseña de prueba"
            type="password"
            sx={{ mt: 1 }}
            helperText={getPasswordStrengthText()}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTestDialogOpen(false)}>Cerrar</Button>
          <Button variant="contained">Validar</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AuthConfigPanel;
