import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveC<PERSON>r,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'recharts';

const sampleData = [
  { name: '<PERSON><PERSON><PERSON>', value: 58, count: 58 },
  { name: '<PERSON><PERSON><PERSON><PERSON>', value: 40, count: 40 },
  { name: '<PERSON><PERSON>', value: 2, count: 2 },
];

const COLORS = {
  'Femenino': '#63B4A9',
  'Masculino': '#5A92C8', 
  'Otro': '#F28A7C'
};

interface GenderDistributionChartProps {
  data?: typeof sampleData;
  height?: number;
}

const GenderDistributionChart: React.FC<GenderDistributionChartProps> = ({ 
  data = sampleData, 
  height = 250 
}) => {
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null; // No mostrar etiquetas para segmentos muy pequeños
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div style={{
          backgroundColor: '#fff',
          border: '1px solid #5A92C8',
          borderRadius: '8px',
          padding: '10px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        }}>
          <p style={{ margin: 0, fontWeight: 'bold', color: '#333' }}>
            {data.name}
          </p>
          <p style={{ margin: 0, color: '#666' }}>
            Cantidad: {data.count} pacientes
          </p>
          <p style={{ margin: 0, color: '#666' }}>
            Porcentaje: {data.value}%
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={renderCustomizedLabel}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={COLORS[entry.name as keyof typeof COLORS]} 
            />
          ))}
        </Pie>
        <Tooltip content={<CustomTooltip />} />
        <Legend 
          verticalAlign="bottom" 
          height={36}
          iconType="circle"
        />
      </PieChart>
    </ResponsiveContainer>
  );
};

export default GenderDistributionChart;
