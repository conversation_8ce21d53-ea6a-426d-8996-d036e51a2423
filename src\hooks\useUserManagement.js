/**
 * Hook personalizado para gestión de usuarios
 */

import { useState, useEffect, useCallback } from 'react';
import userManagementService from '../services/userManagementService';
import { toast } from 'react-toastify';

export const useUserManagement = (initialFilters = {}) => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    tipo_usuario: '',
    activo: undefined,
    institucion_id: '',
    sortField: 'fecha_creacion',
    sortDirection: 'desc',
    page: 1,
    pageSize: 10,
    ...initialFilters
  });
  const [totalCount, setTotalCount] = useState(0);
  const [statistics, setStatistics] = useState(null);

  /**
   * Carga la lista de usuarios
   */
  const fetchUsers = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error, count } = await userManagementService.getUsers(filters);
      
      if (error) {
        throw error;
      }

      setUsers(data || []);
      setTotalCount(count || 0);
    } catch (err) {
      setError(err);
      console.error('Error al cargar usuarios:', err);
      toast.error('Error al cargar la lista de usuarios');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  /**
   * Carga estadísticas de usuarios
   */
  const fetchStatistics = useCallback(async () => {
    try {
      const { data, error } = await userManagementService.getUserStatistics();
      
      if (error) {
        throw error;
      }

      setStatistics(data);
    } catch (err) {
      console.error('Error al cargar estadísticas:', err);
    }
  }, []);

  /**
   * Crea un nuevo usuario
   */
  const createUser = useCallback(async (userData) => {
    setLoading(true);
    
    try {
      const { data, error } = await userManagementService.createUser(userData);
      
      if (error) {
        throw error;
      }

      toast.success('Usuario creado exitosamente');
      await fetchUsers();
      await fetchStatistics();
      
      return { success: true, data };
    } catch (err) {
      console.error('Error al crear usuario:', err);
      toast.error(`Error al crear usuario: ${err.message}`);
      return { success: false, error: err };
    } finally {
      setLoading(false);
    }
  }, [fetchUsers, fetchStatistics]);

  /**
   * Actualiza un usuario existente
   */
  const updateUser = useCallback(async (userId, userData) => {
    setLoading(true);
    
    try {
      const { data, error } = await userManagementService.updateUser(userId, userData);
      
      if (error) {
        throw error;
      }

      toast.success('Usuario actualizado exitosamente');
      await fetchUsers();
      await fetchStatistics();
      
      return { success: true, data };
    } catch (err) {
      console.error('Error al actualizar usuario:', err);
      toast.error(`Error al actualizar usuario: ${err.message}`);
      return { success: false, error: err };
    } finally {
      setLoading(false);
    }
  }, [fetchUsers, fetchStatistics]);

  /**
   * Elimina un usuario (soft delete)
   */
  const deleteUser = useCallback(async (userId, deletedBy) => {
    setLoading(true);
    
    try {
      const { success, error } = await userManagementService.deleteUser(userId, deletedBy);
      
      if (!success) {
        throw error;
      }

      toast.success('Usuario eliminado exitosamente');
      await fetchUsers();
      await fetchStatistics();
      
      return { success: true };
    } catch (err) {
      console.error('Error al eliminar usuario:', err);
      toast.error(`Error al eliminar usuario: ${err.message}`);
      return { success: false, error: err };
    } finally {
      setLoading(false);
    }
  }, [fetchUsers, fetchStatistics]);

  /**
   * Busca un usuario por documento
   */
  const searchUserByDocument = useCallback(async (documento) => {
    try {
      const { data, error } = await userManagementService.getUserByDocument(documento);
      
      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (err) {
      console.error('Error al buscar usuario por documento:', err);
      return { success: false, error: err };
    }
  }, []);

  /**
   * Obtiene los permisos de un usuario
   */
  const getUserPermissions = useCallback(async (userId) => {
    try {
      const { data, error } = await userManagementService.getUserPermissions(userId);
      
      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (err) {
      console.error('Error al obtener permisos:', err);
      return { success: false, error: err };
    }
  }, []);

  /**
   * Verifica acceso a ruta
   */
  const checkRouteAccess = useCallback(async (userId, routePath) => {
    try {
      const { hasAccess, error } = await userManagementService.checkRouteAccess(userId, routePath);
      
      if (error) {
        throw error;
      }

      return { success: true, hasAccess };
    } catch (err) {
      console.error('Error al verificar acceso:', err);
      return { success: false, hasAccess: false, error: err };
    }
  }, []);

  /**
   * Actualiza los filtros
   */
  const updateFilters = useCallback((newFilters) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: newFilters.page || 1 // Reset page when filters change
    }));
  }, []);

  /**
   * Resetea los filtros
   */
  const resetFilters = useCallback(() => {
    setFilters({
      search: '',
      tipo_usuario: '',
      activo: undefined,
      institucion_id: '',
      sortField: 'fecha_creacion',
      sortDirection: 'desc',
      page: 1,
      pageSize: 10
    });
  }, []);

  /**
   * Cambia la página
   */
  const changePage = useCallback((page) => {
    setFilters(prev => ({ ...prev, page }));
  }, []);

  /**
   * Cambia el ordenamiento
   */
  const changeSort = useCallback((field) => {
    setFilters(prev => ({
      ...prev,
      sortField: field,
      sortDirection: prev.sortField === field && prev.sortDirection === 'asc' ? 'desc' : 'asc',
      page: 1
    }));
  }, []);

  // Cargar datos iniciales
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  return {
    // Estado
    users,
    loading,
    error,
    filters,
    totalCount,
    statistics,
    
    // Acciones
    fetchUsers,
    fetchStatistics,
    createUser,
    updateUser,
    deleteUser,
    searchUserByDocument,
    getUserPermissions,
    checkRouteAccess,
    
    // Filtros y paginación
    updateFilters,
    resetFilters,
    changePage,
    changeSort,
    
    // Utilidades
    totalPages: Math.ceil(totalCount / filters.pageSize),
    hasNextPage: filters.page < Math.ceil(totalCount / filters.pageSize),
    hasPrevPage: filters.page > 1
  };
};
