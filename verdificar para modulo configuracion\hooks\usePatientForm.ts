import { useState, useCallback } from 'react';
import type { Patient } from '../types/patient';

export interface PatientFormData {
  name: string;
  birthDate: string;
  gender: string;
  testDate: string;
  psychologist: string;
}

export interface PatientFormErrors {
  name?: string;
  birthDate?: string;
  gender?: string;
  testDate?: string;
  psychologist?: string;
}

export interface UsePatientFormReturn {
  formData: PatientFormData;
  formErrors: PatientFormErrors;
  isValid: boolean;
  setFormData: React.Dispatch<React.SetStateAction<PatientFormData>>;
  setFormErrors: React.Dispatch<React.SetStateAction<PatientFormErrors>>;
  handleInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => void;
  validateForm: () => boolean;
  resetForm: () => void;
  loadPatientData: (patient: Patient) => void;
  getFormattedData: () => Omit<Patient, 'id' | 'age' | 'questionnaires'>;
}

const initialFormData: PatientFormData = {
  name: '',
  birthDate: '',
  gender: '',
  testDate: '',
  psychologist: '',
};

/**
 * Hook personalizado para gestionar el formulario de pacientes
 * Encapsula toda la lógica de validación, estado y transformación de datos
 */
export const usePatientForm = (): UsePatientFormReturn => {
  const [formData, setFormData] = useState<PatientFormData>(initialFormData);
  const [formErrors, setFormErrors] = useState<PatientFormErrors>({});

  const validateForm = useCallback((): boolean => {
    const errors: PatientFormErrors = {};

    if (!formData.name.trim()) {
      errors.name = 'El nombre es requerido';
    }

    if (!formData.birthDate) {
      errors.birthDate = 'La fecha de nacimiento es requerida';
    }

    if (!formData.gender) {
      errors.gender = 'El género es requerido';
    }

    if (!formData.testDate) {
      errors.testDate = 'La fecha de evaluación es requerida';
    }

    if (!formData.psychologist.trim()) {
      errors.psychologist = 'El nombre del psicólogo es requerido';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData]);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;

      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));

      // Clear error when field is modified
      if (formErrors[name as keyof PatientFormErrors]) {
        setFormErrors((prev) => ({
          ...prev,
          [name]: undefined,
        }));
      }
    },
    [formErrors],
  );

  const resetForm = useCallback(() => {
    setFormData(initialFormData);
    setFormErrors({});
  }, []);

  const loadPatientData = useCallback((patient: Patient) => {
    setFormData({
      name: patient.name,
      birthDate: patient.birthDate,
      gender: patient.gender,
      testDate: patient.dateCreated,
      psychologist: patient.notes,
    });
    setFormErrors({});
  }, []);

  const getFormattedData = useCallback((): Omit<
    Patient,
    'id' | 'age' | 'questionnaires'
  > => {
    return {
      name: formData.name.trim(),
      birthDate: formData.birthDate,
      gender: formData.gender,
      dateCreated: formData.testDate,
      notes: formData.psychologist.trim(),
      email: '',
      phone: '',
      address: '',
    };
  }, [formData]);

  const isValid =
    Object.keys(formErrors).length === 0 &&
    formData.name.trim() !== '' &&
    formData.birthDate !== '' &&
    formData.gender !== '' &&
    formData.testDate !== '' &&
    formData.psychologist.trim() !== '';

  return {
    formData,
    formErrors,
    isValid,
    setFormData,
    setFormErrors,
    handleInputChange,
    validateForm,
    resetForm,
    loadPatientData,
    getFormattedData,
  };
};
