import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  IconButton,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Close as CloseIcon,
  Person as PersonIcon,
  Save as SaveIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import { Patient, PatientStatus } from '../../types/patient';
import { patientsService } from '../../services/patients';
import { useNotification } from '../../hooks/useNotification';

interface PatientFormDialogProps {
  open: boolean;
  onClose: () => void;
  patient?: Patient | null;
  onSuccess?: (patient: Patient) => void;
}

interface FormData {
  name: string;
  apellidos: string;
  email: string;
  documento_identidad: string;
  telefono: string;
  birthDate: string;
  gender: string;
  status: PatientStatus;
  notes: string;
}

const initialFormData: FormData = {
  name: '',
  apellidos: '',
  email: '',
  documento_identidad: '',
  telefono: '',
  birthDate: '',
  gender: '',
  status: 'pendiente_evaluacion',
  notes: '',
};

const genderOptions = [
  { value: 'M', label: 'Masculino' },
  { value: 'F', label: 'Femenino' },
];

const statusOptions = [
  { value: 'activo', label: 'Activo' },
  { value: 'inactivo', label: 'Inactivo' },
  { value: 'pendiente_evaluacion', label: 'Pendiente de Evaluación' },
];

export const PatientFormDialog: React.FC<PatientFormDialogProps> = ({
  open,
  onClose,
  patient,
  onSuccess,
}) => {
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [loading, setLoading] = useState(false);
  const { showNotification } = useNotification();

  const isEditing = Boolean(patient);

  useEffect(() => {
    if (patient) {
      setFormData({
        name: patient.name || '',
        apellidos: patient.apellidos || '',
        email: patient.email || '',
        documento_identidad: patient.documento_identidad || '',
        telefono: patient.telefono || '',
        birthDate: patient.birthDate || '',
        gender: patient.gender || '',
        status: patient.status || 'pendiente_evaluacion',
        notes: patient.notes || '',
      });
    } else {
      setFormData(initialFormData);
    }
    setErrors({});
  }, [patient, open]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = event.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleSelectChange = (event: any) => {
    const { name, value } = event.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'El nombre es requerido';
    }

    if (!formData.apellidos.trim()) {
      newErrors.apellidos = 'Los apellidos son requeridos';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'El email es requerido';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'El formato del email no es válido';
    }

    if (!formData.documento_identidad.trim()) {
      newErrors.documento_identidad = 'El documento de identidad es requerido';
    }

    if (!formData.birthDate) {
      newErrors.birthDate = 'La fecha de nacimiento es requerida';
    }

    if (!formData.gender) {
      newErrors.gender = 'El género es requerido';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      let response;
      
      if (isEditing && patient) {
        // Update existing patient
        response = await patientsService.update(patient.id, {
          ...formData,
          // Convert to the format expected by the service
          psychologist: formData.notes, // Temporary mapping
        });
      } else {
        // Create new patient
        response = await patientsService.create({
          ...formData,
          psychologist: formData.notes, // Temporary mapping
          testDate: new Date().toISOString().split('T')[0],
        });
      }

      if (response.success) {
        showNotification(
          isEditing ? 'Paciente actualizado correctamente' : 'Paciente creado correctamente',
          'success'
        );
        onSuccess?.(response.data);
        handleClose();
      } else {
        showNotification('Error: ' + response.message, 'error');
      }
    } catch (error) {
      console.error('Error saving patient:', error);
      showNotification('Error inesperado al guardar el paciente', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData(initialFormData);
    setErrors({});
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '16px',
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
        },
      }}
    >
      <DialogTitle
        sx={{
          borderBottom: '1px solid #e9ecef',
          pb: 2,
          backgroundColor: '#f8f9fa',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box
            sx={{
              p: 1.5,
              borderRadius: '10px',
              backgroundColor: isEditing ? '#fff3e0' : '#e8f5e9',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {isEditing ? (
              <EditIcon sx={{ color: '#f57c00', fontSize: '1.5rem' }} />
            ) : (
              <PersonIcon sx={{ color: '#2e7d32', fontSize: '1.5rem' }} />
            )}
          </Box>
          <Typography
            variant="h6"
            sx={{
              color: '#2c3e50',
              fontWeight: 600,
              fontSize: '1.3rem',
            }}
          >
            {isEditing ? 'Editar Paciente' : 'Nuevo Paciente'}
          </Typography>
        </Box>
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        <Grid container spacing={3}>
          {/* Información Personal */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom sx={{ color: '#2c3e50', fontWeight: 600 }}>
              Información Personal
            </Typography>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              name="name"
              label="Nombre *"
              value={formData.name}
              onChange={handleInputChange}
              fullWidth
              error={!!errors.name}
              helperText={errors.name}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '10px',
                },
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              name="apellidos"
              label="Apellidos *"
              value={formData.apellidos}
              onChange={handleInputChange}
              fullWidth
              error={!!errors.apellidos}
              helperText={errors.apellidos}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '10px',
                },
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              name="documento_identidad"
              label="Documento de Identidad *"
              value={formData.documento_identidad}
              onChange={handleInputChange}
              fullWidth
              error={!!errors.documento_identidad}
              helperText={errors.documento_identidad}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '10px',
                },
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              name="telefono"
              label="Teléfono"
              value={formData.telefono}
              onChange={handleInputChange}
              fullWidth
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '10px',
                },
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              name="email"
              label="Email *"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              fullWidth
              error={!!errors.email}
              helperText={errors.email}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '10px',
                },
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              name="birthDate"
              label="Fecha de Nacimiento *"
              type="date"
              value={formData.birthDate}
              onChange={handleInputChange}
              fullWidth
              error={!!errors.birthDate}
              helperText={errors.birthDate}
              InputLabelProps={{ shrink: true }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '10px',
                },
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth error={!!errors.gender}>
              <InputLabel>Género *</InputLabel>
              <Select
                name="gender"
                value={formData.gender}
                onChange={handleSelectChange}
                label="Género *"
                sx={{ borderRadius: '10px' }}
              >
                {genderOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
              {errors.gender && (
                <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                  {errors.gender}
                </Typography>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Estado</InputLabel>
              <Select
                name="status"
                value={formData.status}
                onChange={handleSelectChange}
                label="Estado"
                sx={{ borderRadius: '10px' }}
              >
                {statusOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <TextField
              name="notes"
              label="Notas adicionales"
              value={formData.notes}
              onChange={handleInputChange}
              fullWidth
              multiline
              rows={3}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '10px',
                },
              }}
            />
          </Grid>
        </Grid>

        {Object.keys(errors).length > 0 && (
          <Alert severity="error" sx={{ mt: 2 }}>
            Por favor, corrija los errores antes de continuar.
          </Alert>
        )}
      </DialogContent>

      <DialogActions
        sx={{
          p: 3,
          backgroundColor: '#f8f9fa',
          borderTop: '1px solid #e9ecef',
          gap: 2,
        }}
      >
        <Button
          onClick={handleClose}
          sx={{
            color: '#7f8c8d',
            borderRadius: '10px',
            px: 3,
            py: 1,
            textTransform: 'none',
            fontWeight: 500,
          }}
          disabled={loading}
        >
          Cancelar
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
          sx={{
            backgroundColor: isEditing ? '#f39c12' : '#27ae60',
            borderRadius: '10px',
            px: 4,
            py: 1,
            textTransform: 'none',
            fontWeight: 600,
            '&:hover': {
              backgroundColor: isEditing ? '#e67e22' : '#229954',
            },
          }}
          disabled={loading}
        >
          {loading ? 'Guardando...' : isEditing ? 'Actualizar' : 'Crear Paciente'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
