import React from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Box,
  Avatar,
  IconButton,
  Tooltip,
  Chip,
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Edit as EditIcon,
  Assignment as AssignmentIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  CalendarToday as CalendarIcon,
  TrendingUp as TrendingUpIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import StatusBadge from './StatusBadge';

interface PatientCardProps {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  documentId?: string;
  status?: 'activo' | 'inactivo';
  lastEvaluation?: Date;
  totalEvaluations?: number;
  pendingEvaluations?: number;
  completedEvaluations?: number;
  psychologistName?: string;
  registrationDate?: Date;
  onView?: () => void;
  onEdit?: () => void;
  onAssignEvaluation?: () => void;
  onViewHistory?: () => void;
  variant?: 'compact' | 'detailed';
  showActions?: boolean;
}

const StyledCard = styled(Card)(({ theme }) => ({
  transition: 'all 0.3s ease-in-out',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 6px 20px rgba(0, 0, 0, 0.1)',
  },
}));

const PatientAvatar = styled(Avatar)(({ theme }) => ({
  width: 56,
  height: 56,
  background: 'linear-gradient(135deg, #5A92C8 0%, #63B4A9 100%)',
  fontSize: '1.5rem',
  fontWeight: 600,
}));

const StatChip = styled(Chip)(({ theme }) => ({
  borderRadius: 16,
  height: 24,
  fontSize: '0.75rem',
  fontWeight: 500,
  '& .MuiChip-label': {
    padding: '0 8px',
  },
}));

export const PatientCard: React.FC<PatientCardProps> = ({
  id,
  name,
  email,
  phone,
  documentId,
  status = 'activo',
  lastEvaluation,
  totalEvaluations = 0,
  pendingEvaluations = 0,
  completedEvaluations = 0,
  psychologistName,
  registrationDate,
  onView,
  onEdit,
  onAssignEvaluation,
  onViewHistory,
  variant = 'detailed',
  showActions = true,
}) => {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('es-ES', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    }).format(date);
  };

  const getInitials = (fullName: string) => {
    return fullName
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const isRecentlyActive = lastEvaluation && 
    (new Date().getTime() - lastEvaluation.getTime()) < (30 * 24 * 60 * 60 * 1000); // 30 días

  if (variant === 'compact') {
    return (
      <StyledCard>
        <CardContent sx={{ p: 2 }}>
          <Box display="flex" alignItems="center" gap={2}>
            <PatientAvatar>
              {getInitials(name)}
            </PatientAvatar>
            
            <Box flex={1}>
              <Typography variant="h6" component="h3" gutterBottom>
                {name}
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                <StatusBadge status={status} />
                {pendingEvaluations > 0 && (
                  <StatChip
                    label={`${pendingEvaluations} pendientes`}
                    color="warning"
                    size="small"
                  />
                )}
                {isRecentlyActive && (
                  <StatChip
                    label="Activo recientemente"
                    color="success"
                    size="small"
                  />
                )}
              </Box>
            </Box>

            {showActions && (
              <Box display="flex" gap={1}>
                <Tooltip title="Ver perfil">
                  <IconButton size="small" onClick={onView}>
                    <ViewIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Asignar evaluación">
                  <IconButton size="small" onClick={onAssignEvaluation}>
                    <AssignmentIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            )}
          </Box>
        </CardContent>
      </StyledCard>
    );
  }

  return (
    <StyledCard>
      <CardContent>
        {/* Header con avatar y información básica */}
        <Box display="flex" alignItems="flex-start" gap={2} mb={3}>
          <PatientAvatar>
            {getInitials(name)}
          </PatientAvatar>
          
          <Box flex={1}>
            <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
              <Typography variant="h6" component="h3">
                {name}
              </Typography>
              <StatusBadge status={status} />
            </Box>
            
            {documentId && (
              <Typography variant="body2" color="text.secondary" gutterBottom>
                ID: {documentId}
              </Typography>
            )}
            
            {psychologistName && (
              <Typography variant="body2" color="text.secondary">
                Psicólogo: Dr. {psychologistName}
              </Typography>
            )}
          </Box>
        </Box>

        {/* Información de contacto */}
        {(email || phone) && (
          <Box mb={3}>
            {email && (
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <EmailIcon fontSize="small" color="action" />
                <Typography variant="body2" color="text.secondary">
                  {email}
                </Typography>
              </Box>
            )}
            {phone && (
              <Box display="flex" alignItems="center" gap={1}>
                <PhoneIcon fontSize="small" color="action" />
                <Typography variant="body2" color="text.secondary">
                  {phone}
                </Typography>
              </Box>
            )}
          </Box>
        )}

        {/* Estadísticas de evaluaciones */}
        <Box mb={3}>
          <Typography variant="subtitle2" gutterBottom color="text.secondary">
            Evaluaciones
          </Typography>
          <Box display="flex" gap={1} flexWrap="wrap">
            <StatChip
              label={`${totalEvaluations} total`}
              color="primary"
              variant="outlined"
            />
            <StatChip
              label={`${completedEvaluations} completadas`}
              color="success"
              variant="outlined"
            />
            {pendingEvaluations > 0 && (
              <StatChip
                label={`${pendingEvaluations} pendientes`}
                color="warning"
                variant="outlined"
              />
            )}
          </Box>
        </Box>

        {/* Fechas importantes */}
        <Box>
          {lastEvaluation && (
            <Box display="flex" alignItems="center" gap={1} mb={1}>
              <CalendarIcon fontSize="small" color="action" />
              <Typography variant="caption" color="text.secondary">
                Última evaluación: {formatDate(lastEvaluation)}
              </Typography>
            </Box>
          )}
          {registrationDate && (
            <Box display="flex" alignItems="center" gap={1}>
              <PersonIcon fontSize="small" color="action" />
              <Typography variant="caption" color="text.secondary">
                Registrado: {formatDate(registrationDate)}
              </Typography>
            </Box>
          )}
        </Box>
      </CardContent>

      {showActions && (
        <CardActions sx={{ px: 2, pb: 2 }}>
          <Box display="flex" justifyContent="space-between" width="100%">
            <Box display="flex" gap={1}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<ViewIcon />}
                onClick={onView}
              >
                Ver Perfil
              </Button>
              <Button
                variant="contained"
                size="small"
                startIcon={<AssignmentIcon />}
                onClick={onAssignEvaluation}
              >
                Asignar
              </Button>
            </Box>
            
            <Box display="flex" gap={1}>
              {onViewHistory && (
                <Tooltip title="Ver historial">
                  <IconButton size="small" onClick={onViewHistory}>
                    <TrendingUpIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              {onEdit && (
                <Tooltip title="Editar paciente">
                  <IconButton size="small" onClick={onEdit}>
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          </Box>
        </CardActions>
      )}
    </StyledCard>
  );
};

export default PatientCard;
