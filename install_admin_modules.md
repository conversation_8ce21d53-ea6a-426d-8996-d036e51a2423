# 🚀 Instalación de Módulos de Administración - BAT-7

## Descripción

Este documento contiene las instrucciones para instalar los módulos de administración en el proyecto BAT-7. Los módulos incluyen:

1. **Gestión de Usuarios Avanzada** - CRUD completo con filtros y estadísticas
2. **Control de Acceso** - Permisos de rutas y roles
3. **Asignación de Pacientes** - Gestión de asignaciones psicólogo-paciente
4. **Control de Uso** - Monitoreo y estadísticas de uso
5. **Reportes** - Informes y análisis del sistema

## 📋 Prerrequisitos

- Acceso a Supabase Dashboard del proyecto BAT-7
- Permisos de administrador en Supabase
- Proyecto ID: `ydglduxhgwajqdseqzpy`

## 🗄️ PASO 1: Ejecutar Scripts SQL en Supabase

### 1.1 Acceder al Editor SQL

1. Ve a [Supabase Dashboard](https://supabase.com/dashboard/project/ydglduxhgwajqdseqzpy/sql)
2. Abre el editor SQL

### 1.2 Ejecutar Scripts en Orden

**IMPORTANTE**: Ejecuta los scripts en el siguiente orden:

#### Script 1: Crear Tablas
```sql
-- Copiar y pegar el contenido de: src/sql/admin_modules_schema.sql
```

#### Script 2: Crear Funciones RPC
```sql
-- Copiar y pegar el contenido de: src/sql/admin_modules_functions.sql
```

#### Script 3: Configurar Políticas RLS
```sql
-- Copiar y pegar el contenido de: src/sql/admin_modules_rls.sql
```

### 1.3 Verificar Instalación

Ejecuta este script para verificar que todo se instaló correctamente:

```sql
-- Verificar tablas creadas
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
  'role_permissions',
  'route_permissions', 
  'user_activity_logs',
  'usage_statistics',
  'session_logs',
  'patient_assignments'
);

-- Verificar funciones RPC
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
  'get_user_permissions',
  'check_route_access',
  'log_user_activity',
  'assign_patient_to_psychologist'
);

-- Verificar datos iniciales
SELECT COUNT(*) as permisos_roles FROM role_permissions;
SELECT COUNT(*) as permisos_rutas FROM route_permissions;
```

## 🎯 PASO 2: Configurar Frontend

### 2.1 Verificar Dependencias

Los siguientes archivos deben estar presentes:

```
src/
├── services/
│   ├── userManagementService.js
│   ├── routePermissionsService.js
│   ├── appUsageService.js
│   └── patientAssignmentService.js
├── hooks/
│   ├── useUserManagement.js
│   ├── useRoutePermissions.js
│   ├── useUsageControl.js
│   └── usePatientAssignment.js
├── components/UserManagement/
│   ├── UserManagementPanel.jsx
│   ├── UserStatistics.jsx
│   ├── UserFilters.jsx
│   ├── PageAccessPanel.jsx
│   ├── PatientAssignmentPanel.jsx
│   ├── UsageControlPanel.jsx
│   └── UsageReportsPanel.jsx
└── pages/Configuracion/
    └── Configuracion.jsx (modificado)
```

### 2.2 Instalar Dependencias NPM

Si es necesario, instala las dependencias:

```bash
npm install react-toastify
```

## 🔧 PASO 3: Configuración de Permisos

### 3.1 Verificar Roles de Usuario

Asegúrate de que los usuarios tengan el campo `tipo_usuario` configurado correctamente:

```sql
-- Verificar usuarios existentes
SELECT id, nombre, apellido, tipo_usuario, activo 
FROM usuarios 
ORDER BY tipo_usuario, nombre;

-- Actualizar tipo de usuario si es necesario
UPDATE usuarios 
SET tipo_usuario = 'Administrador' 
WHERE id = 'TU_USER_ID_AQUI';
```

### 3.2 Configurar Permisos Iniciales

Los permisos básicos se crean automáticamente, pero puedes agregar más:

```sql
-- Agregar permisos personalizados
INSERT INTO role_permissions (role, permission, resource, description) VALUES
('Psicólogo', 'read', 'own_reports', 'Ver sus propios reportes'),
('Candidato', 'update', 'own_profile', 'Actualizar su perfil');
```

## 🧪 PASO 4: Pruebas

### 4.1 Probar Funcionalidades

1. **Login como Administrador**
   - Ve a `/configuracion`
   - Verifica que aparezcan todas las pestañas: Usuarios, Permisos, Asignaciones, Uso, Reportes

2. **Gestión de Usuarios**
   - Crear nuevo usuario
   - Editar usuario existente
   - Filtrar usuarios
   - Exportar lista

3. **Control de Acceso**
   - Crear permiso de ruta
   - Asignar permisos a roles
   - Verificar restricciones

4. **Asignaciones**
   - Asignar paciente a psicólogo
   - Ver estadísticas de asignaciones

### 4.2 Verificar Logs

```sql
-- Ver logs de actividad recientes
SELECT * FROM user_activity_logs 
ORDER BY created_at DESC 
LIMIT 10;

-- Ver estadísticas de uso
SELECT * FROM usage_statistics 
WHERE date = CURRENT_DATE;
```

## 🚨 Solución de Problemas

### Error: "relation does not exist"

Si aparece este error, verifica que:
1. Los scripts SQL se ejecutaron correctamente
2. Las tablas se crearon en el esquema `public`
3. El usuario tiene permisos sobre las tablas

### Error: "permission denied for function"

Ejecuta estos comandos para otorgar permisos:

```sql
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
```

### Error en Frontend: "Cannot read property"

Verifica que:
1. Los servicios están importados correctamente
2. Los hooks están siendo utilizados dentro de componentes React
3. Supabase está configurado correctamente

## 📊 Funcionalidades Disponibles

### Para Administradores
- ✅ Gestión completa de usuarios
- ✅ Control de permisos y acceso
- ✅ Asignación de pacientes
- ✅ Monitoreo de uso del sistema
- ✅ Generación de reportes
- ✅ Configuración del sistema

### Para Psicólogos
- ✅ Ver sus pacientes asignados
- ✅ Gestionar asignaciones propias
- ✅ Configuración personal

### Para Candidatos
- ✅ Configuración personal
- ✅ Cambio de contraseña

## 🔄 Mantenimiento

### Limpieza de Logs

Ejecuta periódicamente para limpiar logs antiguos:

```sql
SELECT cleanup_old_logs(90); -- Mantener 90 días
```

### Actualización de Estadísticas

Las estadísticas se actualizan automáticamente, pero puedes forzar una actualización:

```sql
SELECT update_usage_statistics('daily_active_users', 
  (SELECT COUNT(DISTINCT user_id) FROM session_logs WHERE DATE(login_time) = CURRENT_DATE)
);
```

## ✅ Checklist de Instalación

- [ ] Scripts SQL ejecutados en Supabase
- [ ] Tablas creadas correctamente
- [ ] Funciones RPC disponibles
- [ ] Políticas RLS configuradas
- [ ] Archivos frontend en su lugar
- [ ] Dependencias NPM instaladas
- [ ] Permisos de usuario configurados
- [ ] Pruebas básicas realizadas
- [ ] Logs funcionando correctamente

## 📞 Soporte

Si encuentras problemas durante la instalación:

1. Verifica los logs de Supabase
2. Revisa la consola del navegador
3. Confirma que todos los archivos están en su lugar
4. Verifica las variables de entorno de Supabase

---

**¡Instalación completada!** Los módulos de administración están listos para usar.
