import React from 'react';
import {
  RadarChart as RechartsRadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  Legend,
} from 'recharts';
import { Box, Typography, Paper } from '@mui/material';
import { useTheme } from '@mui/material/styles';

interface RadarChartData {
  subject: string;
  value: number;
  fullMark?: number;
}

interface RadarChartProps {
  data: RadarChartData[];
  title?: string;
  subtitle?: string;
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  fillOpacity?: number;
  strokeWidth?: number;
  colorScheme?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  maxValue?: number;
}

export const RadarChart: React.FC<RadarChartProps> = ({
  data,
  title,
  subtitle,
  height = 400,
  showGrid = true,
  showLegend = false,
  fillOpacity = 0.3,
  strokeWidth = 2,
  colorScheme = 'primary',
  maxValue,
}) => {
  const theme = useTheme();

  // Colores según el esquema de la guía de diseño
  const getColor = () => {
    switch (colorScheme) {
      case 'primary':
        return '#5A92C8';
      case 'secondary':
        return '#63B4A9';
      case 'success':
        return '#63B4A9';
      case 'warning':
        return '#DDA15E';
      case 'error':
        return '#F28A7C';
      default:
        return '#5A92C8';
    }
  };

  const color = getColor();

  // Configuración del tooltip personalizado
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Paper
          sx={{
            p: 2,
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(0, 0, 0, 0.06)',
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
          }}
        >
          <Typography variant="subtitle2" gutterBottom>
            {label}
          </Typography>
          {payload.map((entry: any, index: number) => (
            <Typography
              key={index}
              variant="body2"
              sx={{ color: entry.color, fontWeight: 500 }}
            >
              Puntuación: {entry.value}
              {entry.payload.fullMark && ` / ${entry.payload.fullMark}`}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  // Calcular el valor máximo si no se proporciona
  const calculatedMaxValue = maxValue || Math.max(...data.map(d => d.fullMark || d.value)) || 100;

  return (
    <Box>
      {(title || subtitle) && (
        <Box mb={2} textAlign="center">
          {title && (
            <Typography variant="h6" component="h3" gutterBottom>
              {title}
            </Typography>
          )}
          {subtitle && (
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
      )}
      
      <ResponsiveContainer width="100%" height={height}>
        <RechartsRadarChart data={data} margin={{ top: 20, right: 80, bottom: 20, left: 80 }}>
          {showGrid && (
            <PolarGrid 
              stroke="rgba(0, 0, 0, 0.1)"
              strokeWidth={1}
            />
          )}
          
          <PolarAngleAxis 
            dataKey="subject"
            tick={{ 
              fontSize: 12, 
              fill: theme.palette.text.secondary,
              fontWeight: 500,
            }}
            className="recharts-polar-angle-axis-tick"
          />
          
          <PolarRadiusAxis
            angle={90}
            domain={[0, calculatedMaxValue]}
            tick={{ 
              fontSize: 10, 
              fill: theme.palette.text.disabled,
            }}
            tickCount={5}
          />
          
          <Radar
            name="Puntuación"
            dataKey="value"
            stroke={color}
            fill={color}
            fillOpacity={fillOpacity}
            strokeWidth={strokeWidth}
            dot={{
              r: 4,
              fill: color,
              stroke: '#ffffff',
              strokeWidth: 2,
            }}
          />
          
          <Tooltip content={<CustomTooltip />} />
          
          {showLegend && (
            <Legend 
              wrapperStyle={{
                paddingTop: '20px',
                fontSize: '12px',
              }}
            />
          )}
        </RechartsRadarChart>
      </ResponsiveContainer>
      
      {/* Leyenda de interpretación */}
      <Box mt={2}>
        <Typography variant="caption" color="text.secondary" display="block" textAlign="center">
          Perfil holístico de evaluación - Valores más altos indican mayor presencia del rasgo
        </Typography>
      </Box>
    </Box>
  );
};

export default RadarChart;
