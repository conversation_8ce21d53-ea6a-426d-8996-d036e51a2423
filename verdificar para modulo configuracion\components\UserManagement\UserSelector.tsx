import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Chip,
  Divider,
  TextField,
  InputAdornment,
} from '@mui/material';
import {
  Person as PersonIcon,
  AdminPanelSettings as AdminIcon,
  Psychology as PsychologyIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { userManagementService, type UserProfile } from '../../services/userManagement';
import { useNotifier } from '../../hooks/useNotifier';

interface UserSelectorProps {
  open: boolean;
  onClose: () => void;
  onSave: (selectedUserIds: string[]) => Promise<void>;
  currentUserIds: string[];
  routeName: string;
}

const UserSelector: React.FC<UserSelectorProps> = ({
  open,
  onClose,
  onSave,
  currentUserIds,
  routeName,
}) => {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>(currentUserIds);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const { showError } = useNotifier();

  // Cargar usuarios al abrir el diálogo
  useEffect(() => {
    if (open) {
      loadUsers();
      setSelectedUserIds(currentUserIds);
    }
  }, [open, currentUserIds]);

  const loadUsers = async () => {
    setLoading(true);
    try {
      const { data, error } = await userManagementService.getAllUsers();
      
      if (error) {
        showError(`Error al cargar usuarios: ${error}`);
        return;
      }

      setUsers(data || []);
    } catch (error) {
      showError('Error inesperado al cargar usuarios');
      console.error('Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleUser = (userId: string) => {
    setSelectedUserIds(prev => 
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      await onSave(selectedUserIds);
      onClose();
    } catch (error) {
      // El error ya se maneja en el componente padre
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setSelectedUserIds(currentUserIds);
    onClose();
  };

  // Filtrar usuarios por término de búsqueda
  const filteredUsers = users.filter(user =>
    user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Obtener icono por rol
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'administrador':
        return <AdminIcon fontSize="small" color="error" />;
      case 'psicologo':
        return <PsychologyIcon fontSize="small" color="primary" />;
      case 'paciente':
        return <PersonIcon fontSize="small" color="success" />;
      default:
        return <PersonIcon fontSize="small" />;
    }
  };

  // Obtener color del chip por rol
  const getRoleChipColor = (role: string): "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning" => {
    switch (role) {
      case 'administrador':
        return 'error';
      case 'psicologo':
        return 'primary';
      case 'paciente':
        return 'success';
      default:
        return 'default';
    }
  };

  const formatRole = (role: string) => {
    const roleNames = {
      administrador: 'Administrador',
      psicologo: 'Psicólogo',
      paciente: 'Paciente',
    };
    return roleNames[role as keyof typeof roleNames] || role;
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleCancel}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: '600px' }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">
            Asignar Usuarios Específicos
          </Typography>
          <Chip 
            label={routeName}
            color="primary"
            variant="outlined"
          />
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Selecciona usuarios específicos que tendrán acceso a esta página, 
          independientemente de su rol.
        </Typography>
      </DialogTitle>

      <DialogContent>
        {/* Barra de búsqueda */}
        <TextField
          fullWidth
          placeholder="Buscar usuarios por nombre o email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />

        {/* Resumen de selección */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {selectedUserIds.length} usuario(s) seleccionado(s)
          </Typography>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Lista de usuarios */}
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
            <Typography variant="body2" sx={{ ml: 2 }}>
              Cargando usuarios...
            </Typography>
          </Box>
        ) : filteredUsers.length === 0 ? (
          <Alert severity="info">
            {searchTerm ? 'No se encontraron usuarios que coincidan con la búsqueda.' : 'No hay usuarios disponibles.'}
          </Alert>
        ) : (
          <List sx={{ maxHeight: '400px', overflow: 'auto' }}>
            {filteredUsers.map((user) => (
              <ListItem
                key={user.id}
                disablePadding
                sx={{
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 1,
                  mb: 1,
                }}
              >
                <ListItemButton
                  onClick={() => handleToggleUser(user.id)}
                  sx={{
                    '&:hover': {
                      backgroundColor: 'action.hover',
                    },
                  }}
                >
                <ListItemIcon>
                  <Checkbox
                    checked={selectedUserIds.includes(user.id)}
                    onChange={() => handleToggleUser(user.id)}
                    color="primary"
                  />
                </ListItemIcon>
                
                <ListItemIcon>
                  {getRoleIcon(user.role)}
                </ListItemIcon>

                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography variant="body1">
                        {user.full_name || 'Sin nombre'}
                      </Typography>
                      <Chip
                        label={formatRole(user.role)}
                        size="small"
                        color={getRoleChipColor(user.role)}
                        variant="outlined"
                      />
                    </Box>
                  }
                  secondary={
                    <React.Fragment>
                      <Typography variant="body2" color="text.secondary" component="span" display="block">
                        {user.email}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" component="span" display="block">
                        Creado: {new Date(user.created_at).toLocaleDateString()}
                      </Typography>
                    </React.Fragment>
                  }
                  primaryTypographyProps={{ component: 'div' }}
                  secondaryTypographyProps={{ component: 'div' }}
                />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button 
          onClick={handleCancel}
          disabled={saving}
        >
          Cancelar
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={saving || loading}
          startIcon={saving ? <CircularProgress size={16} /> : null}
        >
          {saving ? 'Guardando...' : 'Guardar Cambios'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UserSelector;
