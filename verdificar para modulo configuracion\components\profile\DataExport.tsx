import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Alert,
  Chip,
  Divider,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Download as DownloadIcon,
  FileDownload as FileDownloadIcon,
  PictureAsPdf as PdfIcon,
  TableChart as CsvIcon,
  Code as JsonIcon,
  History as HistoryIcon,
  Person as PersonIcon,
  Assessment as ReportIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';
import { supabase } from '../../lib/supabaseClient';

interface ExportOptions {
  includeProfile: boolean;
  includeReports: boolean;
  includeHistory: boolean;
  includeNotifications: boolean;
  includeAuditLog: boolean;
  format: 'pdf' | 'csv' | 'json';
  dateRange: 'all' | '30days' | '90days' | '1year';
}

const DataExport: React.FC = () => {
  const [options, setOptions] = useState<ExportOptions>({
    includeProfile: true,
    includeReports: true,
    includeHistory: false,
    includeNotifications: false,
    includeAuditLog: false,
    format: 'pdf',
    dateRange: '1year',
  });
  const [exporting, setExporting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleOptionChange = (key: keyof ExportOptions, value: any) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const getDateRangeFilter = () => {
    const now = new Date();
    switch (options.dateRange) {
      case '30days':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      case '90days':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      case '1year':
        return new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      default:
        return null;
    }
  };

  const exportData = async () => {
    try {
      setExporting(true);
      setError(null);
      setSuccess(null);

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuario no autenticado');

      const exportData: any = {
        exportInfo: {
          userId: user.id,
          exportDate: new Date().toISOString(),
          format: options.format,
          dateRange: options.dateRange,
        },
        data: {}
      };

      const dateFilter = getDateRangeFilter();

      // Exportar perfil
      if (options.includeProfile) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        
        exportData.data.profile = profile;
      }

      // Exportar reportes
      if (options.includeReports) {
        let query = supabase
          .from('reports')
          .select('*')
          .eq('patient_id', user.id);

        if (dateFilter) {
          query = query.gte('created_at', dateFilter.toISOString());
        }

        const { data: reports } = await query;
        exportData.data.reports = reports;
      }

      // Exportar historial de auditoría
      if (options.includeAuditLog) {
        let query = supabase
          .from('audit_log')
          .select('*')
          .eq('usuario_id', user.id);

        if (dateFilter) {
          query = query.gte('created_at', dateFilter.toISOString());
        }

        const { data: auditLog } = await query;
        exportData.data.auditLog = auditLog;
      }

      // Exportar preferencias de notificaciones
      if (options.includeNotifications) {
        const { data: notifications } = await supabase
          .from('notification_preferences')
          .select('*')
          .eq('user_id', user.id)
          .single();
        
        exportData.data.notificationPreferences = notifications;
      }

      // Generar archivo según formato
      await generateFile(exportData);

      setSuccess('¡Datos exportados correctamente!');
    } catch (error) {
      console.error('Error exporting data:', error);
      setError(error instanceof Error ? error.message : 'Error al exportar datos');
    } finally {
      setExporting(false);
    }
  };

  const generateFile = async (data: any) => {
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `maci-ii-export-${timestamp}`;

    switch (options.format) {
      case 'json':
        downloadJSON(data, `${filename}.json`);
        break;
      case 'csv':
        downloadCSV(data, `${filename}.csv`);
        break;
      case 'pdf':
        await generatePDF(data, `${filename}.pdf`);
        break;
    }
  };

  const downloadJSON = (data: any, filename: string) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const downloadCSV = (data: any, filename: string) => {
    let csvContent = '';
    
    // Convertir datos a CSV
    if (data.data.profile) {
      csvContent += 'PERFIL DE USUARIO\n';
      csvContent += Object.entries(data.data.profile)
        .map(([key, value]) => `${key},${value}`)
        .join('\n');
      csvContent += '\n\n';
    }

    if (data.data.reports && data.data.reports.length > 0) {
      csvContent += 'REPORTES\n';
      const headers = Object.keys(data.data.reports[0]).join(',');
      csvContent += headers + '\n';
      csvContent += data.data.reports
        .map((report: any) => Object.values(report).join(','))
        .join('\n');
      csvContent += '\n\n';
    }

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const generatePDF = async (data: any, filename: string) => {
    // Simulación de generación PDF (en producción usarías jsPDF o similar)
    const pdfContent = `
      MACI-II - Exportación de Datos
      ==============================
      
      Usuario: ${data.data.profile?.email || 'N/A'}
      Fecha de exportación: ${new Date().toLocaleDateString('es-ES')}
      
      ${data.data.profile ? `
      PERFIL:
      - Nombre: ${data.data.profile.full_name || 'N/A'}
      - Email: ${data.data.profile.email || 'N/A'}
      - Rol: ${data.data.profile.rol || 'N/A'}
      - Fecha de registro: ${data.data.profile.created_at ? new Date(data.data.profile.created_at).toLocaleDateString('es-ES') : 'N/A'}
      ` : ''}
      
      ${data.data.reports ? `
      REPORTES: ${data.data.reports.length} encontrados
      ` : ''}
      
      ${data.data.auditLog ? `
      HISTORIAL DE AUDITORÍA: ${data.data.auditLog.length} entradas
      ` : ''}
    `;

    const blob = new Blob([pdfContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getSelectedCount = () => {
    return Object.values(options).filter((value, index) => 
      index < 5 && value === true
    ).length;
  };

  return (
    <Card elevation={3} sx={{ borderRadius: 2 }}>
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DownloadIcon color="primary" />
            <Typography variant="h6">Exportación de Datos</Typography>
          </Box>
        }
        subheader="Descarga una copia de tus datos personales"
        action={
          <Chip
            label={`${getSelectedCount()} categorías`}
            color="primary"
            variant="outlined"
          />
        }
      />
      <Divider />
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        <Typography variant="h6" gutterBottom>
          Datos a incluir:
        </Typography>
        
        <FormGroup sx={{ mb: 3 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={options.includeProfile}
                onChange={(e) => handleOptionChange('includeProfile', e.target.checked)}
              />
            }
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PersonIcon fontSize="small" />
                <Typography>Información del perfil</Typography>
              </Box>
            }
          />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={options.includeReports}
                onChange={(e) => handleOptionChange('includeReports', e.target.checked)}
              />
            }
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ReportIcon fontSize="small" />
                <Typography>Reportes generados</Typography>
              </Box>
            }
          />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={options.includeHistory}
                onChange={(e) => handleOptionChange('includeHistory', e.target.checked)}
              />
            }
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <HistoryIcon fontSize="small" />
                <Typography>Historial de actividad</Typography>
              </Box>
            }
          />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={options.includeNotifications}
                onChange={(e) => handleOptionChange('includeNotifications', e.target.checked)}
              />
            }
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <NotificationsIcon fontSize="small" />
                <Typography>Preferencias de notificaciones</Typography>
              </Box>
            }
          />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={options.includeAuditLog}
                onChange={(e) => handleOptionChange('includeAuditLog', e.target.checked)}
              />
            }
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SecurityIcon fontSize="small" />
                <Typography>Registro de auditoría</Typography>
              </Box>
            }
          />
        </FormGroup>

        <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
          <FormControl sx={{ minWidth: 120 }}>
            <InputLabel>Formato</InputLabel>
            <Select
              value={options.format}
              label="Formato"
              onChange={(e) => handleOptionChange('format', e.target.value)}
            >
              <MenuItem value="pdf">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PdfIcon fontSize="small" />
                  PDF
                </Box>
              </MenuItem>
              <MenuItem value="csv">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CsvIcon fontSize="small" />
                  CSV
                </Box>
              </MenuItem>
              <MenuItem value="json">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <JsonIcon fontSize="small" />
                  JSON
                </Box>
              </MenuItem>
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Período</InputLabel>
            <Select
              value={options.dateRange}
              label="Período"
              onChange={(e) => handleOptionChange('dateRange', e.target.value)}
            >
              <MenuItem value="30days">Últimos 30 días</MenuItem>
              <MenuItem value="90days">Últimos 90 días</MenuItem>
              <MenuItem value="1year">Último año</MenuItem>
              <MenuItem value="all">Todo el historial</MenuItem>
            </Select>
          </FormControl>
        </Box>

        <Button
          variant="contained"
          startIcon={exporting ? <CircularProgress size={20} /> : <FileDownloadIcon />}
          onClick={exportData}
          disabled={exporting || getSelectedCount() === 0}
          sx={{
            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            mb: 2,
          }}
        >
          {exporting ? 'Exportando...' : 'Exportar Datos'}
        </Button>

        <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            ℹ️ Información sobre la exportación:
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • Los datos se descargan directamente a tu dispositivo<br/>
            • No se almacenan copias en servidores externos<br/>
            • El archivo incluye solo los datos que selecciones<br/>
            • Puedes usar esta función para respaldo o portabilidad
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default DataExport;
