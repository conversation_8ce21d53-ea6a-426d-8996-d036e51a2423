import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
  Ty<PERSON>graphy,
  Switch,
  FormControlLabel,
  Button,
  Box,
  Alert,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  NotificationsActive as NotificationsActiveIcon,
  NotificationsOff as NotificationsOffIcon,
  Security as SecurityIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { pushNotificationService } from '../../services/pushNotificationService';

const PushNotificationSettings: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [subscribing, setSubscribing] = useState(false);
  const [status, setStatus] = useState({
    supported: false,
    permission: 'default' as NotificationPermission,
    registered: false,
    subscribed: false,
  });
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    checkPushStatus();
  }, []);

  const checkPushStatus = async () => {
    try {
      setLoading(true);
      const currentStatus = pushNotificationService.getStatus();
      const isSubscribed = await pushNotificationService.isSubscribed();
      
      setStatus({
        ...currentStatus,
        subscribed: isSubscribed,
      });
    } catch (error) {
      console.error('Error checking push status:', error);
      setError('Error al verificar el estado de las notificaciones');
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async () => {
    try {
      setSubscribing(true);
      setError(null);
      setSuccess(null);

      const subscription = await pushNotificationService.subscribe();
      
      if (subscription) {
        setSuccess('¡Notificaciones push habilitadas correctamente!');
        await checkPushStatus();
        
        // Mostrar notificación de prueba
        setTimeout(() => {
          pushNotificationService.showLocalNotification({
            title: 'MACI-II',
            body: '¡Notificaciones push configuradas correctamente!',
            tag: 'welcome-notification',
            requireInteraction: false,
          });
        }, 1000);
      } else {
        setError('No se pudo habilitar las notificaciones push');
      }
    } catch (error) {
      console.error('Error subscribing to push:', error);
      setError(error instanceof Error ? error.message : 'Error al habilitar notificaciones');
    } finally {
      setSubscribing(false);
    }
  };

  const handleUnsubscribe = async () => {
    try {
      setSubscribing(true);
      setError(null);
      setSuccess(null);

      const success = await pushNotificationService.unsubscribe();
      
      if (success) {
        setSuccess('Notificaciones push deshabilitadas');
        await checkPushStatus();
      } else {
        setError('No se pudo deshabilitar las notificaciones push');
      }
    } catch (error) {
      console.error('Error unsubscribing from push:', error);
      setError('Error al deshabilitar notificaciones');
    } finally {
      setSubscribing(false);
    }
  };

  const handleTestNotification = async () => {
    try {
      await pushNotificationService.showLocalNotification({
        title: 'Notificación de Prueba',
        body: 'Esta es una notificación de prueba de MACI-II',
        tag: 'test-notification',
        requireInteraction: false,
        actions: [
          { action: 'view', title: 'Ver' },
          { action: 'dismiss', title: 'Descartar' }
        ]
      });
      setSuccess('Notificación de prueba enviada');
    } catch (error) {
      setError('Error al enviar notificación de prueba');
    }
  };

  const getPermissionStatus = () => {
    switch (status.permission) {
      case 'granted':
        return { icon: <CheckCircleIcon color="success" />, text: 'Concedidos', color: 'success' };
      case 'denied':
        return { icon: <ErrorIcon color="error" />, text: 'Denegados', color: 'error' };
      default:
        return { icon: <WarningIcon color="warning" />, text: 'Pendientes', color: 'warning' };
    }
  };

  const permissionStatus = getPermissionStatus();

  if (loading) {
    return (
      <Card elevation={3} sx={{ borderRadius: 2 }}>
        <CardContent sx={{ textAlign: 'center', py: 4 }}>
          <CircularProgress />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Verificando soporte de notificaciones...
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card elevation={3} sx={{ borderRadius: 2 }}>
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <NotificationsIcon color="primary" />
            <Typography variant="h6">Notificaciones Push</Typography>
          </Box>
        }
        subheader="Configuración de notificaciones en tiempo real"
        action={
          <Chip
            icon={status.subscribed ? <NotificationsActiveIcon /> : <NotificationsOffIcon />}
            label={status.subscribed ? 'Activas' : 'Inactivas'}
            color={status.subscribed ? 'success' : 'default'}
            variant="outlined"
          />
        }
      />
      <Divider />
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {!status.supported && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            Tu navegador no soporta notificaciones push
          </Alert>
        )}

        <List sx={{ mb: 2 }}>
          <ListItem>
            <ListItemIcon>
              <SecurityIcon color={status.supported ? 'success' : 'error'} />
            </ListItemIcon>
            <ListItemText
              primary="Soporte del navegador"
              secondary={status.supported ? 'Compatible' : 'No compatible'}
            />
            <Chip
              label={status.supported ? 'Sí' : 'No'}
              color={status.supported ? 'success' : 'error'}
              size="small"
            />
          </ListItem>

          <ListItem>
            <ListItemIcon>
              {permissionStatus.icon}
            </ListItemIcon>
            <ListItemText
              primary="Permisos de notificación"
              secondary="Estado de los permisos del navegador"
            />
            <Chip
              label={permissionStatus.text}
              color={permissionStatus.color as any}
              size="small"
            />
          </ListItem>

          <ListItem>
            <ListItemIcon>
              <NotificationsIcon color={status.subscribed ? 'success' : 'action'} />
            </ListItemIcon>
            <ListItemText
              primary="Estado de suscripción"
              secondary={status.subscribed ? 'Suscrito a notificaciones push' : 'No suscrito'}
            />
            <Chip
              label={status.subscribed ? 'Suscrito' : 'No suscrito'}
              color={status.subscribed ? 'success' : 'default'}
              size="small"
            />
          </ListItem>
        </List>

        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          {status.supported && status.permission !== 'denied' && (
            <>
              {!status.subscribed ? (
                <Button
                  variant="contained"
                  startIcon={subscribing ? <CircularProgress size={20} /> : <NotificationsActiveIcon />}
                  onClick={handleSubscribe}
                  disabled={subscribing}
                  sx={{
                    background: 'linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%)',
                  }}
                >
                  {subscribing ? 'Habilitando...' : 'Habilitar Notificaciones'}
                </Button>
              ) : (
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={subscribing ? <CircularProgress size={20} /> : <NotificationsOffIcon />}
                  onClick={handleUnsubscribe}
                  disabled={subscribing}
                >
                  {subscribing ? 'Deshabilitando...' : 'Deshabilitar'}
                </Button>
              )}

              {status.subscribed && (
                <Button
                  variant="outlined"
                  startIcon={<InfoIcon />}
                  onClick={handleTestNotification}
                >
                  Probar Notificación
                </Button>
              )}
            </>
          )}

          <Button
            variant="text"
            onClick={checkPushStatus}
            disabled={loading}
          >
            Actualizar Estado
          </Button>
        </Box>

        {status.permission === 'denied' && (
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Permisos denegados:</strong> Para habilitar las notificaciones, ve a la configuración de tu navegador y permite las notificaciones para este sitio.
            </Typography>
          </Alert>
        )}

        <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            ℹ️ Información sobre notificaciones push:
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • Las notificaciones push te permiten recibir alertas en tiempo real<br/>
            • Funciona incluso cuando la aplicación está cerrada<br/>
            • Puedes configurar qué tipos de notificaciones recibir<br/>
            • Tus datos de suscripción se almacenan de forma segura
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default PushNotificationSettings;
