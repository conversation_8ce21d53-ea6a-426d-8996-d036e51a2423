import { useUIStore } from '../stores/useUIStore';

/**
 * Hook de compatibilidad que mantiene la misma API que useThemeContext
 * Permite migrar gradualmente sin romper componentes existentes
 */
export const useThemeContext = () => {
  const { theme, toggleTheme } = useUIStore();

  return {
    mode: theme,
    toggleTheme,
  };
};

/**
 * Hook adicional para acceso directo a funciones de tema
 */
export const useThemeActions = () => {
  const { theme, toggleTheme, setTheme } = useUIStore();

  return {
    currentTheme: theme,
    toggleTheme,
    setTheme,
    isDark: theme === 'dark',
    isLight: theme === 'light',
  };
};

export default useThemeContext;
