import React from 'react';
import { Dialog, DialogProps, Slide, Fade, Grow, Zoom } from '@mui/material';
import { TransitionProps } from '@mui/material/transitions';
import { motion, AnimatePresence } from 'framer-motion';

type AnimationType = 'slide' | 'fade' | 'grow' | 'zoom' | 'motion';

interface AnimatedDialogProps extends Omit<DialogProps, 'TransitionComponent'> {
  animationType?: AnimationType;
  direction?: 'up' | 'down' | 'left' | 'right';
}

// Transición con Slide
const SlideTransition = React.forwardRef<
  unknown,
  TransitionProps & { direction?: 'up' | 'down' | 'left' | 'right' }
>(function Transition(props, ref) {
  const { direction = 'up', ...other } = props;
  return <Slide direction={direction} ref={ref} {...other} />;
});

// Transición con Fade
const FadeTransition = React.forwardRef<unknown, TransitionProps>(
  function Transition(props, ref) {
    return <Fade ref={ref} {...props} />;
  },
);

// Transición con Grow
const GrowTransition = React.forwardRef<unknown, TransitionProps>(
  function Transition(props, ref) {
    return <Grow ref={ref} {...props} />;
  },
);

// Transición con Zoom
const ZoomTransition = React.forwardRef<unknown, TransitionProps>(
  function Transition(props, ref) {
    return <Zoom ref={ref} {...props} />;
  },
);

// Wrapper para Framer Motion
const MotionDialogWrapper: React.FC<{
  children: React.ReactNode;
  open: boolean;
}> = ({ children, open }) => {
  return (
    <AnimatePresence>
      {open && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 50 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 50 }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 30,
          }}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 1300,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              backdropFilter: 'blur(4px)',
            }}
          />
          <motion.div
            style={{
              position: 'relative',
              zIndex: 1,
              maxWidth: '90vw',
              maxHeight: '90vh',
              overflow: 'auto',
            }}
          >
            {children}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export const AnimatedDialog: React.FC<AnimatedDialogProps> = ({
  animationType = 'motion',
  direction = 'up',
  children,
  open,
  ...props
}) => {
  const getTransitionComponent = () => {
    switch (animationType) {
      case 'slide':
        return (props: TransitionProps) => (
          <SlideTransition {...props} direction={direction} />
        );
      case 'fade':
        return FadeTransition;
      case 'grow':
        return GrowTransition;
      case 'zoom':
        return ZoomTransition;
      case 'motion':
        return undefined; // Usaremos el wrapper de Framer Motion
      default:
        return FadeTransition;
    }
  };

  if (animationType === 'motion') {
    return (
      <MotionDialogWrapper open={open}>
        <Dialog
          {...props}
          open={true} // Siempre true porque el control lo maneja el wrapper
          TransitionComponent={undefined}
          PaperProps={{
            ...props.PaperProps,
            style: {
              margin: 0,
              ...props.PaperProps?.style,
            },
          }}
        >
          {children}
        </Dialog>
      </MotionDialogWrapper>
    );
  }

  return (
    <Dialog
      {...props}
      open={open}
      TransitionComponent={getTransitionComponent()}
      PaperProps={{
        ...props.PaperProps,
        component: motion.div,
        initial: { opacity: 0, scale: 0.9 },
        animate: { opacity: 1, scale: 1 },
        exit: { opacity: 0, scale: 0.9 },
        transition: { duration: 0.2 },
        ...props.PaperProps,
      }}
    >
      {children}
    </Dialog>
  );
};

// Hook para animaciones de lista
export const useListAnimation = (delay: number = 0.1) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: delay,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 24,
      },
    },
  };

  return { containerVariants, itemVariants };
};

// Componente para animaciones de entrada de página
export const PageTransition: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{
        type: 'spring',
        stiffness: 300,
        damping: 30,
      }}
    >
      {children}
    </motion.div>
  );
};

// Componente para hover effects en cards
export const HoverCard: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <motion.div
      className={className}
      whileHover={{
        y: -4,
        boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
        transition: { duration: 0.2 },
      }}
      whileTap={{ scale: 0.98 }}
    >
      {children}
    </motion.div>
  );
};
