import * as React from 'react';
import * as RadioGroupPrimitive from '@radix-ui/react-radio-group';
import { styled } from '@mui/material/styles';

const StyledRadioGroup = styled(RadioGroupPrimitive.Root)({
  display: 'flex',
  flexDirection: 'row',
  gap: '1rem',
});

const StyledRadioGroupItem = styled(RadioGroupPrimitive.Item)({
  width: 20,
  height: 20,
  borderRadius: '100%',
  border: '2px solid #d1d5db',
  '&:hover': {
    borderColor: '#9ca3af',
  },
  '&[data-state="checked"]': {
    borderColor: '#2563eb',
    backgroundColor: '#2563eb',
  },
});

const StyledRadioGroupIndicator = styled(RadioGroupPrimitive.Indicator)({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  height: '100%',
  position: 'relative',
  '&::after': {
    content: '""',
    display: 'block',
    width: 10,
    height: 10,
    borderRadius: '50%',
    backgroundColor: 'white',
  },
});

export const RadioGroup = StyledRadioGroup;
export const RadioGroupItem = React.forwardRef<
  React.ElementRef<typeof StyledRadioGroupItem>,
  React.ComponentPropsWithoutRef<typeof StyledRadioGroupItem>
>((props, ref) => (
  <StyledRadioGroupItem ref={ref} {...props}>
    <StyledRadioGroupIndicator />
  </StyledRadioGroupItem>
));
