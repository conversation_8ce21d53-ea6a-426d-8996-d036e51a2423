import React, { useEffect, useCallback } from 'react';
import { usePatientsStore } from '../stores';
import { Patient } from '../types/patient';
import {
  Autocomplete,
  TextField,
  Box,
  Alert,
  CircularProgress,
  Typography,
  Avatar,
  Paper,
  InputAdornment,
  Button,
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import MaleIcon from '@mui/icons-material/Male';
import FemaleIcon from '@mui/icons-material/Female';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
// PatientSelect.css removed - using Material-UI styling instead

interface PatientSelectProps {
  onPatientSelect: (patient: Patient) => void;
}

export const PatientSelect: React.FC<PatientSelectProps> = ({
  onPatientSelect,
}) => {
  const {
    items: patients,
    status,
    error,
    selectedPatient,
    fetchPatients,
    setSelectedPatient,
    clearError,
  } = usePatientsStore();

  const [filteredPatients, setFilteredPatients] = React.useState<Patient[]>([]);
  const [inputValue, setInputValue] = React.useState('');
  const [searchTimeout, setSearchTimeout] =
    React.useState<NodeJS.Timeout | null>(null);

  const loading = status === 'loading';

  // Function to normalize text (remove accents, lowercase, remove special characters)
  const normalizeText = useCallback((text: string): string => {
    return text
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s]/g, '');
  }, []);

  // Function to filter patients based on search text
  const filterPatients = useCallback(
    (searchText: string, patientList: Patient[]) => {
      if (!searchText.trim()) {
        return patientList;
      }

      const normalizedSearch = normalizeText(searchText);

      if (!Array.isArray(patientList)) {
        return [];
      }

      return patientList.filter((patient) => {
        const normalizedName = normalizeText(patient.name);
        return normalizedName.includes(normalizedSearch);
      });
    },
    [normalizeText],
  );

  // Handle input changes with debounce
  const handleInputChange = useCallback(
    (event: React.SyntheticEvent, value: string) => {
      setInputValue(value);

      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }

      const timeout = setTimeout(() => {
        setFilteredPatients(filterPatients(value, patients));
      }, 300);

      setSearchTimeout(timeout as unknown as NodeJS.Timeout);
    },
    [patients, filterPatients, searchTimeout],
  );

  // Clear timeout on component unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  // Load patients on component mount
  useEffect(() => {
    if (patients.length === 0 && status === 'idle') {
      fetchPatients();
    }
  }, [patients.length, status, fetchPatients]);

  // Update filtered patients when patients change
  useEffect(() => {
    setFilteredPatients(filterPatients(inputValue, patients));
  }, [patients, inputValue, filterPatients]);

  // Reset selected patient when input is cleared
  useEffect(() => {
    if (!inputValue) {
      setSelectedPatient(null);
    }
  }, [inputValue]);

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        position: 'relative',
        padding: '1.5rem 0',
        backdropFilter: 'blur(10px)',
        borderRadius: '16px',
        overflow: 'hidden',
      }}
      className="patient-select-container"
    >
      <Typography
        variant="h5"
        sx={{
          mb: 3,
          fontWeight: 600,
          color: '#2c3e50',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 1.5,
          textAlign: 'center',
          fontSize: '1.4rem',
          letterSpacing: '0.5px',
        }}
      >
        <CheckCircleIcon sx={{ color: '#27ae60', fontSize: '1.8rem' }} />
        Seleccione un paciente para comenzar
      </Typography>

      {selectedPatient ? (
        <Paper
          elevation={3}
          sx={{
            display: 'flex',
            alignItems: 'center',
            padding: 3,
            width: '100%',
            maxWidth: 520,
            margin: '20px auto',
            borderRadius: '16px',
            backgroundColor: '#f8f9fa',
            border: '1px solid #e9ecef',
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: '0 8px 25px rgba(0,0,0,0.12)',
            },
          }}
        >
          <Avatar
            sx={{
              bgcolor: selectedPatient.gender === 'M' ? '#3498db' : '#2196f3',
              mr: 3,
              width: 64,
              height: 64,
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
              border: '3px solid white',
            }}
          >
            {selectedPatient.gender === 'M' ? (
              <MaleIcon fontSize="large" />
            ) : (
              <FemaleIcon fontSize="large" />
            )}
          </Avatar>
          <Box sx={{ textAlign: 'left' }}>
            <Typography
              variant="h6"
              component="div"
              sx={{
                fontWeight: 600,
                color: '#2c3e50',
                fontSize: '1.2rem',
                mb: 0.5,
              }}
            >
              {selectedPatient.name}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: '#7f8c8d',
                fontSize: '0.95rem',
                fontStyle: 'italic',
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
              }}
            >
              <CheckCircleIcon sx={{ fontSize: '1rem', color: '#27ae60' }} />
              Listo para evaluación
            </Typography>
          </Box>
        </Paper>
      ) : (
        <Paper
          elevation={2}
          sx={{
            width: '100%',
            maxWidth: 520,
            margin: '0 auto',
            padding: 3,
            borderRadius: '20px',
            backgroundColor: '#ffffff',
            border: '1px solid #e9ecef',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              mb: 3,
              pb: 2,
              borderBottom: '1px solid #e9ecef',
              position: 'relative',
            }}
          >
            <SearchIcon
              sx={{
                color: '#3498db',
                mr: 2,
                fontSize: '28px',
              }}
            />
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: '#2c3e50',
                letterSpacing: '0.5px',
                fontSize: '1.1rem',
              }}
            >
              Buscar paciente por nombre
            </Typography>
          </Box>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              position: 'relative',
              borderRadius: '12px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
              overflow: 'hidden',
              backgroundColor: '#f8f9fa',
              border: '1px solid #e9ecef',
              mb: 2,
              transition: 'all 0.2s ease',
              '&:focus-within': {
                boxShadow: '0 4px 12px rgba(52, 152, 219, 0.15)',
                borderColor: '#3498db',
              },
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                flex: 1,
                position: 'relative',
              }}
            >
              <Autocomplete
                options={filteredPatients}
                getOptionLabel={(option) => option.name}
                loading={loading}
                inputValue={inputValue}
                onInputChange={handleInputChange}
                onChange={(event, newValue) => {
                  if (newValue) {
                    setSelectedPatient(newValue);
                    onPatientSelect(newValue);
                  }
                }}
                noOptionsText={
                  error
                    ? 'Error al cargar pacientes'
                    : 'No se encontraron pacientes'
                }
                loadingText="Buscando pacientes..."
                filterOptions={(x) => x} // Disable automatic filtering, we do it manually
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="Buscar paciente..."
                    InputProps={{
                      ...params.InputProps,
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon sx={{ color: '#7f8c8d' }} />
                        </InputAdornment>
                      ),
                      sx: {
                        pl: 1,
                        backgroundColor: 'transparent',
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: 'none',
                        },
                        '& input': {
                          fontSize: '1rem',
                          color: '#2c3e50',
                        },
                        '& input::placeholder': {
                          color: '#95a5a6',
                          opacity: 1,
                        },
                      },
                    }}
                  />
                )}
                renderOption={(props, option) => {
                  const { key, ...otherProps } = props;
                  return (
                    <Box
                      component="li"
                      key={key}
                      {...otherProps}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        borderBottom: '1px solid #f1f2f6',
                        py: 2.5,
                        px: 3,
                        transition: 'all 0.3s ease',
                        cursor: 'pointer',
                        '&:hover': {
                          backgroundColor:
                            option.gender === 'M'
                              ? 'rgba(52, 152, 219, 0.08)'
                              : 'rgba(33, 150, 243, 0.08)',
                          transform: 'translateX(4px)',
                          borderRadius: '8px',
                        },
                        '&:last-child': {
                          borderBottom: 'none',
                        },
                      }}
                    >
                      <Avatar
                        sx={{
                          bgcolor:
                            option.gender === 'M' ? '#3498db' : '#2196f3',
                          mr: 2.5,
                          width: 44,
                          height: 44,
                          boxShadow: '0 3px 10px rgba(0, 0, 0, 0.12)',
                          border: '2px solid white',
                        }}
                      >
                        {option.gender === 'M' ? <MaleIcon /> : <FemaleIcon />}
                      </Avatar>
                      <Typography
                        variant="body1"
                        sx={{
                          fontWeight: 500,
                          color: '#2c3e50',
                          fontSize: '1rem',
                        }}
                      >
                        {option.name}
                      </Typography>
                    </Box>
                  );
                }}
                sx={{ width: '100%' }}
              />
            </Box>
          </Box>
          {filteredPatients.length === 0 && !loading && !!inputValue && (
            <Box
              sx={{
                textAlign: 'center',
                py: 3,
                px: 2,
                backgroundColor: '#fff5f5',
                borderRadius: '8px',
                border: '1px solid #fed7d7',
                mt: 2,
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: '#e53e3e',
                  fontWeight: 500,
                  fontSize: '0.95rem',
                }}
              >
                No se encontraron pacientes con ese nombre
              </Typography>
            </Box>
          )}
        </Paper>
      )}


    </Box>
  );
};

export default PatientSelect;
