import React, { useState } from 'react';
import {
  Box,
  Button,
  Menu,
  MenuItem,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Divider,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  PersonAdd as PersonAddIcon,
  PersonOff as PersonOffIcon,
  AdminPanelSettings as AdminIcon,
  Psychology as PsychologyIcon,
  Person as PersonIcon,
  Delete as DeleteIcon,
  Check as CheckIcon,
  Close as CloseIcon,
} from '@mui/icons-material';

interface BulkActionsToolbarProps {
  selectedUsers: string[];
  onClearSelection: () => void;
  onBulkRoleChange: (userIds: string[], newRole: string) => Promise<void>;
  onBulkStatusChange: (userIds: string[], isActive: boolean) => Promise<void>;
  onBulkDelete: (userIds: string[]) => Promise<void>;
}

export const BulkActionsToolbar: React.FC<BulkActionsToolbarProps> = ({
  selectedUsers,
  onClearSelection,
  onBulkRoleChange,
  onBulkStatusChange,
  onBulkDelete,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [roleChangeDialog, setRoleChangeDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [selectedRole, setSelectedRole] = useState('');
  const [processing, setProcessing] = useState(false);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleRoleChange = async () => {
    if (!selectedRole) return;
    
    setProcessing(true);
    try {
      await onBulkRoleChange(selectedUsers, selectedRole);
      setRoleChangeDialog(false);
      setSelectedRole('');
      onClearSelection();
    } catch (error) {
      console.error('Error changing roles:', error);
    } finally {
      setProcessing(false);
    }
  };

  const handleStatusChange = async (isActive: boolean) => {
    setProcessing(true);
    try {
      await onBulkStatusChange(selectedUsers, isActive);
      onClearSelection();
    } catch (error) {
      console.error('Error changing status:', error);
    } finally {
      setProcessing(false);
      handleMenuClose();
    }
  };

  const handleDelete = async () => {
    setProcessing(true);
    try {
      await onBulkDelete(selectedUsers);
      setDeleteDialog(false);
      onClearSelection();
    } catch (error) {
      console.error('Error deleting users:', error);
    } finally {
      setProcessing(false);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'administrador':
        return <AdminIcon fontSize="small" />;
      case 'psicologo':
        return <PsychologyIcon fontSize="small" />;
      case 'paciente':
        return <PersonIcon fontSize="small" />;
      default:
        return <PersonIcon fontSize="small" />;
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'administrador':
        return 'Administrador';
      case 'psicologo':
        return 'Psicólogo';
      case 'paciente':
        return 'Paciente';
      default:
        return role;
    }
  };

  if (selectedUsers.length === 0) {
    return null;
  }

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          p: 2,
          bgcolor: 'primary.light',
          borderRadius: 1,
          mb: 2,
        }}
      >
        <Chip
          label={`${selectedUsers.length} usuario${selectedUsers.length > 1 ? 's' : ''} seleccionado${selectedUsers.length > 1 ? 's' : ''}`}
          color="primary"
          variant="outlined"
        />

        <Button
          variant="contained"
          startIcon={<MoreVertIcon />}
          onClick={handleMenuOpen}
          disabled={processing}
        >
          Acciones
        </Button>

        <Button
          variant="outlined"
          startIcon={<CloseIcon />}
          onClick={onClearSelection}
          size="small"
        >
          Limpiar Selección
        </Button>
      </Box>

      {/* Menú de Acciones */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: { minWidth: 200 }
        }}
      >
        <MenuItem onClick={() => { setRoleChangeDialog(true); handleMenuClose(); }}>
          <ListItemIcon>
            <AdminIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Cambiar Rol</ListItemText>
        </MenuItem>

        <Divider />

        <MenuItem onClick={() => handleStatusChange(true)}>
          <ListItemIcon>
            <PersonAddIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Activar Usuarios</ListItemText>
        </MenuItem>

        <MenuItem onClick={() => handleStatusChange(false)}>
          <ListItemIcon>
            <PersonOffIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Desactivar Usuarios</ListItemText>
        </MenuItem>

        <Divider />

        <MenuItem 
          onClick={() => { setDeleteDialog(true); handleMenuClose(); }}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Eliminar Usuarios</ListItemText>
        </MenuItem>
      </Menu>

      {/* Dialog para Cambio de Rol */}
      <Dialog open={roleChangeDialog} onClose={() => setRoleChangeDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Cambiar Rol de Usuarios</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            Se cambiará el rol de {selectedUsers.length} usuario{selectedUsers.length > 1 ? 's' : ''} seleccionado{selectedUsers.length > 1 ? 's' : ''}.
          </Alert>

          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Nuevo Rol</InputLabel>
            <Select
              value={selectedRole}
              label="Nuevo Rol"
              onChange={(e) => setSelectedRole(e.target.value)}
            >
              <MenuItem value="administrador">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {getRoleIcon('administrador')}
                  {getRoleLabel('administrador')}
                </Box>
              </MenuItem>
              <MenuItem value="psicologo">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {getRoleIcon('psicologo')}
                  {getRoleLabel('psicologo')}
                </Box>
              </MenuItem>
              <MenuItem value="paciente">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {getRoleIcon('paciente')}
                  {getRoleLabel('paciente')}
                </Box>
              </MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRoleChangeDialog(false)}>
            Cancelar
          </Button>
          <Button
            onClick={handleRoleChange}
            variant="contained"
            disabled={!selectedRole || processing}
            startIcon={processing ? undefined : <CheckIcon />}
          >
            {processing ? 'Cambiando...' : 'Cambiar Rol'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para Confirmación de Eliminación */}
      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Confirmar Eliminación</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="body2">
              ¿Estás seguro de que deseas eliminar {selectedUsers.length} usuario{selectedUsers.length > 1 ? 's' : ''}?
            </Typography>
            <Typography variant="body2" sx={{ mt: 1, fontWeight: 'bold' }}>
              Esta acción no se puede deshacer.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog(false)}>
            Cancelar
          </Button>
          <Button
            onClick={handleDelete}
            variant="contained"
            color="error"
            disabled={processing}
            startIcon={processing ? undefined : <DeleteIcon />}
          >
            {processing ? 'Eliminando...' : 'Eliminar'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
