import React from 'react';
import {
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart,
} from 'recharts';
import { Box, Typography, Paper } from '@mui/material';
import { useTheme } from '@mui/material/styles';

interface LineChartData {
  name: string;
  value: number;
  date?: string;
  category?: string;
}

interface LineChartProps {
  data: LineChartData[];
  title?: string;
  subtitle?: string;
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  showArea?: boolean;
  strokeWidth?: number;
  colorScheme?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  showDots?: boolean;
  curved?: boolean;
}

export const LineChart: React.FC<LineChartProps> = ({
  data,
  title,
  subtitle,
  height = 300,
  showGrid = true,
  showLegend = false,
  showArea = false,
  strokeWidth = 3,
  colorScheme = 'primary',
  showDots = true,
  curved = true,
}) => {
  const theme = useTheme();

  // Colores según el esquema de la guía de diseño
  const getColor = () => {
    switch (colorScheme) {
      case 'primary':
        return '#5A92C8';
      case 'secondary':
        return '#63B4A9';
      case 'success':
        return '#63B4A9';
      case 'warning':
        return '#DDA15E';
      case 'error':
        return '#F28A7C';
      default:
        return '#5A92C8';
    }
  };

  const color = getColor();

  // Configuración del tooltip personalizado
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Paper
          sx={{
            p: 2,
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(0, 0, 0, 0.06)',
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
          }}
        >
          <Typography variant="subtitle2" gutterBottom>
            {label}
          </Typography>
          {data.date && (
            <Typography variant="caption" color="text.secondary" display="block">
              Fecha: {data.date}
            </Typography>
          )}
          {payload.map((entry: any, index: number) => (
            <Typography
              key={index}
              variant="body2"
              sx={{ color: entry.color, fontWeight: 500 }}
            >
              Valor: {entry.value}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  const ChartComponent = showArea ? AreaChart : RechartsLineChart;

  return (
    <Box>
      {(title || subtitle) && (
        <Box mb={2}>
          {title && (
            <Typography variant="h6" component="h3" gutterBottom>
              {title}
            </Typography>
          )}
          {subtitle && (
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
      )}
      
      <ResponsiveContainer width="100%" height={height}>
        <ChartComponent
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke="rgba(0, 0, 0, 0.06)"
            />
          )}
          
          <XAxis 
            dataKey="name"
            tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
            axisLine={{ stroke: 'rgba(0, 0, 0, 0.1)' }}
            tickLine={{ stroke: 'rgba(0, 0, 0, 0.1)' }}
          />
          
          <YAxis 
            tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
            axisLine={{ stroke: 'rgba(0, 0, 0, 0.1)' }}
            tickLine={{ stroke: 'rgba(0, 0, 0, 0.1)' }}
          />
          
          <Tooltip content={<CustomTooltip />} />
          
          {showLegend && <Legend />}
          
          {showArea ? (
            <Area
              type={curved ? "monotone" : "linear"}
              dataKey="value"
              stroke={color}
              fill={color}
              fillOpacity={0.3}
              strokeWidth={strokeWidth}
              dot={showDots ? {
                r: 4,
                fill: color,
                stroke: '#ffffff',
                strokeWidth: 2,
              } : false}
            />
          ) : (
            <Line
              type={curved ? "monotone" : "linear"}
              dataKey="value"
              stroke={color}
              strokeWidth={strokeWidth}
              dot={showDots ? {
                r: 4,
                fill: color,
                stroke: '#ffffff',
                strokeWidth: 2,
              } : false}
              activeDot={{
                r: 6,
                fill: color,
                stroke: '#ffffff',
                strokeWidth: 2,
              }}
            />
          )}
        </ChartComponent>
      </ResponsiveContainer>
      
      {/* Información adicional */}
      <Box mt={1}>
        <Typography variant="caption" color="text.secondary" display="block" textAlign="center">
          Evolución temporal - Seguimiento del progreso a lo largo del tiempo
        </Typography>
      </Box>
    </Box>
  );
};

export default LineChart;
