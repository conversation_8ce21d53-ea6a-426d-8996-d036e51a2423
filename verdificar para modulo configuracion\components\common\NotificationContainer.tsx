import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ert, AlertColor } from '@mui/material';
import { useNotifications, useUIStore } from '../../stores/useUIStore';

/**
 * Contenedor global de notificaciones que muestra todas las notificaciones
 * del store de UI usando Snackbars apilados
 */
const NotificationContainer: React.FC = () => {
  const notifications = useNotifications();
  const { removeNotification } = useUIStore();

  // Mapear tipos de notificación a severidad de Material-UI
  const getSeverity = (type: string): AlertColor => {
    switch (type) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return 'info';
    }
  };

  return (
    <>
      {notifications.map((notification, index) => (
        <Snackbar
          key={notification.id}
          open={true}
          autoHideDuration={notification.duration || 5000}
          onClose={() => removeNotification(notification.id)}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          sx={{
            // Apilar notificaciones verticalmente
            top: `${80 + index * 70}px !important`,
            zIndex: 9999 - index, // Asegurar orden correcto
          }}
        >
          <Alert
            onClose={() => removeNotification(notification.id)}
            severity={getSeverity(notification.type)}
            variant="filled"
            sx={{
              minWidth: '300px',
              boxShadow: 3,
            }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </>
  );
};

export default NotificationContainer;
