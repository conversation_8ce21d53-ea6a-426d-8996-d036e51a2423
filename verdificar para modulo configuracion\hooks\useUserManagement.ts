import { useState, useCallback, useEffect } from 'react';
import {
  userManagementService,
  type UserProfile,
  type UserRole,
  type SystemStats,
  type UserPermissions,
} from '../services/userManagement';
import { useNotifier } from './useNotifier';
import { useModuleCache } from './useModuleCache';

interface UseUserManagementReturn {
  // Estado
  users: UserProfile[];
  stats: SystemStats | null;
  permissions: UserPermissions | null;
  loading: boolean;
  error: string | null;

  // Acciones
  fetchUsers: () => Promise<void>;
  fetchStats: () => Promise<void>;
  fetchPermissions: () => Promise<void>;
  createUser: (userData: {
    email: string;
    password: string;
    full_name: string;
    role: UserRole;
  }) => Promise<boolean>;
  updateUserRole: (userId: string, newRole: UserRole) => Promise<boolean>;
  updateUserStatus: (userId: string, isActive: boolean) => Promise<boolean>;
  deleteUser: (userId: string) => Promise<boolean>;
  refreshData: () => Promise<void>;
  clearError: () => void;
}

/**
 * Hook personalizado para gestión de usuarios
 * Proporciona funcionalidad completa para administradores
 */
export const useUserManagement = (): UseUserManagementReturn => {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [permissions, setPermissions] = useState<UserPermissions | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { showSuccess, showError } = useNotifier();

  // Cache para usuarios
  const usersCache = useModuleCache(
    'users',
    async () => {
      const { data, error: fetchError } = await userManagementService.getAllUsers();
      if (fetchError) throw new Error(fetchError);
      return data || [];
    },
    { ttl: 5 * 60 * 1000 } // 5 minutos
  );

  // Cache para estadísticas
  const statsCache = useModuleCache(
    'stats',
    async () => {
      const { data, error: fetchError } = await userManagementService.getSystemStats();
      if (fetchError) throw new Error(fetchError);
      return data;
    },
    { ttl: 2 * 60 * 1000 } // 2 minutos
  );

  /**
   * Obtener lista de usuarios (con cache optimizado)
   */
  const fetchUsers = useCallback(async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      const data = await usersCache.fetchData(forceRefresh);
      setUsers(data);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      if (!usersCache.data) { // Solo mostrar error si no hay datos en cache
        showError(`Error inesperado: ${errorMessage}`);
      }
    } finally {
      setLoading(false);
    }
  }, [usersCache, showError]);

  /**
   * Obtener estadísticas del sistema (con cache optimizado)
   */
  const fetchStats = useCallback(async (forceRefresh = false) => {
    try {
      const data = await statsCache.fetchData(forceRefresh);
      setStats(data);
    } catch (err) {
      console.error('Error in fetchStats:', err);
      // Si hay datos en cache, mantenerlos
      if (statsCache.data) {
        setStats(statsCache.data);
      }
    }
  }, [statsCache]);

  /**
   * Obtener permisos del usuario actual
   */
  const fetchPermissions = useCallback(async () => {
    try {
      const { data, error: fetchError } =
        await userManagementService.checkUserPermissions();

      if (fetchError) {
        console.error('Error fetching permissions:', fetchError);
        return;
      }

      setPermissions(data);
    } catch (err) {
      console.error('Error in fetchPermissions:', err);
    }
  }, []);

  /**
   * Crear nuevo usuario
   */
  const createUser = useCallback(
    async (userData: {
      email: string;
      password: string;
      full_name: string;
      role: UserRole;
    }): Promise<boolean> => {
      setLoading(true);
      setError(null);

      try {
        const { success, error: createError } =
          await userManagementService.createUser(userData);

        if (createError) {
          setError(createError);
          showError(`Error al crear usuario: ${createError}`);
          return false;
        }

        if (success) {
          showSuccess('Usuario creado exitosamente');
          // Refrescar la lista de usuarios
          await fetchUsers();
          await fetchStats();
          return true;
        }

        return false;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Error desconocido';
        setError(errorMessage);
        showError(`Error inesperado: ${errorMessage}`);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [showError, showSuccess, fetchUsers, fetchStats],
  );

  /**
   * Actualizar rol de usuario
   */
  const updateUserRole = useCallback(
    async (userId: string, newRole: UserRole): Promise<boolean> => {
      setLoading(true);
      setError(null);

      try {
        const { success, error: updateError } =
          await userManagementService.updateUserRole(userId, newRole);

        if (updateError) {
          setError(updateError);
          showError(`Error al actualizar rol: ${updateError}`);
          return false;
        }

        if (success) {
          showSuccess('Rol actualizado exitosamente');
          // Actualizar la lista de usuarios localmente
          setUsers((prevUsers) =>
            prevUsers.map((user) =>
              user.id === userId ? { ...user, role: newRole } : user,
            ),
          );
          // Refrescar estadísticas
          await fetchStats();
          return true;
        }

        return false;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Error desconocido';
        setError(errorMessage);
        showError(`Error inesperado: ${errorMessage}`);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [showError, showSuccess, fetchStats],
  );

  /**
   * Activar/Desactivar usuario
   */
  const updateUserStatus = useCallback(
    async (userId: string, isActive: boolean): Promise<boolean> => {
      setLoading(true);
      setError(null);

      try {
        const { success, error: updateError } =
          await userManagementService.updateUserStatus(userId, isActive);

        if (updateError) {
          setError(updateError);
          showError(`Error al actualizar estado: ${updateError}`);
          return false;
        }

        if (success) {
          showSuccess(
            `Usuario ${isActive ? 'activado' : 'desactivado'} exitosamente`,
          );
          // Actualizar la lista de usuarios localmente
          setUsers((prevUsers) =>
            prevUsers.map((user) =>
              user.id === userId ? { ...user, is_active: isActive } : user,
            ),
          );
          // Refrescar estadísticas
          await fetchStats();
          return true;
        }

        return false;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Error desconocido';
        setError(errorMessage);
        showError(`Error inesperado: ${errorMessage}`);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [showError, showSuccess, fetchStats],
  );

  /**
   * Eliminar usuario
   */
  const deleteUser = useCallback(
    async (userId: string): Promise<boolean> => {
      setLoading(true);
      setError(null);

      try {
        const { success, error: deleteError } =
          await userManagementService.deleteUser(userId);

        if (deleteError) {
          setError(deleteError);
          showError(`Error al eliminar usuario: ${deleteError}`);
          return false;
        }

        if (success) {
          showSuccess('Usuario eliminado exitosamente');
          // Actualizar la lista de usuarios localmente
          setUsers((prevUsers) =>
            prevUsers.filter((user) => user.id !== userId),
          );
          // Refrescar estadísticas
          await fetchStats();
          return true;
        }

        return false;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Error desconocido';
        setError(errorMessage);
        showError(`Error inesperado: ${errorMessage}`);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [showError, showSuccess, fetchStats],
  );

  /**
   * Refrescar todos los datos (con opción de forzar)
   */
  const refreshData = useCallback(async (forceRefresh = false) => {
    await Promise.all([
      fetchUsers(forceRefresh),
      fetchStats(forceRefresh),
      fetchPermissions()
    ]);
  }, [fetchUsers, fetchStats, fetchPermissions]);

  /**
   * Limpiar error y cache
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Invalidar cache
   */
  const invalidateCache = useCallback(() => {
    usersCache.invalidateCache();
    statsCache.invalidateCache();
  }, [usersCache, statsCache]);

  /**
   * Cargar datos iniciales (optimizado con cache)
   */
  useEffect(() => {
    // Solo cargar si no hay datos en cache o si están obsoletos
    const loadInitialData = async () => {
      const usersCacheInfo = usersCache.getCacheInfo();
      const statsCacheInfo = statsCache.getCacheInfo();

      // Si hay datos en cache y no están obsoletos, usarlos inmediatamente
      if (usersCacheInfo?.hasData && !usersCacheInfo.isStale) {
        setUsers(usersCache.data || []);
      }

      if (statsCacheInfo?.hasData && !statsCacheInfo.isStale) {
        setStats(statsCache.data);
      }

      // Cargar datos (cache se encargará de decidir si fetch o no)
      await refreshData();
    };

    loadInitialData();
  }, []); // Solo ejecutar una vez al montar

  return {
    // Estado
    users,
    stats,
    permissions,
    loading: loading || usersCache.loading || statsCache.loading,
    error: error || usersCache.error || statsCache.error,

    // Acciones
    fetchUsers,
    fetchStats,
    fetchPermissions,
    createUser,
    updateUserRole,
    updateUserStatus,
    deleteUser,
    refreshData,
    clearError,
    invalidateCache,

    // Cache info para optimizaciones
    cacheInfo: {
      users: usersCache.getCacheInfo(),
      stats: statsCache.getCacheInfo(),
    },
  };
};

/**
 * Hook simplificado para verificar permisos
 */
export const usePermissions = () => {
  const [permissions, setPermissions] = useState<UserPermissions | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkPermissions = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('🔍 Verificando permisos de usuario...');
        const result = await userManagementService.checkUserPermissions();

        if (result.error) {
          console.error('❌ Error en permisos:', result.error);
          setError(result.error);
          setPermissions(null);
        } else {
          console.log('✅ Permisos obtenidos:', result.data);
          setPermissions(result.data);
          setError(null);
        }
      } catch (err) {
        console.error('❌ Excepción en checkPermissions:', err);
        const errorMessage = err instanceof Error ? err.message : 'Error al verificar permisos';
        setError(errorMessage);
        setPermissions(null);
      } finally {
        setLoading(false);
      }
    };

    checkPermissions();
  }, []);

  return {
    permissions,
    loading,
    error,
    isAdmin: permissions?.role === 'administrador',
    isPsychologist: permissions?.role === 'psicologo',
    isPatient: permissions?.role === 'paciente',
    canManageUsers: permissions?.permissions?.can_manage_users || false,
    canViewAllPatients:
      permissions?.permissions?.can_view_all_patients || false,
    canCreatePatients: permissions?.permissions?.can_create_patients || false,
    canAccessAdminPanel:
      permissions?.permissions?.can_access_admin_panel || false,
  };
};
