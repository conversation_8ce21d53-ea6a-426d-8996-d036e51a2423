import React, { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Box, CircularProgress, Typography, Alert } from '@mui/material';
import { useAuth } from '../../hooks/useAuth';
import { RoutePermissions, ROUTES } from '../../config/routes';
import { UserRole } from '../../services/supabaseAuth';

// Función helper para obtener etiquetas de roles
const getRoleLabel = (role: UserRole): string => {
  const roleLabels: Record<UserRole, string> = {
    administrador: 'Administrador',
    psicologo: 'Psicólogo',
    paciente: 'Paciente',
  };
  return roleLabels[role] || role;
};

interface RouteGuardProps {
  children: ReactNode;
  permissions?: RoutePermissions;
  allowedRoles?: UserRole[];
}

/**
 * Componente que controla el acceso a rutas basándose en roles de usuario
 */
export const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  permissions,
  allowedRoles: allowedRolesProp,
}) => {
  const { user, isAuthenticated, isLoading, initialized } = useAuth();
  const location = useLocation();

  // Determinar los permisos a usar (permissions object o allowedRoles prop)
  let allowedRoles: UserRole[];
  let requireAuth = true;
  let redirectTo = ROUTES.LOGIN;

  if (permissions) {
    // Usar el objeto permissions completo
    allowedRoles = permissions.allowedRoles;
    requireAuth = permissions.requireAuth ?? true;
    redirectTo = permissions.redirectTo ?? ROUTES.LOGIN;
  } else if (allowedRolesProp) {
    // Usar solo allowedRoles como prop directa (compatibilidad con App.jsx)
    allowedRoles = allowedRolesProp;
  } else {
    // Error: no se proporcionaron permisos
    console.error('RouteGuard: neither permissions nor allowedRoles prop provided');
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <Typography variant="h6" gutterBottom>
            Error de Configuración
          </Typography>
          <Typography variant="body1">
            Los permisos de ruta no están configurados correctamente.
          </Typography>
        </Alert>
      </Box>
    );
  }

  // Mostrar loading mientras se inicializa la autenticación
  if (!initialized || isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          backgroundColor: '#f5f5f5',
        }}
      >
        <CircularProgress size={60} sx={{ mb: 2, color: '#1976d2' }} />
        <Typography variant="h6" sx={{ color: '#666' }}>
          Verificando permisos...
        </Typography>
      </Box>
    );
  }

  // Si requiere autenticación y no está autenticado
  if (requireAuth && !isAuthenticated) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // Si no requiere autenticación y está autenticado, redirigir al dashboard
  if (!requireAuth && isAuthenticated) {
    return <Navigate to={ROUTES.DASHBOARD} replace />;
  }

  // Si está autenticado, verificar permisos de rol
  if (isAuthenticated && user) {
    // TEMPORAL: Si no hay rol pero el usuario está autenticado, permitir acceso temporalmente
    // Esto es para resolver el problema de la función RPC get_user_profile
    if (!user.role) {
      console.warn(
        '⚠️ TEMPORAL: Usuario sin rol detectado, permitiendo acceso temporal',
      );
      // Permitir acceso temporal mientras se resuelve el problema de la función RPC
      return <>{children}</>;
    }

    const hasPermission = allowedRoles.includes(user.role);

    if (!hasPermission) {
      return (
        <Box sx={{ p: 3 }}>
          <Alert severity="error">
            <Typography variant="h6" gutterBottom>
              Acceso Denegado
            </Typography>
            <Typography variant="body1">
              No tienes permisos para acceder a esta página.
            </Typography>
            <Typography variant="body2" sx={{ mt: 1 }}>
              Tu rol actual: <strong>{getRoleLabel(user.role)}</strong>
            </Typography>
            <Typography variant="body2">
              Roles permitidos:{' '}
              <strong>{allowedRoles.map(getRoleLabel).join(', ')}</strong>
            </Typography>
          </Alert>
        </Box>
      );
    }
  }

  return <>{children}</>;
};
