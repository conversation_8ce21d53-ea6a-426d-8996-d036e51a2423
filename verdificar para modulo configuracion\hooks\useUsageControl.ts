import { useState, useEffect, useCallback } from 'react';
import { appUsageService } from '../services/appUsageService';
import { useAuth } from './useAuth';

interface UsageInfo {
  app_id: string;
  app_name: string;
  remaining_uses: number;
  total_assigned: number;
  last_used_at?: string;
}

interface UseUsageControlReturn {
  usageInfo: UsageInfo[];
  totalRemaining: number;
  totalAssigned: number;
  totalUsed: number;
  loading: boolean;
  hasUsesForApp: (appId: string) => boolean;
  canPerformAction: (appId: string) => boolean;
  refreshUsage: () => Promise<void>;
  getUsageForApp: (appId: string) => UsageInfo | null;
  isBlocked: boolean;
}

/**
 * Hook para controlar y verificar el uso de aplicaciones
 * Proporciona información sobre usos disponibles y bloqueos
 */
export const useUsageControl = (): UseUsageControlReturn => {
  const { user } = useAuth();
  const [usageInfo, setUsageInfo] = useState<UsageInfo[]>([]);
  const [loading, setLoading] = useState(true);

  const loadUsageInfo = useCallback(async () => {
    if (!user?.id) {
      setUsageInfo([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const result = await appUsageService.getUserUsage(user.id);
      
      if (result.success && result.usages) {
        setUsageInfo(result.usages);
      } else {
        setUsageInfo([]);
      }
    } catch (error) {
      console.error('Error loading usage info:', error);
      setUsageInfo([]);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    loadUsageInfo();
    
    // Suscribirse a cambios en tiempo real
    const subscription = appUsageService.subscribeToUsageChanges(async () => {
      await loadUsageInfo();
    });

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [loadUsageInfo]);

  // Calcular totales
  const totalRemaining = usageInfo.reduce((sum, usage) => sum + usage.remaining_uses, 0);
  const totalAssigned = usageInfo.reduce((sum, usage) => sum + usage.total_assigned, 0);
  const totalUsed = totalAssigned - totalRemaining;

  // Verificar si tiene usos para una aplicación específica
  const hasUsesForApp = useCallback((appId: string): boolean => {
    const appUsage = usageInfo.find(usage => usage.app_id === appId);
    return appUsage ? appUsage.remaining_uses > 0 : false;
  }, [usageInfo]);

  // Verificar si puede realizar una acción (considerando rol y usos)
  const canPerformAction = useCallback((appId: string): boolean => {
    // Los administradores siempre pueden realizar acciones
    if (user?.rol === 'administrador') {
      return true;
    }

    // Los psicólogos necesitan tener usos disponibles
    if (user?.rol === 'psicologo') {
      return hasUsesForApp(appId);
    }

    // Otros roles no pueden realizar acciones
    return false;
  }, [user?.rol, hasUsesForApp]);

  // Obtener información de uso para una aplicación específica
  const getUsageForApp = useCallback((appId: string): UsageInfo | null => {
    return usageInfo.find(usage => usage.app_id === appId) || null;
  }, [usageInfo]);

  // Determinar si el usuario está completamente bloqueado
  const isBlocked = user?.rol === 'psicologo' && totalRemaining === 0;

  return {
    usageInfo,
    totalRemaining,
    totalAssigned,
    totalUsed,
    loading,
    hasUsesForApp,
    canPerformAction,
    refreshUsage: loadUsageInfo,
    getUsageForApp,
    isBlocked
  };
};

export default useUsageControl;
