import React from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';

interface DatabaseSetupProps {
  onClose: () => void;
}

const DatabaseSetup: React.FC<DatabaseSetupProps> = ({ onClose }) => {
  return (
    <Paper elevation={3} sx={{ p: 4, mt: 4, textAlign: 'center' }}>
      <Typography variant="h5" component="h2" gutterBottom>
        Configuración de la Base de Datos Requerida
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Parece que hay un problema con la conexión o la configuración de la base
        de datos. Por favor, asegúrate de que tu base de datos Supabase esté
        correctamente configurada y las tablas necesarias existan.
      </Typography>
      <Button variant="contained" onClick={onClose}>
        Entendido
      </Button>
    </Paper>
  );
};

export default DatabaseSetup;
