import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  CircularProgress,
  Alert,
  Chip,
  Divider,
  TextField,
  Button,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Menu,
  MenuItem,
  Snackbar,
  LinearProgress,
} from '@mui/material';
import {
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  Save as SaveIcon,
  Assessment as AssessmentIcon,
  PictureAsPdf as PdfIcon,
  Download as DownloadIcon,
  Share as ShareIcon,
  Preview as PreviewIcon,
  ArrowDropDown as ArrowDropDownIcon,
} from '@mui/icons-material';
import {
  Bar<PERSON><PERSON>,
  RadarChart,
  PolarAngleAxis,
  PolarGrid,
  Radar,
  Legend,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  LineC<PERSON>,
  Line,
  CartesianGrid,
} from 'recharts';
import { supabase } from '../../config/supabase';
import { useNotifier } from '../../hooks/useNotifier';
import DetailedScoresTable from './DetailedScoresTable';
import PDFHistoryPanel from './PDFHistoryPanel';
import { PDFService } from '../../services/pdfService';
import { allScaleNames, scaleGroups, getScoreInterpretation, percentileInterpretation } from '../../data/maciScalesShared';

// Define la estructura del score que esperamos del backend
interface EvaluationScore {
  raw_scores: { [key: string]: number };
  percentile_scores: { [key: string]: number };
  clinical_alerts: Array<{
    type: string;
    scale: string;
    scale_name: string;
    percentile: number;
    message: string;
    action_required: boolean;
    priority: string;
  }>;
  calculation_metadata: {
    patient_gender: string;
    patient_age: number;
    calculated_at: string;
    version: string;
    total_scales_calculated: number;
  };
}

interface EvaluationData {
  id: string;
  paciente_id: string;
  evaluador_id: string;
  tipo_evaluacion: string;
  estado: string;
  completed_at: string;
  score: EvaluationScore;
  psychologist_notes: string;
  patients: {
    name: string;
    email: string;
  };
}



// Usar las funciones del archivo maestro compartido para consistencia
const { getColorForPercentile, getInterpretationText } = percentileInterpretation;

const EvaluationReportDashboard: React.FC = () => {
  const { evaluationId } = useParams<{ evaluationId: string }>();
  const [loading, setLoading] = useState(true);
  const [evaluationData, setEvaluationData] = useState<EvaluationData | null>(null);
  const [notes, setNotes] = useState('');
  const [saving, setSaving] = useState(false);
  const [generatingPdf, setGeneratingPdf] = useState(false);
  const [pdfMenuAnchor, setPdfMenuAnchor] = useState<null | HTMLElement>(null);
  const [pdfProgress, setPdfProgress] = useState(0);
  const [showPdfProgress, setShowPdfProgress] = useState(false);
  const { showSuccess, showError } = useNotifier();

  useEffect(() => {
    const fetchEvaluation = async () => {
      if (!evaluationId) return;
      setLoading(true);
      try {
        const { data, error } = await supabase
          .from('evaluaciones')
          .select(`
            *,
            patients:paciente_id ( name, email )
          `)
          .eq('id', evaluationId)
          .single();

        if (error) throw error;

        setEvaluationData(data);
        setNotes(data.psychologist_notes || '');
      } catch (err) {
        showError('No se pudo cargar la evaluación.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchEvaluation();
  }, [evaluationId, showError]);

  const handleSaveNotes = async () => {
    setSaving(true);
    try {
      const { error } = await supabase
        .from('evaluaciones')
        .update({ psychologist_notes: notes })
        .eq('id', evaluationId);
      
      if (error) throw error;
      showSuccess('Notas guardadas correctamente.');
    } catch (err) {
      showError('Error al guardar las notas.');
      console.error(err);
    } finally {
      setSaving(false);
    }
  };

  const handleGeneratePDF = async (downloadImmediately = true) => {
    if (!evaluationId) return;

    setGeneratingPdf(true);
    setShowPdfProgress(true);
    setPdfProgress(0);

    try {
      // Simular progreso
      const progressInterval = setInterval(() => {
        setPdfProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const result = await PDFService.generateEvaluationPDF(evaluationId);

      clearInterval(progressInterval);
      setPdfProgress(100);

      if (result.success && result.pdfPath && result.fileName) {
        showSuccess(`PDF generado exitosamente: ${result.fileName}`);

        if (downloadImmediately) {
          // Descargar el PDF automáticamente
          const downloadSuccess = await PDFService.downloadPDF(result.pdfPath, result.fileName);

          if (!downloadSuccess) {
            showError('PDF generado pero error al descargar');
          } else {
            showSuccess('PDF descargado exitosamente');
          }
        }
      } else {
        showError(result.error || 'Error al generar el PDF');
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      showError('Error inesperado al generar el PDF');
    } finally {
      setGeneratingPdf(false);
      setTimeout(() => {
        setShowPdfProgress(false);
        setPdfProgress(0);
      }, 2000);
    }
  };

  const handlePdfMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setPdfMenuAnchor(event.currentTarget);
  };

  const handlePdfMenuClose = () => {
    setPdfMenuAnchor(null);
  };

  const handlePdfOption = async (option: 'download' | 'generate-only' | 'preview') => {
    handlePdfMenuClose();

    switch (option) {
      case 'download':
        await handleGeneratePDF(true);
        break;
      case 'generate-only':
        await handleGeneratePDF(false);
        break;
      case 'preview':
        showError('Vista previa no disponible aún');
        break;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>Cargando informe de evaluación...</Typography>
      </Box>
    );
  }

  if (!evaluationData) {
    return (
      <Alert severity="error" sx={{ m: 3 }}>
        No se encontraron datos para esta evaluación.
      </Alert>
    );
  }

  const score: EvaluationScore = evaluationData.score;

  if (!score) {
    return (
      <Alert severity="warning" sx={{ m: 3 }}>
        Esta evaluación aún no tiene puntuaciones calculadas. 
        Por favor, complete el cuestionario para generar el informe.
      </Alert>
    );
  }

  // Preparar datos para los gráficos usando funciones del archivo maestro
  const personalityData = [...scaleGroups.personality, ...scaleGroups.concerns]
    .map(key => ({
      subject: allScaleNames[key] || key,
      PC: score.percentile_scores[key] || 0,
      fullMark: 100,
      color: getColorForPercentile(score.percentile_scores[key] || 0),
    }));

  const clinicalData = scaleGroups.syndromes
    .map(key => ({
      name: allScaleNames[key] || key,
      PC: score.percentile_scores[key] || 0,
      fill: getColorForPercentile(score.percentile_scores[key] || 0),
      interpretation: getInterpretationText(score.percentile_scores[key] || 0),
    }));

  const criticalAlerts = score.clinical_alerts || [];

  return (
    <Box sx={{ p: 3, maxWidth: '1400px', mx: 'auto' }}>
      {/* Header */}
      <Paper elevation={3} sx={{ p: 3, mb: 3, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <AssessmentIcon sx={{ fontSize: 40, mr: 2 }} />
          <Box>
            <Typography variant="h4" gutterBottom sx={{ mb: 0 }}>
              Informe de Evaluación MACI-II
            </Typography>
            <Typography variant="h6">
              Paciente: {evaluationData.patients?.name || 'No disponible'}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              Completado el: {new Date(evaluationData.completed_at).toLocaleDateString('es-ES', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* Alertas Críticas */}
      {criticalAlerts.length > 0 && (
        <Alert 
          severity="error" 
          sx={{ mb: 3, fontSize: '1.1rem' }}
          icon={<ErrorIcon fontSize="large" />}
        >
          <Typography variant="h6" gutterBottom>
            ⚠️ ALERTAS CRÍTICAS DETECTADAS
          </Typography>
          {criticalAlerts.map((alert, index) => (
            <Box key={index} sx={{ mt: 1 }}>
              <Typography variant="body1">
                <strong>{alert.scale_name}</strong> (PC: {alert.percentile}): {alert.message}
              </Typography>
            </Box>
          ))}
        </Alert>
      )}

      {/* Metadatos de Cálculo */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            📊 Información del Cálculo
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Chip 
                label={`Género: ${score.calculation_metadata?.patient_gender || 'No especificado'}`}
                variant="outlined"
                color="primary"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Chip 
                label={`Edad: ${score.calculation_metadata?.patient_age || 'No especificada'} años`}
                variant="outlined"
                color="primary"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Chip 
                label={`Escalas: ${score.calculation_metadata?.total_scales_calculated || 0}`}
                variant="outlined"
                color="secondary"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Chip 
                label={score.calculation_metadata?.version || 'MACI-II v1.0'}
                variant="outlined"
                color="success"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Gráficos de Visualización */}
      <Grid container spacing={3}>
        {/* Patrones de Personalidad - Radar Chart */}
        <Grid item xs={12} lg={6}>
          <Paper elevation={2} sx={{ p: 3, height: 500 }}>
            <Typography variant="h6" align="center" gutterBottom>
              🧠 Patrones de Personalidad (Percentiles)
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <RadarChart data={personalityData}>
                <PolarGrid />
                <PolarAngleAxis dataKey="subject" tick={{ fontSize: 10 }} />
                <Radar 
                  name="Puntaje Centil" 
                  dataKey="PC" 
                  stroke="#8884d8" 
                  fill="#8884d8" 
                  fillOpacity={0.3}
                  strokeWidth={2}
                />
                <Tooltip 
                  formatter={(value: any) => [`${value}`, 'Percentil']}
                  labelFormatter={(label) => `Escala: ${label}`}
                />
              </RadarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Síndromes Clínicos - Bar Chart */}
        <Grid item xs={12} lg={6}>
          <Paper elevation={2} sx={{ p: 3, height: 500 }}>
            <Typography variant="h6" align="center" gutterBottom>
              🏥 Síndromes Clínicos (Percentiles)
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <BarChart data={clinicalData} layout="vertical" margin={{ left: 100 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" domain={[0, 100]} />
                <YAxis type="category" dataKey="name" width={90} tick={{ fontSize: 10 }} />
                <Tooltip 
                  formatter={(value: any, name: any, props: any) => [
                    `${value} (${props.payload.interpretation})`, 
                    'Percentil'
                  ]}
                />
                <Bar dataKey="PC" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Tabla detallada con todas las escalas */}
      <Paper elevation={2} sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
          📊 Puntuaciones Detalladas
        </Typography>
        <DetailedScoresTable score={score} />
      </Paper>

      {/* Notas del Psicólogo */}
      <Paper elevation={2} sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
          📝 Interpretación y Notas del Psicólogo
        </Typography>
        <TextField
          fullWidth
          multiline
          rows={8}
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          placeholder="Añada aquí su interpretación clínica, observaciones y plan de acción..."
          variant="outlined"
          sx={{ mb: 2 }}
        />
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={generatingPdf ? <CircularProgress size={20} /> : <PdfIcon />}
            onClick={() => handleGeneratePDF()}
            disabled={generatingPdf}
            size="large"
            color="secondary"
            endIcon={<ArrowDropDownIcon />}
            onContextMenu={handlePdfMenuClick}
          >
            {generatingPdf ? 'Generando PDF...' : 'Exportar a PDF'}
          </Button>

          <Menu
            anchorEl={pdfMenuAnchor}
            open={Boolean(pdfMenuAnchor)}
            onClose={handlePdfMenuClose}
          >
            <MenuItem onClick={() => handlePdfOption('download')}>
              <DownloadIcon sx={{ mr: 1 }} />
              Generar y Descargar
            </MenuItem>
            <MenuItem onClick={() => handlePdfOption('generate-only')}>
              <PdfIcon sx={{ mr: 1 }} />
              Solo Generar
            </MenuItem>
            <MenuItem onClick={() => handlePdfOption('preview')}>
              <PreviewIcon sx={{ mr: 1 }} />
              Vista Previa
            </MenuItem>
          </Menu>
          <Button
            variant="contained"
            startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
            onClick={handleSaveNotes}
            disabled={saving}
            size="large"
          >
            {saving ? 'Guardando...' : 'Guardar Notas'}
          </Button>
        </Box>

        {/* Indicador de Progreso PDF */}
        <Snackbar
          open={showPdfProgress}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert severity="info" sx={{ width: '100%', alignItems: 'center' }}>
            <Box sx={{ width: '100%' }}>
              <Typography variant="body2" gutterBottom>
                Generando PDF... {pdfProgress}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={pdfProgress}
                sx={{ mt: 1 }}
              />
            </Box>
          </Alert>
        </Snackbar>

        {/* Panel de Historial de PDFs */}
        <Grid item xs={12}>
          <PDFHistoryPanel evaluationId={evaluationId || ''} />
        </Grid>
      </Paper>
    </Box>
  );
};

export default EvaluationReportDashboard;
