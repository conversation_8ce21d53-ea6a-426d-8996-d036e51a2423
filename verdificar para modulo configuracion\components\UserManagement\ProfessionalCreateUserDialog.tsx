import React, { useState } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  IconButton,
  InputAdornment,
  Alert,
  Chip,
  Divider,
  Grid,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  ContentCopy,
  Refresh,
  Person as PersonIcon,
  Email as EmailIcon,
  Lock as LockIcon,
  AdminPanelSettings as AdminIcon,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { motion } from 'framer-motion';
import { UserRole } from '../../services/userManagement';
import { useNotifier } from '../../hooks/useNotifier';
import { AnimatedDialog } from '../Common/AnimatedDialog';

interface CreateUserFormData {
  full_name: string;
  email: string;
  role: UserRole;
  password: string;
  document_id?: string;
  phone?: string;
}

interface ProfessionalCreateUserDialogProps {
  open: boolean;
  onClose: () => void;
  onUserCreated: (userData: CreateUserFormData) => Promise<void>;
}

const generateSecurePassword = (): string => {
  const length = 12;
  const charset =
    'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
};

const roleOptions = [
  {
    value: 'administrador',
    label: 'Administrador',
    color: '#f44336',
    icon: <AdminIcon />,
  },
  {
    value: 'psicologo',
    label: 'Psicólogo',
    color: '#2196f3',
    icon: <PersonIcon />,
  },
  {
    value: 'paciente',
    label: 'Paciente',
    color: '#4caf50',
    icon: <PersonIcon />,
  },
];

export const ProfessionalCreateUserDialog: React.FC<
  ProfessionalCreateUserDialogProps
> = ({ open, onClose, onUserCreated }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { showNotification } = useNotifier();

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<CreateUserFormData>({
    defaultValues: {
      full_name: '',
      email: '',
      role: 'paciente',
      password: '',
      document_id: '',
      phone: '',
    },
    mode: 'onChange',
  });

  const watchedPassword = watch('password');
  const watchedRole = watch('role');

  const handleGeneratePassword = () => {
    const newPassword = generateSecurePassword();
    setValue('password', newPassword);
    showNotification('Contraseña generada automáticamente', 'info');
  };

  const handleCopyPassword = async () => {
    try {
      await navigator.clipboard.writeText(watchedPassword);
      showNotification('Contraseña copiada al portapapeles', 'success');
    } catch (error) {
      showNotification('Error al copiar la contraseña', 'error');
    }
  };

  const onSubmit = async (data: CreateUserFormData) => {
    try {
      setLoading(true);
      setError(null);
      await onUserCreated(data);
      showNotification('Usuario creado exitosamente', 'success');
      reset();
      onClose();
    } catch (error: any) {
      setError(error.message || 'Error al crear el usuario');
      showNotification('Error al crear el usuario', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    setError(null);
    onClose();
  };

  const getPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
  };

  const passwordStrength = getPasswordStrength(watchedPassword);
  const strengthColors = [
    '#f44336',
    '#ff9800',
    '#ffeb3b',
    '#8bc34a',
    '#4caf50',
  ];
  const strengthLabels = [
    'Muy débil',
    'Débil',
    'Regular',
    'Buena',
    'Excelente',
  ];

  return (
    <AnimatedDialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      animationType="motion"
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <PersonIcon sx={{ color: '#011129' }} />
          <Typography variant="h6" sx={{ fontWeight: 600, color: '#011129' }}>
            Crear Nuevo Usuario
          </Typography>
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Complete la información para crear una nueva cuenta de usuario
        </Typography>
      </DialogTitle>

      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent sx={{ pt: 2 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <Grid container spacing={3}>
            {/* Información Personal */}
            <Grid item xs={12}>
              <Typography
                variant="subtitle1"
                sx={{ fontWeight: 600, mb: 2, color: '#011129' }}
              >
                Información Personal
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="full_name"
                control={control}
                rules={{ required: 'El nombre completo es requerido' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Nombre Completo"
                    error={!!errors.full_name}
                    helperText={errors.full_name?.message}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonIcon color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="document_id"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Documento de Identidad"
                    placeholder="Opcional"
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="email"
                control={control}
                rules={{
                  required: 'El email es requerido',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Email inválido',
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Correo Electrónico"
                    type="email"
                    error={!!errors.email}
                    helperText={errors.email?.message}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <EmailIcon color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="phone"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Teléfono"
                    placeholder="Opcional"
                  />
                )}
              />
            </Grid>

            {/* Rol y Acceso */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography
                variant="subtitle1"
                sx={{ fontWeight: 600, mb: 2, color: '#011129' }}
              >
                Rol y Acceso
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="role"
                control={control}
                rules={{ required: 'El rol es requerido' }}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.role}>
                    <InputLabel>Rol del Usuario</InputLabel>
                    <Select {...field} label="Rol del Usuario">
                      {roleOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                            }}
                          >
                            {option.icon}
                            <span>{option.label}</span>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Chip
                  label={
                    roleOptions.find((r) => r.value === watchedRole)?.label
                  }
                  sx={{
                    backgroundColor: roleOptions.find(
                      (r) => r.value === watchedRole,
                    )?.color,
                    color: 'white',
                    fontWeight: 600,
                  }}
                />
              </Box>
            </Grid>

            {/* Contraseña */}
            <Grid item xs={12}>
              <Controller
                name="password"
                control={control}
                rules={{
                  required: 'La contraseña es requerida',
                  minLength: { value: 8, message: 'Mínimo 8 caracteres' },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Contraseña"
                    type={showPassword ? 'text' : 'password'}
                    error={!!errors.password}
                    helperText={errors.password?.message}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LockIcon color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                          <IconButton
                            onClick={handleCopyPassword}
                            disabled={!watchedPassword}
                          >
                            <ContentCopy />
                          </IconButton>
                          <IconButton onClick={handleGeneratePassword}>
                            <Refresh />
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                )}
              />
            </Grid>

            {watchedPassword && (
              <Grid item xs={12}>
                <Box sx={{ mt: 1 }}>
                  <Typography variant="caption" color="text.secondary">
                    Fortaleza de la contraseña:{' '}
                    {strengthLabels[passwordStrength - 1] || 'Muy débil'}
                  </Typography>
                  <Box
                    sx={{
                      width: '100%',
                      height: 4,
                      backgroundColor: '#e0e0e0',
                      borderRadius: 2,
                      mt: 0.5,
                    }}
                  >
                    <Box
                      sx={{
                        width: `${(passwordStrength / 5) * 100}%`,
                        height: '100%',
                        backgroundColor:
                          strengthColors[passwordStrength - 1] || '#f44336',
                        borderRadius: 2,
                        transition: 'all 0.3s ease',
                      }}
                    />
                  </Box>
                </Box>
              </Grid>
            )}
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button onClick={handleClose} disabled={loading}>
            Cancelar
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={!isValid || loading}
            sx={{
              backgroundColor: '#011129',
              '&:hover': { backgroundColor: '#022247' },
            }}
          >
            {loading ? 'Creando...' : 'Crear Usuario'}
          </Button>
        </DialogActions>
      </form>
    </AnimatedDialog>
  );
};
