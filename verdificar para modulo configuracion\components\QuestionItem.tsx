import React, { memo } from 'react';
import { Question } from '../types/questions';
import {
  Box,
  Radio,
  FormControlLabel,
  RadioGroup,
  Paper,
  Typography,
  useTheme,
  useMediaQuery,
  Fade,
} from '@mui/material';

interface QuestionItemProps {
  question: Question;
  onAnswer: (questionId: number, value: boolean | null) => void;
  answer: boolean | null;
}

const QuestionItem = memo(
  ({ question, onAnswer, answer }: QuestionItemProps) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value === 'true';
      onAnswer(question.id, value);
    };

    const handleKeyPress = (event: React.KeyboardEvent) => {
      if (event.key === '1' || event.key === 'v' || event.key === 'V') {
        onAnswer(question.id, true);
      } else if (event.key === '0' || event.key === 'f' || event.key === 'F') {
        onAnswer(question.id, false);
      }
    };

    return (
      <Fade in={true} timeout={500}>
        <Paper
          elevation={2}
          onKeyPress={handleKeyPress}
          tabIndex={0}
          sx={{
            p: isMobile ? 2 : 3,
            height: 'auto',
            minHeight: isMobile ? '180px' : '200px',
            maxHeight: '250px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            backgroundColor: answer !== null ? '#f8f9fa' : 'white',
            border: '1px solid',
            borderColor: answer !== null ? '#d1e7dd' : '#dee2e6',
            transition: 'all 0.3s ease-in-out',
            '&:hover': {
              borderColor: answer !== null ? '#d1e7dd' : '#90caf9',
              boxShadow: '0 4px 8px rgba(0,0,0,0.15)',
              transform: 'translateY(-2px)',
            },
            '&:focus': {
              outline: 'none',
              borderColor: theme.palette.primary.main,
              boxShadow: `0 0 0 2px ${theme.palette.primary.main}25`,
            },
          }}
        >
          <Box sx={{ flex: 1, overflow: 'auto', mb: 2 }}>
            <Typography
              variant="body1"
              sx={{
                lineHeight: 1.6,
                fontSize: isMobile ? '0.9rem' : '1rem',
                '&::-webkit-scrollbar': {
                  width: '6px',
                  height: '6px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: '#88888850',
                  borderRadius: '3px',
                  '&:hover': {
                    backgroundColor: '#88888880',
                  },
                },
              }}
            >
              {question.text}
            </Typography>
          </Box>

          <RadioGroup
            row
            value={answer === null ? '' : String(answer)}
            onChange={handleChange}
            sx={{
              justifyContent: 'flex-start',
              gap: isMobile ? 1 : 2,
            }}
          >
            <FormControlLabel
              value="true"
              control={<Radio size={isMobile ? 'small' : 'medium'} />}
              label="Verdadero"
              sx={{
                '& .MuiFormControlLabel-label': {
                  fontSize: isMobile ? '0.875rem' : '1rem',
                },
              }}
            />
            <FormControlLabel
              value="false"
              control={<Radio size={isMobile ? 'small' : 'medium'} />}
              label="Falso"
              sx={{
                '& .MuiFormControlLabel-label': {
                  fontSize: isMobile ? '0.875rem' : '1rem',
                },
              }}
            />
          </RadioGroup>
        </Paper>
      </Fade>
    );
  },
);

export default QuestionItem;
