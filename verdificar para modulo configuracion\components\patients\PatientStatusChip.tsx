import React from 'react';
import { Chip } from '@mui/material';
import { 
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  HourglassEmpty as HourglassEmptyIcon 
} from '@mui/icons-material';
import { PatientStatus } from '../../types/patient';

interface PatientStatusChipProps {
  status: PatientStatus;
  size?: 'small' | 'medium';
}

const statusConfig = {
  activo: {
    label: 'Activo',
    color: 'success' as const,
    icon: <CheckCircleIcon />,
  },
  inactivo: {
    label: 'Inactivo',
    color: 'default' as const,
    icon: <CancelIcon />,
  },
  pendiente_evaluacion: {
    label: 'Pendiente',
    color: 'warning' as const,
    icon: <HourglassEmptyIcon />,
  },
};

export const PatientStatusChip: React.FC<PatientStatusChipProps> = ({ 
  status, 
  size = 'small' 
}) => {
  const config = statusConfig[status];

  return (
    <Chip
      label={config.label}
      color={config.color}
      size={size}
      icon={config.icon}
      variant="outlined"
    />
  );
};
