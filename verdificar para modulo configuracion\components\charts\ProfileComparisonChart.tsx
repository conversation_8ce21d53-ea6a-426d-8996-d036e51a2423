import React from 'react';
import {
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  ResponsiveContainer,
  Tooltip,
  Legend,
} from 'recharts';
import { Box, Typography, Chip } from '@mui/material';

// Estilo para ocultar visualmente pero mantener accesible
const visuallyHiddenStyle = {
  border: 0,
  clip: 'rect(0 0 0 0)',
  height: '1px',
  margin: '-1px',
  overflow: 'hidden',
  padding: 0,
  position: 'absolute' as const,
  width: '1px',
};

interface ProfileData {
  scale: string;
  scaleName: string;
  evaluation1?: number;
  evaluation2?: number;
  fullMark: number;
}

interface ProfileComparisonChartProps {
  data?: ProfileData[];
  height?: number;
  evaluation1Label?: string;
  evaluation2Label?: string;
  showComparison?: boolean;
}

// Datos de ejemplo para comparación
const sampleComparisonData: ProfileData[] = [
  { scale: '1', scaleName: 'Introvertido', evaluation1: 65, evaluation2: 45, fullMark: 100 },
  { scale: '2A', scaleName: 'Inhibido', evaluation1: 45, evaluation2: 55, fullMark: 100 },
  { scale: '3', scaleName: 'Sumiso', evaluation1: 55, evaluation2: 40, fullMark: 100 },
  { scale: '9', scaleName: 'Tendencia Límite', evaluation1: 75, evaluation2: 60, fullMark: 100 },
  { scale: 'DD', scaleName: 'Impulsividad', evaluation1: 80, evaluation2: 70, fullMark: 100 },
  { scale: 'EE', scaleName: 'Ansiedad', evaluation1: 70, evaluation2: 50, fullMark: 100 },
  { scale: 'FF', scaleName: 'Depresión', evaluation1: 60, evaluation2: 45, fullMark: 100 },
  { scale: 'GG', scaleName: 'Suicidio', evaluation1: 40, evaluation2: 25, fullMark: 100 },
];

const ProfileComparisonChart: React.FC<ProfileComparisonChartProps> = ({
  data = sampleComparisonData,
  height = 400,
  evaluation1Label = "Evaluación Inicial",
  evaluation2Label = "Evaluación Actual",
  showComparison = true
}) => {
  const chartId = `profile-comparison-chart-${Math.random().toString(36).substr(2, 9)}`;
  const descriptionId = `${chartId}-desc`;
  const tableId = `${chartId}-table`;
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div style={{
          backgroundColor: '#fff',
          border: '1px solid #5A92C8',
          borderRadius: '8px',
          padding: '12px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          maxWidth: '220px',
        }}>
          <p style={{ margin: 0, fontWeight: 'bold', color: '#333', marginBottom: '8px' }}>
            {data.scaleName}
          </p>
          {data.evaluation1 !== undefined && (
            <p style={{ margin: 0, color: '#5A92C8', fontSize: '14px' }}>
              {evaluation1Label}: {data.evaluation1}
            </p>
          )}
          {data.evaluation2 !== undefined && (
            <p style={{ margin: 0, color: '#63B4A9', fontSize: '14px' }}>
              {evaluation2Label}: {data.evaluation2}
            </p>
          )}
          {data.evaluation1 !== undefined && data.evaluation2 !== undefined && (
            <p style={{ 
              margin: '4px 0 0 0', 
              color: data.evaluation1 > data.evaluation2 ? '#d32f2f' : '#388e3c',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>
              Cambio: {data.evaluation2 - data.evaluation1 > 0 ? '+' : ''}{data.evaluation2 - data.evaluation1}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  const getChangeIndicator = () => {
    if (!showComparison || !data.length) return null;
    
    const improvements = data.filter(item => 
      item.evaluation1 !== undefined && 
      item.evaluation2 !== undefined && 
      item.evaluation2 < item.evaluation1
    ).length;
    
    const deteriorations = data.filter(item => 
      item.evaluation1 !== undefined && 
      item.evaluation2 !== undefined && 
      item.evaluation2 > item.evaluation1
    ).length;

    return (
      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', mb: 2 }}>
        <Chip 
          label={`${improvements} mejoras`} 
          color="success" 
          size="small" 
          variant="outlined"
        />
        <Chip 
          label={`${deteriorations} aumentos`} 
          color="warning" 
          size="small" 
          variant="outlined"
        />
      </Box>
    );
  };

  return (
    <Box>
      {/* Descripción accesible */}
      <Typography id={descriptionId} sx={visuallyHiddenStyle}>
        Gráfico de comparación temporal que muestra la evolución del perfil psicológico
        entre {evaluation1Label} y {evaluation2Label}. Se comparan {data.length} escalas
        diferentes. Los valores van de 0 a 100, donde puntuaciones superiores a 75 se
        consideran elevadas.
      </Typography>

      {getChangeIndicator()}

      <ResponsiveContainer width="100%" height={height}>
        <RadarChart
          data={data}
          margin={{ top: 20, right: 30, bottom: 20, left: 30 }}
          aria-label={`Comparación temporal entre ${evaluation1Label} y ${evaluation2Label}`}
          aria-describedby={descriptionId}
          role="img"
        >
          <PolarGrid
            stroke="#e0e0e0"
            strokeWidth={1}
          />
          <PolarAngleAxis
            dataKey="scaleName"
            tick={{ fontSize: 11, fill: '#666' }}
          />
          <PolarRadiusAxis
            angle={90}
            domain={[0, 100]}
            tick={{ fontSize: 10, fill: '#999' }}
            tickCount={6}
          />

          {/* Primera evaluación */}
          <Radar
            name={evaluation1Label}
            dataKey="evaluation1"
            stroke="#5A92C8"
            fill="#5A92C8"
            fillOpacity={0.2}
            strokeWidth={2}
            dot={{ fill: '#5A92C8', strokeWidth: 2, r: 4 }}
          />

          {/* Segunda evaluación (si existe) */}
          {showComparison && (
            <Radar
              name={evaluation2Label}
              dataKey="evaluation2"
              stroke="#63B4A9"
              fill="#63B4A9"
              fillOpacity={0.2}
              strokeWidth={2}
              dot={{ fill: '#63B4A9', strokeWidth: 2, r: 4 }}
            />
          )}

          <Tooltip content={<CustomTooltip />} />
          <Legend
            verticalAlign="bottom"
            height={36}
            iconType="circle"
          />
        </RadarChart>
      </ResponsiveContainer>

      {/* Tabla de datos alternativa para accesibilidad */}
      <Box component="table" sx={visuallyHiddenStyle} id={tableId}>
        <caption>Comparación temporal de escalas psicológicas</caption>
        <thead>
          <tr>
            <th scope="col">Escala</th>
            <th scope="col">{evaluation1Label}</th>
            <th scope="col">{evaluation2Label}</th>
            <th scope="col">Cambio</th>
            <th scope="col">Tendencia</th>
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => {
            const change = (item.evaluation2 || 0) - (item.evaluation1 || 0);
            const trend = change > 0 ? 'Aumento' : change < 0 ? 'Disminución' : 'Sin cambio';
            return (
              <tr key={`${item.scale}-${index}`}>
                <td>{item.scaleName}</td>
                <td>{item.evaluation1 || 'N/A'}</td>
                <td>{item.evaluation2 || 'N/A'}</td>
                <td>{change > 0 ? '+' : ''}{change}</td>
                <td>{trend}</td>
              </tr>
            );
          })}
        </tbody>
      </Box>
    </Box>
  );
};

export default ProfileComparisonChart;
