import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  People as PeopleIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  HourglassEmpty as HourglassEmptyIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { patientsService } from '../../services/patients';
import { PatientStatistics as PatientStatsType } from '../../types/patient';
import { useNotification } from '../../hooks/useNotification';

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  description?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, description }) => (
  <Card elevation={2}>
    <CardContent>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography variant="h4" component="div" color={color} fontWeight="bold">
            {value}
          </Typography>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            {title}
          </Typography>
          {description && (
            <Typography variant="body2" color="text.secondary">
              {description}
            </Typography>
          )}
        </Box>
        <Box sx={{ color: color, fontSize: '3rem' }}>
          {icon}
        </Box>
      </Box>
    </CardContent>
  </Card>
);

export const PatientStatistics: React.FC = () => {
  const [statistics, setStatistics] = useState<PatientStatsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { showNotification } = useNotification();

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchStatistics = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await patientsService.getStatistics();
      
      if (response.success) {
        setStatistics(response.data);
      } else {
        setError(response.message);
        showNotification('Error al cargar estadísticas: ' + response.message, 'error');
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
      setError('Error inesperado al cargar estadísticas');
      showNotification('Error inesperado al cargar estadísticas', 'error');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error || !statistics) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error || 'No se pudieron cargar las estadísticas'}
      </Alert>
    );
  }

  return (
    <Box sx={{ mb: 4 }}>
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        Estadísticas de Pacientes
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Total Pacientes"
            value={statistics.total_patients}
            icon={<PeopleIcon />}
            color="primary.main"
            description="Pacientes registrados"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Activos"
            value={statistics.active_patients}
            icon={<CheckCircleIcon />}
            color="success.main"
            description="En tratamiento"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Inactivos"
            value={statistics.inactive_patients}
            icon={<CancelIcon />}
            color="text.secondary"
            description="Sin actividad"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Pendientes"
            value={statistics.pending_evaluation}
            icon={<HourglassEmptyIcon />}
            color="warning.main"
            description="Esperando evaluación"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Evaluaciones Recientes"
            value={statistics.recent_evaluations}
            icon={<TrendingUpIcon />}
            color="info.main"
            description="Últimos 30 días"
          />
        </Grid>
      </Grid>
    </Box>
  );
};
