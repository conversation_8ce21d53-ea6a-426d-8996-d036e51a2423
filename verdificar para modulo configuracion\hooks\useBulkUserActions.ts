import { useState } from 'react';
import { supabase } from '../lib/supabaseClient';
import { userManagementService } from '../services/userManagement';

export const useBulkUserActions = () => {
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const bulkRoleChange = async (userIds: string[], newRole: string) => {
    setProcessing(true);
    setError(null);
    
    try {
      console.log(`🔄 Cambiando rol a ${newRole} para ${userIds.length} usuarios...`);
      
      // Actualizar roles en lote usando RPC
      const { error: rpcError } = await supabase.rpc('bulk_update_user_roles', {
        user_ids: userIds,
        new_role: newRole
      });

      if (rpcError) {
        throw new Error(rpcError.message);
      }

      console.log(`✅ Roles actualizados exitosamente para ${userIds.length} usuarios`);
      return { success: true };
    } catch (err) {
      console.error('❌ Error en bulk role change:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error al cambiar roles';
      setError(errorMessage);
      throw err;
    } finally {
      setProcessing(false);
    }
  };

  const bulkStatusChange = async (userIds: string[], isActive: boolean) => {
    setProcessing(true);
    setError(null);
    
    try {
      console.log(`🔄 ${isActive ? 'Activando' : 'Desactivando'} ${userIds.length} usuarios...`);
      
      // Actualizar estado en lote
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ is_active: isActive })
        .in('id', userIds);

      if (updateError) {
        throw new Error(updateError.message);
      }

      console.log(`✅ Estado actualizado exitosamente para ${userIds.length} usuarios`);
      return { success: true };
    } catch (err) {
      console.error('❌ Error en bulk status change:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error al cambiar estado';
      setError(errorMessage);
      throw err;
    } finally {
      setProcessing(false);
    }
  };

  const bulkDelete = async (userIds: string[]) => {
    setProcessing(true);
    setError(null);
    
    try {
      console.log(`🔄 Eliminando ${userIds.length} usuarios...`);
      
      // Soft delete en lote
      const { error: deleteError } = await supabase
        .from('profiles')
        .update({ 
          is_active: false,
          deleted_at: new Date().toISOString()
        })
        .in('id', userIds);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      console.log(`✅ Usuarios eliminados exitosamente: ${userIds.length}`);
      return { success: true };
    } catch (err) {
      console.error('❌ Error en bulk delete:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error al eliminar usuarios';
      setError(errorMessage);
      throw err;
    } finally {
      setProcessing(false);
    }
  };

  const bulkAssignToRole = async (userIds: string[], targetRole: string, additionalData?: any) => {
    setProcessing(true);
    setError(null);
    
    try {
      console.log(`🔄 Asignando ${userIds.length} usuarios al rol ${targetRole}...`);
      
      // Preparar datos de actualización
      const updateData: any = { rol: targetRole };
      
      // Agregar datos adicionales según el rol
      if (targetRole === 'psicologo' && additionalData?.specialization) {
        updateData.specialization = additionalData.specialization;
      }
      
      if (targetRole === 'paciente' && additionalData?.assignedPsychologist) {
        updateData.assigned_psychologist = additionalData.assignedPsychologist;
      }

      const { error: updateError } = await supabase
        .from('profiles')
        .update(updateData)
        .in('id', userIds);

      if (updateError) {
        throw new Error(updateError.message);
      }

      console.log(`✅ Usuarios asignados exitosamente al rol ${targetRole}`);
      return { success: true };
    } catch (err) {
      console.error('❌ Error en bulk assign to role:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error al asignar rol';
      setError(errorMessage);
      throw err;
    } finally {
      setProcessing(false);
    }
  };

  const bulkPermissionUpdate = async (userIds: string[], permissions: string[]) => {
    setProcessing(true);
    setError(null);
    
    try {
      console.log(`🔄 Actualizando permisos para ${userIds.length} usuarios...`);
      
      // Usar RPC para actualizar permisos en lote
      const { error: rpcError } = await supabase.rpc('bulk_update_user_permissions', {
        user_ids: userIds,
        permission_list: permissions
      });

      if (rpcError) {
        throw new Error(rpcError.message);
      }

      console.log(`✅ Permisos actualizados exitosamente para ${userIds.length} usuarios`);
      return { success: true };
    } catch (err) {
      console.error('❌ Error en bulk permission update:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error al actualizar permisos';
      setError(errorMessage);
      throw err;
    } finally {
      setProcessing(false);
    }
  };

  const getAuditLog = async (userIds: string[], action: string) => {
    try {
      // Registrar acción en el log de auditoría
      const auditEntries = userIds.map(userId => ({
        user_id: userId,
        action,
        timestamp: new Date().toISOString(),
        performed_by: 'current_user', // Se puede obtener del contexto de auth
      }));

      const { error: auditError } = await supabase
        .from('audit_log')
        .insert(auditEntries);

      if (auditError) {
        console.warn('Warning: Could not log audit entry:', auditError);
      }
    } catch (err) {
      console.warn('Warning: Audit logging failed:', err);
    }
  };

  return {
    processing,
    error,
    bulkRoleChange,
    bulkStatusChange,
    bulkDelete,
    bulkAssignToRole,
    bulkPermissionUpdate,
    getAuditLog,
  };
};
