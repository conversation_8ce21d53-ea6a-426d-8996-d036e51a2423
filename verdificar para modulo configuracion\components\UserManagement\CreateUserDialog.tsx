import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  IconButton,
  InputAdornment,
  LinearProgress,
  Chip,
  Divider,
  Card,
  CardContent,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  AutoFixHigh as AutoFixHighIcon,
  PersonAdd as PersonAddIcon,
  Email as EmailIcon,
  Lock as LockIcon,
  Badge as BadgeIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import {
  adminUserCreationService,
  userCreationUtils,
  type CreateUserRequest,
  type UserRole,
} from '../../services/adminUserCreation';
import { useNotifier } from '../../hooks/useNotifier';

interface CreateUserDialogProps {
  open: boolean;
  onClose: () => void;
  onUserCreated: () => void;
}

const CreateUserDialog: React.FC<CreateUserDialogProps> = ({
  open,
  onClose,
  onUserCreated,
}) => {
  const [formData, setFormData] = useState<CreateUserRequest>({
    email: '',
    password: '',
    role: 'paciente',
    fullName: '',
  });

  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [emailChecking, setEmailChecking] = useState(false);
  const [emailAvailable, setEmailAvailable] = useState<boolean | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [passwordStrength, setPasswordStrength] = useState({
    score: 0,
    message: '',
  });

  const { showSuccess, showError } = useNotifier();

  // Limpiar formulario al abrir/cerrar
  useEffect(() => {
    if (open) {
      setFormData({
        email: '',
        password: '',
        role: 'paciente',
        fullName: '',
      });
      setErrors({});
      setEmailAvailable(null);
      setPasswordStrength({ score: 0, message: '' });
    }
  }, [open]);

  // Verificar disponibilidad de email con debounce
  useEffect(() => {
    if (
      !formData.email ||
      !adminUserCreationService.validateEmailFormat(formData.email)
    ) {
      setEmailAvailable(null);
      return;
    }

    const timeoutId = setTimeout(async () => {
      setEmailChecking(true);
      try {
        const { available, reason } =
          await adminUserCreationService.checkEmailAvailability(formData.email);
        setEmailAvailable(available);
        if (!available) {
          setErrors((prev) => ({ ...prev, email: reason }));
        } else {
          setErrors((prev) => {
            const newErrors = { ...prev };
            delete newErrors.email;
            return newErrors;
          });
        }
      } catch (error) {
        console.error('Error checking email:', error);
      } finally {
        setEmailChecking(false);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [formData.email]);

  // Evaluar fortaleza de contraseña
  useEffect(() => {
    if (formData.password) {
      const strength = adminUserCreationService.validatePasswordStrength(
        formData.password,
      );
      setPasswordStrength(strength);
    } else {
      setPasswordStrength({ score: 0, message: '' });
    }
  }, [formData.password]);

  const handleInputChange =
    (field: keyof CreateUserRequest) =>
    (
      event:
        | React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
        | { target: { value: unknown } },
    ) => {
      const value = event.target.value as string;
      setFormData((prev) => ({ ...prev, [field]: value }));

      // Limpiar error del campo cuando el usuario empiece a escribir
      if (errors[field]) {
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[field];
          return newErrors;
        });
      }

      // Auto-generar nombre sugerido basado en email
      if (field === 'email' && value && !formData.fullName) {
        const suggestedName = userCreationUtils.generateSuggestedName(value);
        if (suggestedName) {
          setFormData((prev) => ({ ...prev, fullName: suggestedName }));
        }
      }
    };

  const handleGeneratePassword = async () => {
    try {
      const { password, error } =
        await adminUserCreationService.generateSecurePassword(12);
      if (error) {
        showError(`Error al generar contraseña: ${error}`);
        return;
      }
      if (password) {
        setFormData((prev) => ({ ...prev, password }));
        showSuccess('Contraseña segura generada');
      }
    } catch (error) {
      showError('Error inesperado al generar contraseña');
    }
  };

  const handleSubmit = async () => {
    // Validar formulario
    const validation = userCreationUtils.validateUserForm(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    if (!emailAvailable) {
      showError('El email no está disponible o no ha sido verificado');
      return;
    }

    setLoading(true);
    try {
      const { success, data, error } =
        await adminUserCreationService.createUser(formData);

      if (error) {
        showError(`Error al crear usuario: ${error}`);
        return;
      }

      if (success && data) {
        showSuccess(
          `Usuario ${data.email} creado correctamente con rol ${userCreationUtils.formatRole(data.role as UserRole)}`,
        );
        onUserCreated();
        onClose();
      }
    } catch (error) {
      showError('Error inesperado al crear usuario');
      console.error('Error creating user:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPasswordStrengthColor = (score: number) => {
    switch (score) {
      case 1:
        return 'error';
      case 2:
        return 'warning';
      case 3:
        return 'info';
      case 4:
        return 'success';
      default:
        return 'inherit';
    }
  };

  const roles = userCreationUtils.getAllRoles();

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 },
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center">
          <PersonAddIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6">Crear Nuevo Usuario</Typography>
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Registra un nuevo usuario en el sistema con el rol apropiado
        </Typography>
      </DialogTitle>

      <DialogContent dividers>
        <Box
          component="form"
          sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}
        >
          {/* Email */}
          <TextField
            label="Email"
            type="email"
            value={formData.email}
            onChange={handleInputChange('email')}
            error={!!errors.email}
            helperText={errors.email || 'Email único para el usuario'}
            required
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <EmailIcon color="action" />
                </InputAdornment>
              ),
              endAdornment: emailChecking ? (
                <InputAdornment position="end">
                  <LinearProgress sx={{ width: 20 }} />
                </InputAdornment>
              ) : emailAvailable === true ? (
                <Chip label="Disponible" color="success" size="small" />
              ) : emailAvailable === false ? (
                <Chip label="No disponible" color="error" size="small" />
              ) : null,
            }}
          />

          {/* Nombre completo */}
          <TextField
            label="Nombre Completo"
            value={formData.fullName}
            onChange={handleInputChange('fullName')}
            error={!!errors.fullName}
            helperText={
              errors.fullName || 'Nombre completo del usuario (opcional)'
            }
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <PersonIcon color="action" />
                </InputAdornment>
              ),
            }}
          />

          {/* Contraseña */}
          <Box>
            <TextField
              label="Contraseña"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange('password')}
              error={!!errors.password}
              helperText={errors.password}
              required
              fullWidth
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockIcon color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            {/* Indicador de fortaleza de contraseña */}
            {formData.password && (
              <Box sx={{ mt: 1 }}>
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                >
                  <Typography
                    variant="caption"
                    color={getPasswordStrengthColor(passwordStrength.score)}
                  >
                    {passwordStrength.message}
                  </Typography>
                  <Button
                    size="small"
                    startIcon={<AutoFixHighIcon />}
                    onClick={handleGeneratePassword}
                    variant="outlined"
                  >
                    Generar
                  </Button>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={(passwordStrength.score / 4) * 100}
                  color={getPasswordStrengthColor(passwordStrength.score)}
                  sx={{ mt: 0.5 }}
                />
              </Box>
            )}
          </Box>

          {/* Rol */}
          <FormControl fullWidth required>
            <InputLabel>Rol del Usuario</InputLabel>
            <Select
              value={formData.role}
              onChange={handleInputChange('role')}
              label="Rol del Usuario"
              startAdornment={
                <InputAdornment position="start">
                  <BadgeIcon color="action" />
                </InputAdornment>
              }
            >
              {roles.map((role) => (
                <MenuItem key={role.value} value={role.value}>
                  <Box>
                    <Typography variant="body1">{role.label}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {role.description}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Información del rol seleccionado */}
          {formData.role && (
            <Card variant="outlined">
              <CardContent sx={{ py: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Permisos del rol:{' '}
                  {userCreationUtils.formatRole(formData.role)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {roles.find((r) => r.value === formData.role)?.description}
                </Typography>
              </CardContent>
            </Card>
          )}

          {/* Alertas */}
          {Object.keys(errors).length > 0 && (
            <Alert severity="error">
              Por favor, corrige los errores antes de continuar.
            </Alert>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={onClose} disabled={loading}>
          Cancelar
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={
            loading || !emailAvailable || Object.keys(errors).length > 0
          }
          startIcon={loading ? null : <PersonAddIcon />}
        >
          {loading ? 'Creando...' : 'Crear Usuario'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CreateUserDialog;
