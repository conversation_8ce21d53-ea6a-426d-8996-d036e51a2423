import React, { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useAuth } from '../../hooks/useAuth';
import { ROUTES } from '../../config/routes';

interface PublicRouteProps {
  children: ReactNode;
  redirectTo?: string;
}

/**
 * Componente para rutas públicas que previene el acceso a usuarios autenticados
 * Redirige a usuarios autenticados al dashboard
 */
export const PublicRoute: React.FC<PublicRouteProps> = ({
  children,
  redirectTo = ROUTES.DASHBOARD,
}) => {
  const { isAuthenticated, isLoading, initialized } = useAuth();

  // Mostrar loading mientras se inicializa la autenticación
  if (!initialized || isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          backgroundColor: '#f5f5f5',
        }}
      >
        <CircularProgress size={60} sx={{ mb: 2, color: '#1976d2' }} />
        <Typography variant="h6" sx={{ color: '#666' }}>
          Verificando autenticación...
        </Typography>
      </Box>
    );
  }

  // Si está autenticado, redirigir al dashboard
  if (isAuthenticated) {
    return <Navigate to={redirectTo} replace />;
  }

  // Si no está autenticado, mostrar el contenido público
  return <>{children}</>;
};
