import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Autocomplete,
  TextField,
  Alert,
  AlertTitle,
  Chip,
  CircularProgress,
  Divider,
  Avatar,
  InputAdornment,
} from '@mui/material';
import {
  Person as PersonIcon,
  Psychology as PsychologyIcon,
  Assignment as AssignmentIcon,
  Group as GroupIcon,
  PersonAdd as PersonAddIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { supabase } from '../../lib/supabaseClient';
import { useNotifier } from '../../hooks/useNotifier';
import { ModuleWrapper } from '../Admin/ModuleFallback';

interface Patient {
  id: string;
  name: string;
  email?: string;
  psicologo_id?: string;
  created_at: string;
}

interface Psychologist {
  id: string;
  email: string;
  nombres_apellidos?: string;
}

interface AssignmentStats {
  total_patients: number;
  assigned_patients: number;
  unassigned_patients: number;
  total_psychologists: number;
}

const PatientAssignmentPanel: React.FC = () => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [psychologists, setPsychologists] = useState<Psychologist[]>([]);
  const [stats, setStats] = useState<AssignmentStats>({
    total_patients: 0,
    assigned_patients: 0,
    unassigned_patients: 0,
    total_psychologists: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [selectedPatients, setSelectedPatients] = useState<string[]>([]);
  const [selectedPsychologist, setSelectedPsychologist] = useState<Psychologist | null>(null);
  const [assigning, setAssigning] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const { showSuccess, showError } = useNotifier();

  // Cargar datos iniciales
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('🔍 Cargando datos de asignación de pacientes...');
      await Promise.all([
        loadPatients(),
        loadPsychologists(),
        loadStats(),
      ]);
      console.log('✅ Datos de asignación cargados correctamente');
    } catch (err) {
      console.error('❌ Error loading data:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error al cargar los datos';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const loadPatients = async () => {
    const { data, error } = await supabase
      .from('patients')
      .select('id, name, email, psicologo_id, created_at')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error loading patients:', error);
      throw error;
    }

    setPatients(data || []);
  };

  const loadPsychologists = async () => {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, email, nombres_apellidos')
      .eq('rol', 'psicologo')
      .order('nombres_apellidos');

    if (error) {
      console.error('Error loading psychologists:', error);
      throw error;
    }

    setPsychologists(data || []);
  };

  const loadStats = async () => {
    const { data: patientsData, error: patientsError } = await supabase
      .from('patients')
      .select('id, psicologo_id');

    const { data: psychologistsData, error: psychologistsError } = await supabase
      .from('profiles')
      .select('id')
      .eq('rol', 'psicologo');

    if (patientsError || psychologistsError) {
      throw new Error('Error loading statistics');
    }

    const totalPatients = patientsData?.length || 0;
    const assignedPatients = patientsData?.filter(p => p.psicologo_id).length || 0;
    const unassignedPatients = totalPatients - assignedPatients;
    const totalPsychologists = psychologistsData?.length || 0;

    setStats({
      total_patients: totalPatients,
      assigned_patients: assignedPatients,
      unassigned_patients: unassignedPatients,
      total_psychologists: totalPsychologists,
    });
  };

  const handleAssignPatients = async () => {
    if (!selectedPsychologist || selectedPatients.length === 0) {
      showError('Selecciona un psicólogo y al menos un paciente');
      return;
    }

    setAssigning(true);
    try {
      // Usar la nueva función automatizada que maneja tanto asignación como evaluaciones
      const { data: results, error } = await supabase.rpc('asignar_pacientes_automatizado', {
        p_psicologo_id: selectedPsychologist.id,
        p_pacientes_ids: selectedPatients,
        p_tipo_cuestionario: 'MACI-II',
      });

      if (error) {
        throw error;
      }

      // Procesar resultados
      const successful = results?.filter((r: any) => r.asignacion_exitosa) || [];
      const failed = results?.filter((r: any) => !r.asignacion_exitosa) || [];
      const evaluationsCreated = results?.filter((r: any) => r.evaluacion_creada) || [];

      // Mostrar resultados detallados
      if (successful.length > 0) {
        showSuccess(`${successful.length} paciente(s) asignado(s) y ${evaluationsCreated.length} evaluación(es) MACI-II creada(s)`);
      }

      if (failed.length > 0) {
        const failureMessages = failed.map((f: any) => `${f.nombre_completo}: ${f.mensaje}`).slice(0, 3).join(', ');
        showError(`${failed.length} asignación(es) fallida(s): ${failureMessages}${failed.length > 3 ? '...' : ''}`);
      }

      // Recargar datos y cerrar diálogo
      await loadData();
      setAssignDialogOpen(false);
      setSelectedPatients([]);
      setSelectedPsychologist(null);

    } catch (error) {
      console.error('Error assigning patients:', error);
      showError('Error al asignar pacientes: ' + (error as any)?.message || 'Error desconocido');
    } finally {
      setAssigning(false);
    }
  };

  const handleAssignAllUnassigned = async () => {
    const unassignedPatients = patients.filter(p => !p.psicologo_id);
    if (unassignedPatients.length === 0) {
      showError('No hay pacientes sin asignar');
      return;
    }

    setSelectedPatients(unassignedPatients.map(p => p.id));
    setAssignDialogOpen(true);
  };

  // Filtrar pacientes por término de búsqueda (memoizado para optimizar rendimiento)
  const filteredPatients = useMemo(() => {
    return patients.filter(patient => {
      if (!searchTerm) return true;
      const searchLower = searchTerm.toLowerCase();
      return (
        patient.name?.toLowerCase().includes(searchLower) ||
        patient.email?.toLowerCase().includes(searchLower)
      );
    });
  }, [patients, searchTerm]);

  const unassignedPatients = useMemo(() =>
    filteredPatients.filter(p => !p.psicologo_id),
    [filteredPatients]
  );

  const assignedPatients = useMemo(() =>
    filteredPatients.filter(p => p.psicologo_id),
    [filteredPatients]
  );

  return (
    <ModuleWrapper
      loading={loading}
      error={error}
      onRetry={loadData}
    >
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <AssignmentIcon />
        Asignación de Pacientes
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Gestiona la asignación de pacientes a psicólogos del sistema
      </Typography>

      {/* Estadísticas */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <GroupIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">
                {stats.total_patients}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Pacientes
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <PersonIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">
                {stats.assigned_patients}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Asignados
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <PersonAddIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">
                {stats.unassigned_patients}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Sin Asignar
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <PsychologyIcon sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">
                {stats.total_psychologists}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Psicólogos
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Campo de búsqueda */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          label="Buscar pacientes"
          placeholder="Buscar por nombre o email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          variant="outlined"
          sx={{ maxWidth: 400 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: 'text.secondary' }} />
              </InputAdornment>
            )
          }}
        />
      </Box>

      {/* Acciones rápidas */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <Button
          variant="contained"
          startIcon={<AssignmentIcon />}
          onClick={() => setAssignDialogOpen(true)}
          disabled={patients.length === 0 || psychologists.length === 0}
        >
          Asignar Pacientes
        </Button>
        
        <Button
          variant="outlined"
          startIcon={<PersonAddIcon />}
          onClick={handleAssignAllUnassigned}
          disabled={unassignedPatients.length === 0 || psychologists.length === 0}
        >
          Asignar Todos los Sin Asignar ({unassignedPatients.length})
        </Button>
      </Box>

      {/* Lista de pacientes sin asignar */}
      {unassignedPatients.length > 0 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom color="warning.main">
            Pacientes Sin Asignar ({unassignedPatients.length})
          </Typography>
          <List dense>
            {unassignedPatients.slice(0, 5).map((patient) => (
              <ListItem key={patient.id}>
                <ListItemIcon>
                  <Avatar sx={{ width: 32, height: 32 }}>
                    <PersonIcon />
                  </Avatar>
                </ListItemIcon>
                <ListItemText
                  primary={patient.name}
                  secondary={patient.email || 'Sin email'}
                />
              </ListItem>
            ))}
            {unassignedPatients.length > 5 && (
              <ListItem>
                <ListItemText
                  primary={`... y ${unassignedPatients.length - 5} más`}
                  sx={{ fontStyle: 'italic', color: 'text.secondary' }}
                />
              </ListItem>
            )}
          </List>
        </Paper>
      )}

      {/* Diálogo de asignación */}
      <Dialog
        open={assignDialogOpen}
        onClose={() => setAssignDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Asignar Pacientes y Crear Evaluaciones
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Se asignarán los pacientes al psicólogo y se crearán evaluaciones del tipo seleccionado
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Autocomplete
              options={psychologists}
              getOptionLabel={(option) => `${option.nombres_apellidos || option.email} (${option.email})`}
              value={selectedPsychologist}
              onChange={(_, newValue) => setSelectedPsychologist(newValue)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Seleccionar Psicólogo"
                  placeholder="Buscar psicólogo..."
                  fullWidth
                />
              )}
            />
          </Box>

          <Box sx={{ mb: 3 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              <AlertTitle>Cuestionario Asignado</AlertTitle>
              Se creará automáticamente una evaluación MACI-II (Inventario Clínico para Adolescentes de Millon) para cada paciente asignado.
            </Alert>
          </Box>

          <Typography variant="subtitle1" gutterBottom>
            Seleccionar Pacientes:
          </Typography>
          
          <List sx={{ maxHeight: 300, overflow: 'auto' }}>
            {patients.map((patient) => (
              <ListItem key={patient.id} dense>
                <ListItemIcon>
                  <Checkbox
                    checked={selectedPatients.includes(patient.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedPatients([...selectedPatients, patient.id]);
                      } else {
                        setSelectedPatients(selectedPatients.filter(id => id !== patient.id));
                      }
                    }}
                  />
                </ListItemIcon>
                <ListItemText
                  primary={patient.name}
                  secondary={
                    <React.Fragment>
                      <Typography component="span" variant="body2" color="text.secondary">
                        {patient.email || 'Sin email'}
                      </Typography>
                      {patient.psicologo_id && (
                        <Chip
                          label="Ya asignado"
                          size="small"
                          color="info"
                          variant="outlined"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </React.Fragment>
                  }
                  secondaryTypographyProps={{ component: 'div' }}
                />
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignDialogOpen(false)}>
            Cancelar
          </Button>
          <Button
            onClick={handleAssignPatients}
            variant="contained"
            disabled={!selectedPsychologist || selectedPatients.length === 0 || assigning}
            startIcon={assigning ? <CircularProgress size={20} /> : <AssignmentIcon />}
          >
            {assigning ? 'Asignando...' : `Asignar ${selectedPatients.length} Paciente(s)`}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
    </ModuleWrapper>
  );
};

export default PatientAssignmentPanel;
