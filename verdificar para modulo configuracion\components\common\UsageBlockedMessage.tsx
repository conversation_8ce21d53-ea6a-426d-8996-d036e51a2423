import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>ertT<PERSON>le,
  <PERSON>,
  Button,
  Typo<PERSON>,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import {
  Block as BlockIcon,
  ContactSupport as ContactIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

interface UsageBlockedMessageProps {
  appName?: string;
  variant?: 'alert' | 'card' | 'inline';
  showContactInfo?: boolean;
  onRefresh?: () => void;
  className?: string;
}

const UsageBlockedMessage: React.FC<UsageBlockedMessageProps> = ({
  appName = 'esta aplicación',
  variant = 'alert',
  showContactInfo = true,
  onRefresh,
  className
}) => {
  const title = `Sin usos disponibles para ${appName}`;
  const message = `Se han agotado sus usos para ${appName}. Para continuar, por favor, póngase en contacto con el administrador del sistema para solicitar una recarga.`;

  const ContactButton = () => (
    <Button
      startIcon={<ContactIcon />}
      variant="outlined"
      size="small"
      onClick={() => {
        // Aquí podrías abrir un modal de contacto o redirigir a una página de soporte
        window.location.href = 'mailto:<EMAIL>?subject=Solicitud de recarga de usos&body=Hola, necesito una recarga de usos para continuar utilizando el sistema MACI-II.';
      }}
      sx={{ mt: 1 }}
    >
      Contactar Administrador
    </Button>
  );

  const RefreshButton = () => onRefresh ? (
    <Button
      startIcon={<RefreshIcon />}
      variant="text"
      size="small"
      onClick={onRefresh}
      sx={{ mt: 1, ml: 1 }}
    >
      Verificar Usos
    </Button>
  ) : null;

  if (variant === 'card') {
    return (
      <Card className={className} sx={{ maxWidth: 500, mx: 'auto', my: 2 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <BlockIcon color="error" sx={{ mr: 1 }} />
            <Typography variant="h6" color="error">
              {title}
            </Typography>
          </Box>
          
          <Typography variant="body1" color="text.secondary" paragraph>
            {message}
          </Typography>

          <Divider sx={{ my: 2 }} />

          <Typography variant="body2" color="text.secondary" gutterBottom>
            ¿Qué puedes hacer?
          </Typography>
          
          <Typography variant="body2" color="text.secondary" component="ul" sx={{ pl: 2 }}>
            <li>Contacta al administrador para solicitar más usos</li>
            <li>Verifica si tienes usos disponibles actualizando la página</li>
            <li>Revisa informes ya generados en la sección de reportes</li>
          </Typography>

          <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {showContactInfo && <ContactButton />}
            <RefreshButton />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (variant === 'inline') {
    return (
      <Box className={className} sx={{ p: 2, textAlign: 'center' }}>
        <BlockIcon color="error" sx={{ fontSize: 48, mb: 1 }} />
        <Typography variant="h6" color="error" gutterBottom>
          {title}
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          {message}
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap', gap: 1 }}>
          {showContactInfo && <ContactButton />}
          <RefreshButton />
        </Box>
      </Box>
    );
  }

  // Variant 'alert' (default)
  return (
    <Alert 
      severity="error" 
      className={className}
      icon={<BlockIcon />}
      action={
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          {showContactInfo && <ContactButton />}
          <RefreshButton />
        </Box>
      }
    >
      <AlertTitle>{title}</AlertTitle>
      {message}
    </Alert>
  );
};

export default UsageBlockedMessage;
