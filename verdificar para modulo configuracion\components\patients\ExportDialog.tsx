import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
  FormGroup,
  Box,
  Typography,
  IconButton,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material';
import {
  Close as CloseIcon,
  Download as DownloadIcon,
  FileDownload as FileDownloadIcon,
  PictureAsPdf as PdfIcon,
  TableChart as CsvIcon,
} from '@mui/icons-material';
import { Patient, PatientStatistics } from '../../types/patient';
import { exportService } from '../../services/exportService';
import { useNotification } from '../../hooks/useNotification';

interface ExportDialogProps {
  open: boolean;
  onClose: () => void;
  patients: Patient[];
  statistics?: PatientStatistics;
}

type ExportFormat = 'csv' | 'pdf';
type ExportType = 'patients' | 'statistics' | 'both';

interface ExportOptions {
  format: ExportFormat;
  type: ExportType;
  includeFields: {
    name: boolean;
    apellidos: boolean;
    email: boolean;
    documento_identidad: boolean;
    telefono: boolean;
    birthDate: boolean;
    gender: boolean;
    status: boolean;
    last_evaluation_date: boolean;
    created_at: boolean;
  };
}

const defaultOptions: ExportOptions = {
  format: 'csv',
  type: 'patients',
  includeFields: {
    name: true,
    apellidos: true,
    email: true,
    documento_identidad: true,
    telefono: false,
    birthDate: false,
    gender: false,
    status: true,
    last_evaluation_date: false,
    created_at: false,
  },
};

export const ExportDialog: React.FC<ExportDialogProps> = ({
  open,
  onClose,
  patients,
  statistics,
}) => {
  const [options, setOptions] = useState<ExportOptions>(defaultOptions);
  const [loading, setLoading] = useState(false);
  const { showNotification } = useNotification();

  const handleFormatChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setOptions(prev => ({ ...prev, format: event.target.value as ExportFormat }));
  };

  const handleTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setOptions(prev => ({ ...prev, type: event.target.value as ExportType }));
  };

  const handleFieldChange = (field: keyof ExportOptions['includeFields']) => {
    setOptions(prev => ({
      ...prev,
      includeFields: {
        ...prev.includeFields,
        [field]: !prev.includeFields[field],
      },
    }));
  };

  const handleSelectAll = () => {
    const allSelected = Object.values(options.includeFields).every(Boolean);
    const newValue = !allSelected;
    
    setOptions(prev => ({
      ...prev,
      includeFields: Object.keys(prev.includeFields).reduce((acc, key) => ({
        ...acc,
        [key]: newValue,
      }), {} as ExportOptions['includeFields']),
    }));
  };

  const handleExport = async () => {
    setLoading(true);
    try {
      let result;
      
      if (options.type === 'patients') {
        result = await exportService.exportPatients(patients, options.format, options.includeFields);
      } else if (options.type === 'statistics' && statistics) {
        result = await exportService.exportStatistics(statistics, options.format);
      } else if (options.type === 'both' && statistics) {
        result = await exportService.exportComplete(patients, statistics, options.format, options.includeFields);
      }

      if (result?.success) {
        showNotification('Exportación completada exitosamente', 'success');
        onClose();
      } else {
        showNotification('Error en la exportación: ' + (result?.message || 'Error desconocido'), 'error');
      }
    } catch (error) {
      console.error('Export error:', error);
      showNotification('Error inesperado durante la exportación', 'error');
    } finally {
      setLoading(false);
    }
  };

  const getSelectedFieldsCount = () => {
    return Object.values(options.includeFields).filter(Boolean).length;
  };

  const isExportDisabled = () => {
    if (options.type === 'patients' && getSelectedFieldsCount() === 0) return true;
    if (options.type === 'statistics' && !statistics) return true;
    if (options.type === 'both' && (!statistics || getSelectedFieldsCount() === 0)) return true;
    return false;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '16px',
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
        },
      }}
    >
      <DialogTitle
        sx={{
          borderBottom: '1px solid #e9ecef',
          pb: 2,
          backgroundColor: '#f8f9fa',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box
            sx={{
              p: 1.5,
              borderRadius: '10px',
              backgroundColor: '#e3f2fd',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <FileDownloadIcon sx={{ color: '#1976d2', fontSize: '1.5rem' }} />
          </Box>
          <Typography
            variant="h6"
            sx={{
              color: '#2c3e50',
              fontWeight: 600,
              fontSize: '1.3rem',
            }}
          >
            Exportar Datos
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        {/* Formato de exportación */}
        <FormControl component="fieldset" sx={{ mb: 3 }}>
          <FormLabel component="legend" sx={{ fontWeight: 600, color: '#2c3e50' }}>
            Formato de Exportación
          </FormLabel>
          <RadioGroup
            value={options.format}
            onChange={handleFormatChange}
            row
            sx={{ mt: 1 }}
          >
            <FormControlLabel
              value="csv"
              control={<Radio />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CsvIcon sx={{ color: '#4caf50' }} />
                  CSV (Excel)
                </Box>
              }
            />
            <FormControlLabel
              value="pdf"
              control={<Radio />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PdfIcon sx={{ color: '#f44336' }} />
                  PDF
                </Box>
              }
            />
          </RadioGroup>
        </FormControl>

        <Divider sx={{ my: 2 }} />

        {/* Tipo de datos */}
        <FormControl component="fieldset" sx={{ mb: 3 }}>
          <FormLabel component="legend" sx={{ fontWeight: 600, color: '#2c3e50' }}>
            Datos a Exportar
          </FormLabel>
          <RadioGroup
            value={options.type}
            onChange={handleTypeChange}
            sx={{ mt: 1 }}
          >
            <FormControlLabel
              value="patients"
              control={<Radio />}
              label={`Lista de Pacientes (${patients.length} registros)`}
            />
            <FormControlLabel
              value="statistics"
              control={<Radio />}
              label="Estadísticas del Sistema"
              disabled={!statistics}
            />
            <FormControlLabel
              value="both"
              control={<Radio />}
              label="Reporte Completo (Pacientes + Estadísticas)"
              disabled={!statistics}
            />
          </RadioGroup>
        </FormControl>

        {/* Campos a incluir (solo para pacientes) */}
        {(options.type === 'patients' || options.type === 'both') && (
          <>
            <Divider sx={{ my: 2 }} />
            <FormControl component="fieldset" sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <FormLabel component="legend" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                  Campos a Incluir ({getSelectedFieldsCount()} seleccionados)
                </FormLabel>
                <Button
                  size="small"
                  onClick={handleSelectAll}
                  sx={{ textTransform: 'none' }}
                >
                  {Object.values(options.includeFields).every(Boolean) ? 'Deseleccionar Todo' : 'Seleccionar Todo'}
                </Button>
              </Box>
              <FormGroup>
                <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 1 }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={options.includeFields.name}
                        onChange={() => handleFieldChange('name')}
                      />
                    }
                    label="Nombre"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={options.includeFields.apellidos}
                        onChange={() => handleFieldChange('apellidos')}
                      />
                    }
                    label="Apellidos"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={options.includeFields.email}
                        onChange={() => handleFieldChange('email')}
                      />
                    }
                    label="Email"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={options.includeFields.documento_identidad}
                        onChange={() => handleFieldChange('documento_identidad')}
                      />
                    }
                    label="Documento"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={options.includeFields.telefono}
                        onChange={() => handleFieldChange('telefono')}
                      />
                    }
                    label="Teléfono"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={options.includeFields.birthDate}
                        onChange={() => handleFieldChange('birthDate')}
                      />
                    }
                    label="Fecha Nacimiento"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={options.includeFields.gender}
                        onChange={() => handleFieldChange('gender')}
                      />
                    }
                    label="Género"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={options.includeFields.status}
                        onChange={() => handleFieldChange('status')}
                      />
                    }
                    label="Estado"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={options.includeFields.last_evaluation_date}
                        onChange={() => handleFieldChange('last_evaluation_date')}
                      />
                    }
                    label="Última Evaluación"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={options.includeFields.created_at}
                        onChange={() => handleFieldChange('created_at')}
                      />
                    }
                    label="Fecha Registro"
                  />
                </Box>
              </FormGroup>
            </FormControl>
          </>
        )}

        {isExportDisabled() && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            {options.type === 'patients' && getSelectedFieldsCount() === 0
              ? 'Seleccione al menos un campo para exportar'
              : 'No hay datos disponibles para exportar'
            }
          </Alert>
        )}
      </DialogContent>

      <DialogActions
        sx={{
          p: 3,
          backgroundColor: '#f8f9fa',
          borderTop: '1px solid #e9ecef',
          gap: 2,
        }}
      >
        <Button
          onClick={onClose}
          sx={{
            color: '#7f8c8d',
            borderRadius: '10px',
            px: 3,
            py: 1,
            textTransform: 'none',
            fontWeight: 500,
          }}
          disabled={loading}
        >
          Cancelar
        </Button>
        <Button
          onClick={handleExport}
          variant="contained"
          startIcon={loading ? <CircularProgress size={20} /> : <DownloadIcon />}
          sx={{
            backgroundColor: '#3498db',
            borderRadius: '10px',
            px: 4,
            py: 1,
            textTransform: 'none',
            fontWeight: 600,
            '&:hover': {
              backgroundColor: '#2980b9',
            },
          }}
          disabled={loading || isExportDisabled()}
        >
          {loading ? 'Exportando...' : 'Exportar'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
